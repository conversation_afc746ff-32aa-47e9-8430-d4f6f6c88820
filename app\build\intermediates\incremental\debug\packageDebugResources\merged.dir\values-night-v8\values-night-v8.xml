<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="BlueCardStyleDark" parent="Widget.MaterialComponents.CardView">
        <item name="cardBackgroundColor">@color/blue_card_background_dark</item>
        <item name="cardElevation">4dp</item>
        <item name="cardCornerRadius">8dp</item>
        <item name="android:textColor">@color/white</item>
    </style>
    <style name="BlueNegativeButtonStyle.Night">
        <item name="android:textColor">@color/orange_200</item>
    </style>
    <style name="BluePositiveButtonStyle.Night">
        <item name="android:textColor">@color/blue_200</item>
    </style>
    <style name="CrimsonCardStyleDark" parent="Widget.MaterialComponents.CardView">
        <item name="cardBackgroundColor">@color/crimson_card_background_dark</item>
        <item name="cardElevation">4dp</item>
        <item name="cardCornerRadius">8dp</item>
        <item name="android:textColor">@color/white</item>
    </style>
    <style name="CrimsonNegativeButtonStyle.Night">
        <item name="android:textColor">@color/crimson_light</item>
    </style>
    <style name="CrimsonPositiveButtonStyle.Night">
        <item name="android:textColor">@color/crimson_lightest</item>
    </style>
    <style name="CustomDropdownTextInputLayout" parent="Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu">
        <item name="boxStrokeColor">?attr/colorPrimary</item>
        <item name="hintTextColor">?attr/colorPrimary</item>
        <item name="android:textColorHint">@color/white</item>
    </style>
    <style name="CustomDropdownTextInputLayout.Blue">
        <item name="boxStrokeColor">@color/blue_200</item>
        <item name="hintTextColor">@color/blue_200</item>
        <item name="boxBackgroundColor">@color/blue_card_background_dark</item>
        <item name="endIconTint">@color/blue_200</item>
        <item name="android:textColorHint">@color/white</item>
    </style>
    <style name="CustomDropdownTextInputLayout.Crimson">
        <item name="boxStrokeColor">@color/crimson_lightest</item>
        <item name="hintTextColor">@color/crimson_lightest</item>
        <item name="boxBackgroundColor">@color/crimson_card_background_dark</item>
        <item name="endIconTint">@color/crimson_lightest</item>
        <item name="android:textColorHint">@color/white</item>
    </style>
    <style name="CustomDropdownTextInputLayout.Dark">
        <item name="boxStrokeColor">@color/purple_200</item>
        <item name="hintTextColor">@color/purple_200</item>
        <item name="boxBackgroundColor">@color/dark_gray</item>
        <item name="endIconTint">@color/purple_200</item>
        <item name="android:textColorHint">@color/white</item>
    </style>
    <style name="CustomDropdownTextInputLayout.DarkBlue">
        <item name="boxStrokeColor">@color/dark_blue_200</item>
        <item name="hintTextColor">@color/dark_blue_200</item>
        <item name="boxBackgroundColor">@color/dark_blue_card_background_dark</item>
        <item name="endIconTint">@color/dark_blue_200</item>
        <item name="android:textColorHint">@color/white</item>
    </style>
    <style name="CustomDropdownTextInputLayout.Neon">
        <item name="boxStrokeColor">@color/neon_purple_2</item>
        <item name="hintTextColor">@color/neon_purple_2</item>
        <item name="boxBackgroundColor">@color/neon_card_background_dark</item>
        <item name="endIconTint">@color/neon_purple_2</item>
        <item name="android:textColorHint">@color/white</item>
    </style>
    <style name="CustomDropdownTextInputLayout.Ocean">
        <item name="boxStrokeColor">@color/ocean_lightest</item>
        <item name="hintTextColor">@color/ocean_lightest</item>
        <item name="boxBackgroundColor">@color/ocean_card_background_dark</item>
        <item name="endIconTint">@color/ocean_lightest</item>
        <item name="android:textColorHint">@color/white</item>
    </style>
    <style name="CustomDropdownTextInputLayout.Snooker">
        <item name="boxStrokeColor">@color/snooker_red_200</item>
        <item name="hintTextColor">@color/snooker_red_200</item>
        <item name="boxBackgroundColor">@color/snooker_card_background_dark</item>
        <item name="endIconTint">@color/snooker_red_200</item>
        <item name="android:textColorHint">@color/white</item>
    </style>
    <style name="CustomTabLayout" parent="Widget.MaterialComponents.TabLayout">
        <item name="tabBackground">@color/dark_gray</item>
        <item name="tabIndicatorColor">@color/purple_200</item>
        <item name="tabSelectedTextColor">@color/purple_200</item>
        <item name="tabTextColor">@color/white</item>
    </style>
    <style name="CustomTabLayout.Blue">
        <item name="tabBackground">@color/blue_card_background_dark</item>
        <item name="tabIndicatorColor">@color/blue_200</item>
        <item name="tabSelectedTextColor">@color/blue_200</item>
        <item name="tabTextColor">@color/white</item>
    </style>
    <style name="CustomTabLayout.Crimson">
        <item name="tabBackground">@color/crimson_card_background_dark</item>
        <item name="tabIndicatorColor">@color/crimson_lightest</item>
        <item name="tabSelectedTextColor">@color/crimson_lightest</item>
        <item name="tabTextColor">@color/white</item>
    </style>
    <style name="CustomTabLayout.Dark">
        <item name="tabBackground">@color/dark_gray</item>
        <item name="tabIndicatorColor">@color/purple_200</item>
        <item name="tabSelectedTextColor">@color/purple_200</item>
        <item name="tabTextColor">@color/white</item>
    </style>
    <style name="CustomTabLayout.DarkBlue">
        <item name="tabBackground">@color/dark_blue_card_background_dark</item>
        <item name="tabIndicatorColor">@color/dark_blue_200</item>
        <item name="tabSelectedTextColor">@color/dark_blue_200</item>
        <item name="tabTextColor">@color/white</item>
    </style>
    <style name="CustomTabLayout.Neon">
        <item name="tabBackground">@color/neon_card_background_dark</item>
        <item name="tabIndicatorColor">@color/neon_purple_2</item>
        <item name="tabSelectedTextColor">@color/neon_purple_2</item>
        <item name="tabTextColor">@color/white</item>
    </style>
    <style name="CustomTabLayout.Ocean">
        <item name="tabBackground">@color/ocean_card_background_dark</item>
        <item name="tabIndicatorColor">@color/ocean_lightest</item>
        <item name="tabSelectedTextColor">@color/ocean_lightest</item>
        <item name="tabTextColor">@color/white</item>
    </style>
    <style name="CustomTabLayout.Snooker">
        <item name="tabBackground">@color/snooker_card_background_dark</item>
        <item name="tabIndicatorColor">@color/snooker_red_200</item>
        <item name="tabSelectedTextColor">@color/snooker_red_200</item>
        <item name="tabTextColor">@color/white</item>
    </style>
    <style name="CustomTextInputLayout" parent="Widget.MaterialComponents.TextInputLayout.OutlinedBox">
        <item name="boxStrokeColor">?attr/colorPrimary</item>
        <item name="hintTextColor">?attr/colorPrimary</item>
        <item name="android:textColorHint">@color/white</item>
    </style>
    <style name="CustomTextInputLayout.Blue">
        <item name="boxStrokeColor">@color/blue_200</item>
        <item name="hintTextColor">@color/blue_200</item>
        <item name="boxBackgroundColor">@color/blue_card_background_dark</item>
        <item name="android:textColorHint">@color/white</item>
    </style>
    <style name="CustomTextInputLayout.Crimson">
        <item name="boxStrokeColor">@color/crimson_lightest</item>
        <item name="hintTextColor">@color/crimson_lightest</item>
        <item name="boxBackgroundColor">@color/crimson_card_background_dark</item>
        <item name="android:textColorHint">@color/white</item>
    </style>
    <style name="CustomTextInputLayout.Dark">
        <item name="boxStrokeColor">@color/purple_200</item>
        <item name="hintTextColor">@color/purple_200</item>
        <item name="boxBackgroundColor">@color/dark_gray</item>
        <item name="android:textColorHint">@color/white</item>
    </style>
    <style name="CustomTextInputLayout.DarkBlue">
        <item name="boxStrokeColor">@color/dark_blue_200</item>
        <item name="hintTextColor">@color/dark_blue_200</item>
        <item name="boxBackgroundColor">@color/dark_blue_card_background_dark</item>
        <item name="android:textColorHint">@color/white</item>
    </style>
    <style name="CustomTextInputLayout.Neon">
        <item name="boxStrokeColor">@color/neon_purple_2</item>
        <item name="hintTextColor">@color/neon_purple_2</item>
        <item name="boxBackgroundColor">@color/neon_card_background_dark</item>
        <item name="android:textColorHint">@color/white</item>
    </style>
    <style name="CustomTextInputLayout.Ocean">
        <item name="boxStrokeColor">@color/ocean_lightest</item>
        <item name="hintTextColor">@color/ocean_lightest</item>
        <item name="boxBackgroundColor">@color/ocean_card_background_dark</item>
        <item name="android:textColorHint">@color/white</item>
    </style>
    <style name="CustomTextInputLayout.Snooker">
        <item name="boxStrokeColor">@color/snooker_red_200</item>
        <item name="hintTextColor">@color/snooker_red_200</item>
        <item name="boxBackgroundColor">@color/snooker_card_background_dark</item>
        <item name="android:textColorHint">@color/white</item>
    </style>
    <style name="DarkBlueCardStyleDark" parent="Widget.MaterialComponents.CardView">
        <item name="cardBackgroundColor">@color/dark_blue_card_background_dark</item>
        <item name="cardElevation">4dp</item>
        <item name="cardCornerRadius">8dp</item>
        <item name="android:textColor">@color/white</item>
    </style>
    <style name="DarkBlueNegativeButtonStyle.Night">
        <item name="android:textColor">@color/cyan_200</item>
    </style>
    <style name="DarkBluePositiveButtonStyle.Night">
        <item name="android:textColor">@color/dark_blue_200</item>
    </style>
    <style name="DatePickerDialogTheme" parent="ThemeOverlay.MaterialComponents.Dialog">
        <item name="colorAccent">?attr/colorPrimary</item>
        <item name="android:buttonBarPositiveButtonStyle">@style/DatePickerButtonStyle</item>
        <item name="android:buttonBarNegativeButtonStyle">@style/DatePickerButtonStyle</item>
    </style>
    <style name="DefaultAnimationDialog.Blue">
        <item name="android:windowBackground">@drawable/dropdown_background_blue_dark</item>
        <item name="android:textColor">@color/white</item>
        <item name="colorAccent">@color/blue_200</item>
        <item name="buttonBarPositiveButtonStyle">@style/BluePositiveButtonStyle.Night</item>
        <item name="buttonBarNegativeButtonStyle">@style/BlueNegativeButtonStyle.Night</item>
    </style>
    <style name="DefaultAnimationDialog.Crimson">
        <item name="android:windowBackground">@drawable/dropdown_background_crimson_dark</item>
        <item name="android:textColor">@color/white</item>
        <item name="colorAccent">@color/crimson_lightest</item>
        <item name="buttonBarPositiveButtonStyle">@style/CrimsonPositiveButtonStyle.Night</item>
        <item name="buttonBarNegativeButtonStyle">@style/CrimsonNegativeButtonStyle.Night</item>
    </style>
    <style name="DefaultAnimationDialog.Dark">
        <item name="android:windowBackground">@drawable/dropdown_background_dark</item>
        <item name="android:textColor">@color/white</item>
        <item name="colorAccent">@color/purple_200</item>
        <item name="buttonBarPositiveButtonStyle">@style/DarkPositiveButtonStyle</item>
        <item name="buttonBarNegativeButtonStyle">@style/DarkNegativeButtonStyle</item>
    </style>
    <style name="DefaultAnimationDialog.DarkBlue">
        <item name="android:windowBackground">@drawable/dropdown_background_dark_blue_dark</item>
        <item name="android:textColor">@color/white</item>
        <item name="colorAccent">@color/dark_blue_200</item>
        <item name="buttonBarPositiveButtonStyle">@style/DarkBluePositiveButtonStyle.Night</item>
        <item name="buttonBarNegativeButtonStyle">@style/DarkBlueNegativeButtonStyle.Night</item>
    </style>
    <style name="DefaultAnimationDialog.Neon">
        <item name="android:windowBackground">@drawable/dropdown_background_neon_dark</item>
        <item name="android:textColor">@color/white</item>
        <item name="colorAccent">@color/neon_purple_2</item>
        <item name="buttonBarPositiveButtonStyle">@style/NeonPositiveButtonStyle.Night</item>
        <item name="buttonBarNegativeButtonStyle">@style/NeonNegativeButtonStyle.Night</item>
    </style>
    <style name="DefaultAnimationDialog.Ocean">
        <item name="android:windowBackground">@drawable/dropdown_background_ocean_dark</item>
        <item name="android:textColor">@color/white</item>
        <item name="colorAccent">@color/ocean_lightest</item>
        <item name="buttonBarPositiveButtonStyle">@style/OceanPositiveButtonStyle.Night</item>
        <item name="buttonBarNegativeButtonStyle">@style/OceanNegativeButtonStyle.Night</item>
    </style>
    <style name="DefaultAnimationDialog.Snooker">
        <item name="android:windowBackground">@drawable/dropdown_background_snooker_dark</item>
        <item name="android:textColor">@color/white</item>
        <item name="colorAccent">@color/snooker_red_200</item>
        <item name="buttonBarPositiveButtonStyle">@style/SnookerPositiveButtonStyle.Night</item>
        <item name="buttonBarNegativeButtonStyle">@style/SnookerNegativeButtonStyle.Night</item>
    </style>
    <style name="DropdownDialog.Blue">
        <item name="android:windowBackground">@drawable/dropdown_background_blue_dark</item>
        <item name="android:textColor">@color/white</item>
        <item name="colorAccent">@color/blue_200</item>
        <item name="buttonBarPositiveButtonStyle">@style/BluePositiveButtonStyle.Night</item>
        <item name="buttonBarNegativeButtonStyle">@style/BlueNegativeButtonStyle.Night</item>
    </style>
    <style name="DropdownDialog.Crimson">
        <item name="android:windowBackground">@drawable/dropdown_background_crimson_dark</item>
        <item name="android:textColor">@color/white</item>
        <item name="colorAccent">@color/crimson_lightest</item>
        <item name="buttonBarPositiveButtonStyle">@style/CrimsonPositiveButtonStyle.Night</item>
        <item name="buttonBarNegativeButtonStyle">@style/CrimsonNegativeButtonStyle.Night</item>
    </style>
    <style name="DropdownDialog.DarkBlue">
        <item name="android:windowBackground">@drawable/dropdown_background_dark_blue_dark</item>
        <item name="android:textColor">@color/white</item>
        <item name="colorAccent">@color/dark_blue_200</item>
        <item name="buttonBarPositiveButtonStyle">@style/DarkBluePositiveButtonStyle.Night</item>
        <item name="buttonBarNegativeButtonStyle">@style/DarkBlueNegativeButtonStyle.Night</item>
    </style>
    <style name="DropdownDialog.Neon">
        <item name="android:windowBackground">@drawable/dropdown_background_neon_dark</item>
        <item name="android:textColor">@color/white</item>
        <item name="colorAccent">@color/neon_purple_2</item>
        <item name="buttonBarPositiveButtonStyle">@style/NeonPositiveButtonStyle.Night</item>
        <item name="buttonBarNegativeButtonStyle">@style/NeonNegativeButtonStyle.Night</item>
    </style>
    <style name="DropdownDialog.Ocean">
        <item name="android:windowBackground">@drawable/dropdown_background_ocean_dark</item>
        <item name="android:textColor">@color/white</item>
        <item name="colorAccent">@color/ocean_lightest</item>
        <item name="buttonBarPositiveButtonStyle">@style/OceanPositiveButtonStyle.Night</item>
        <item name="buttonBarNegativeButtonStyle">@style/OceanNegativeButtonStyle.Night</item>
    </style>
    <style name="DropdownDialog.Snooker">
        <item name="android:windowBackground">@drawable/dropdown_background_snooker_dark</item>
        <item name="android:textColor">@color/white</item>
        <item name="colorAccent">@color/snooker_red_200</item>
        <item name="buttonBarPositiveButtonStyle">@style/SnookerPositiveButtonStyle.Night</item>
        <item name="buttonBarNegativeButtonStyle">@style/SnookerNegativeButtonStyle.Night</item>
    </style>
    <style name="NeonCardStyleDark" parent="Widget.MaterialComponents.CardView">
        <item name="cardBackgroundColor">@color/neon_card_background_dark</item>
        <item name="cardElevation">4dp</item>
        <item name="cardCornerRadius">8dp</item>
        <item name="android:textColor">@color/white</item>
    </style>
    <style name="NeonNegativeButtonStyle.Night">
        <item name="android:textColor">@color/neon_pink_1</item>
    </style>
    <style name="NeonPositiveButtonStyle.Night">
        <item name="android:textColor">@color/neon_purple_2</item>
    </style>
    <style name="OceanCardStyleDark" parent="Widget.MaterialComponents.CardView">
        <item name="cardBackgroundColor">@color/ocean_card_background_dark</item>
        <item name="cardElevation">4dp</item>
        <item name="cardCornerRadius">8dp</item>
        <item name="android:textColor">@color/white</item>
    </style>
    <style name="OceanNegativeButtonStyle.Night">
        <item name="android:textColor">@color/ocean_light</item>
    </style>
    <style name="OceanPositiveButtonStyle.Night">
        <item name="android:textColor">@color/ocean_lightest</item>
    </style>
    <style name="PopupMenuItemStyle" parent="TextAppearance.AppCompat.Widget.PopupMenu.Small">
        <item name="android:textColor">@color/white</item>
    </style>
    <style name="PopupMenuItemStyle_Blue" parent="PopupMenuItemStyle">
        <item name="android:textColor">@color/white</item>
        <item name="android:background">@drawable/dropdown_background_blue_dark</item>
    </style>
    <style name="PopupMenuItemStyle_Dark" parent="PopupMenuItemStyle">
        <item name="android:textColor">@color/white</item>
        <item name="android:background">@drawable/dropdown_background_dark</item>
    </style>
    <style name="PopupMenuItemStyle_DarkBlue" parent="PopupMenuItemStyle">
        <item name="android:textColor">@color/white</item>
        <item name="android:background">@drawable/dropdown_background_dark_blue_dark</item>
    </style>
    <style name="PopupMenuItemStyle_Snooker" parent="PopupMenuItemStyle">
        <item name="android:textColor">@color/white</item>
        <item name="android:background">@drawable/dropdown_background_snooker_dark</item>
    </style>
    <style name="PopupMenuListViewStyle" parent="Widget.AppCompat.ListView.DropDown">
        <item name="android:divider">@android:color/transparent</item>
        <item name="android:dividerHeight">0dp</item>
        <item name="android:background">@drawable/dropdown_background_dark</item>
    </style>
    <style name="PopupMenuListViewStyle_Blue" parent="PopupMenuListViewStyle">
        <item name="android:background">@drawable/dropdown_background_blue_dark</item>
    </style>
    <style name="PopupMenuListViewStyle_Dark" parent="PopupMenuListViewStyle">
        <item name="android:background">@drawable/dropdown_background_dark</item>
    </style>
    <style name="PopupMenuListViewStyle_DarkBlue" parent="PopupMenuListViewStyle">
        <item name="android:background">@drawable/dropdown_background_dark_blue_dark</item>
    </style>
    <style name="PopupMenuListViewStyle_Snooker" parent="PopupMenuListViewStyle">
        <item name="android:background">@drawable/dropdown_background_snooker_dark</item>
    </style>
    <style name="PopupMenuStyle" parent="Widget.AppCompat.PopupMenu">
        <item name="android:popupBackground">@drawable/dropdown_background_dark</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:itemBackground">@drawable/dropdown_background_dark</item>
        <item name="android:dropDownListViewStyle">@style/PopupMenuListViewStyle</item>
    </style>
    <style name="PopupMenuStyle_Blue" parent="PopupMenuStyle">
        <item name="android:popupBackground">@drawable/dropdown_background_blue_dark</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:itemBackground">@drawable/dropdown_background_blue_dark</item>
        <item name="android:dropDownListViewStyle">@style/PopupMenuListViewStyle_Blue</item>
    </style>
    <style name="PopupMenuStyle_Dark" parent="PopupMenuStyle">
        <item name="android:popupBackground">@drawable/dropdown_background_dark</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:itemBackground">@drawable/dropdown_background_dark</item>
        <item name="android:dropDownListViewStyle">@style/PopupMenuListViewStyle_Dark</item>
    </style>
    <style name="PopupMenuStyle_DarkBlue" parent="PopupMenuStyle">
        <item name="android:popupBackground">@drawable/dropdown_background_dark_blue_dark</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:itemBackground">@drawable/dropdown_background_dark_blue_dark</item>
        <item name="android:dropDownListViewStyle">@style/PopupMenuListViewStyle_DarkBlue</item>
    </style>
    <style name="PopupMenuStyle_Snooker" parent="PopupMenuStyle">
        <item name="android:popupBackground">@drawable/dropdown_background_snooker_dark</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:itemBackground">@drawable/dropdown_background_snooker_dark</item>
        <item name="android:dropDownListViewStyle">@style/PopupMenuListViewStyle_Snooker</item>
    </style>
    <style name="SnookerCardStyleDark" parent="Widget.MaterialComponents.CardView">
        <item name="cardBackgroundColor">@color/snooker_card_background_dark</item>
        <item name="cardElevation">4dp</item>
        <item name="cardCornerRadius">8dp</item>
        <item name="android:textColor">@color/white</item>
    </style>
    <style name="SnookerNegativeButtonStyle.Night">
        <item name="android:textColor">@color/snooker_green_200</item>
    </style>
    <style name="SnookerPositiveButtonStyle.Night">
        <item name="android:textColor">@color/snooker_red_200</item>
    </style>
    <style name="Theme.DieSnookerApp" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        <item name="colorPrimary">@color/purple_200</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/black</item>
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_200</item>
        <item name="colorOnSecondary">@color/black</item>
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>

        
        <item name="android:popupMenuStyle">@style/PopupMenuStyle</item>
        <item name="popupMenuStyle">@style/PopupMenuStyle</item>
        <item name="android:itemTextAppearance">@style/PopupMenuItemStyle</item>
    </style>
    <style name="Theme.DieSnookerApp.Blue" parent="Theme.MaterialComponents.NoActionBar">
        <item name="colorPrimary">@color/blue_200</item>
        <item name="colorPrimaryVariant">@color/blue_500</item>
        <item name="colorOnPrimary">@color/black</item>
        <item name="colorSecondary">@color/orange_200</item>
        <item name="colorSecondaryVariant">@color/orange_500</item>
        <item name="colorOnSecondary">@color/black</item>
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        <item name="android:windowBackground">@color/black</item>
        <item name="android:textColorPrimary">@color/white</item>
        <item name="colorSurface">@color/black</item>
        <item name="colorOnSurface">@color/white</item>

        
        <item name="cardBackgroundColor">@color/blue_card_background_dark</item>
        <item name="materialCardViewStyle">@style/BlueCardStyleDark</item>

        
        <item name="android:popupMenuStyle">@style/PopupMenuStyle_Blue</item>
        <item name="popupMenuStyle">@style/PopupMenuStyle_Blue</item>
        <item name="android:itemTextAppearance">@style/PopupMenuItemStyle_Blue</item>
    </style>
    <style name="Theme.DieSnookerApp.Crimson" parent="Theme.MaterialComponents.NoActionBar">
        <item name="colorPrimary">@color/crimson_lightest</item>
        <item name="colorPrimaryVariant">@color/crimson_light</item>
        <item name="colorOnPrimary">@color/black</item>
        <item name="colorSecondary">@color/crimson_medium</item>
        <item name="colorSecondaryVariant">@color/crimson_dark</item>
        <item name="colorOnSecondary">@color/white</item>
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        <item name="android:windowBackground">@color/crimson_dark</item>
        <item name="android:textColorPrimary">@color/white</item>
        <item name="colorSurface">@color/crimson_dark</item>
        <item name="colorOnSurface">@color/white</item>

        
        <item name="cardBackgroundColor">@color/crimson_card_background_dark</item>
        <item name="materialCardViewStyle">@style/CrimsonCardStyleDark</item>

        
        <item name="android:popupMenuStyle">@style/PopupMenuStyle_Crimson</item>
        <item name="popupMenuStyle">@style/PopupMenuStyle_Crimson</item>
        <item name="android:itemTextAppearance">@style/PopupMenuItemStyle</item>
    </style>
    <style name="Theme.DieSnookerApp.DarkBlue" parent="Theme.MaterialComponents.NoActionBar">
        <item name="colorPrimary">@color/dark_blue_200</item>
        <item name="colorPrimaryVariant">@color/dark_blue_500</item>
        <item name="colorOnPrimary">@color/black</item>
        <item name="colorSecondary">@color/cyan_200</item>
        <item name="colorSecondaryVariant">@color/cyan_500</item>
        <item name="colorOnSecondary">@color/black</item>
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        <item name="android:windowBackground">@color/black</item>
        <item name="android:textColorPrimary">@color/white</item>
        <item name="colorSurface">@color/black</item>
        <item name="colorOnSurface">@color/white</item>

        
        <item name="cardBackgroundColor">@color/dark_blue_card_background_dark</item>
        <item name="materialCardViewStyle">@style/DarkBlueCardStyleDark</item>

        
        <item name="android:popupMenuStyle">@style/PopupMenuStyle_DarkBlue</item>
        <item name="popupMenuStyle">@style/PopupMenuStyle_DarkBlue</item>
        <item name="android:itemTextAppearance">@style/PopupMenuItemStyle_DarkBlue</item>
    </style>
    <style name="Theme.DieSnookerApp.Neon" parent="Theme.MaterialComponents.NoActionBar">
        <item name="colorPrimary">@color/neon_purple_2</item>
        <item name="colorPrimaryVariant">@color/neon_purple_1</item>
        <item name="colorOnPrimary">@color/black</item>
        <item name="colorSecondary">@color/neon_pink_1</item>
        <item name="colorSecondaryVariant">@color/neon_pink_3</item>
        <item name="colorOnSecondary">@color/white</item>
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        <item name="android:windowBackground">@color/neon_purple_1</item>
        <item name="android:textColorPrimary">@color/white</item>
        <item name="colorSurface">@color/neon_purple_1</item>
        <item name="colorOnSurface">@color/white</item>

        
        <item name="cardBackgroundColor">@color/neon_card_background_dark</item>
        <item name="materialCardViewStyle">@style/NeonCardStyleDark</item>

        
        <item name="android:popupMenuStyle">@style/PopupMenuStyle_Neon</item>
        <item name="popupMenuStyle">@style/PopupMenuStyle_Neon</item>
        <item name="android:itemTextAppearance">@style/PopupMenuItemStyle</item>
    </style>
    <style name="Theme.DieSnookerApp.Ocean" parent="Theme.MaterialComponents.NoActionBar">
        <item name="colorPrimary">@color/ocean_lightest</item>
        <item name="colorPrimaryVariant">@color/ocean_light</item>
        <item name="colorOnPrimary">@color/black</item>
        <item name="colorSecondary">@color/ocean_medium</item>
        <item name="colorSecondaryVariant">@color/ocean_dark</item>
        <item name="colorOnSecondary">@color/white</item>
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        <item name="android:windowBackground">@color/ocean_dark</item>
        <item name="android:textColorPrimary">@color/white</item>
        <item name="colorSurface">@color/ocean_dark</item>
        <item name="colorOnSurface">@color/white</item>

        
        <item name="cardBackgroundColor">@color/ocean_card_background_dark</item>
        <item name="materialCardViewStyle">@style/OceanCardStyleDark</item>

        
        <item name="android:popupMenuStyle">@style/PopupMenuStyle_Ocean</item>
        <item name="popupMenuStyle">@style/PopupMenuStyle_Ocean</item>
        <item name="android:itemTextAppearance">@style/PopupMenuItemStyle</item>
    </style>
    <style name="Theme.DieSnookerApp.Snooker" parent="Theme.MaterialComponents.NoActionBar">
        <item name="colorPrimary">@color/snooker_red_200</item>
        <item name="colorPrimaryVariant">@color/snooker_red_500</item>
        <item name="colorOnPrimary">@color/black</item>
        <item name="colorSecondary">@color/snooker_green_200</item>
        <item name="colorSecondaryVariant">@color/snooker_green_500</item>
        <item name="colorOnSecondary">@color/black</item>
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        <item name="android:windowBackground">@color/snooker_green_700</item>
        <item name="android:textColorPrimary">@color/white</item>
        <item name="colorSurface">@color/snooker_green_700</item>
        <item name="colorOnSurface">@color/white</item>

        
        <item name="cardBackgroundColor">@color/snooker_card_background_dark</item>
        <item name="materialCardViewStyle">@style/SnookerCardStyleDark</item>

        
        <item name="android:popupMenuStyle">@style/PopupMenuStyle_Snooker</item>
        <item name="popupMenuStyle">@style/PopupMenuStyle_Snooker</item>
        <item name="android:itemTextAppearance">@style/PopupMenuItemStyle_Snooker</item>
    </style>
</resources>