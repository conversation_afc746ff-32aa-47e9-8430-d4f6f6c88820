// Generated by view binder compiler. Do not edit!
package com.atom.diesnookerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.atom.diesnookerapp.R;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton;
import com.google.android.material.floatingactionbutton.FloatingActionButton;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentTrainingsplanBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final FloatingActionButton addExerciseButton;

  @NonNull
  public final MaterialButton debugButton;

  @NonNull
  public final TextView emptyStateText;

  @NonNull
  public final ExtendedFloatingActionButton historyButton;

  @NonNull
  public final MaterialButton manageExercisesButton;

  @NonNull
  public final TextView titleText;

  @NonNull
  public final RecyclerView trainingsplanRecyclerView;

  private FragmentTrainingsplanBinding(@NonNull ConstraintLayout rootView,
      @NonNull FloatingActionButton addExerciseButton, @NonNull MaterialButton debugButton,
      @NonNull TextView emptyStateText, @NonNull ExtendedFloatingActionButton historyButton,
      @NonNull MaterialButton manageExercisesButton, @NonNull TextView titleText,
      @NonNull RecyclerView trainingsplanRecyclerView) {
    this.rootView = rootView;
    this.addExerciseButton = addExerciseButton;
    this.debugButton = debugButton;
    this.emptyStateText = emptyStateText;
    this.historyButton = historyButton;
    this.manageExercisesButton = manageExercisesButton;
    this.titleText = titleText;
    this.trainingsplanRecyclerView = trainingsplanRecyclerView;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentTrainingsplanBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentTrainingsplanBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_trainingsplan, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentTrainingsplanBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.addExerciseButton;
      FloatingActionButton addExerciseButton = ViewBindings.findChildViewById(rootView, id);
      if (addExerciseButton == null) {
        break missingId;
      }

      id = R.id.debugButton;
      MaterialButton debugButton = ViewBindings.findChildViewById(rootView, id);
      if (debugButton == null) {
        break missingId;
      }

      id = R.id.emptyStateText;
      TextView emptyStateText = ViewBindings.findChildViewById(rootView, id);
      if (emptyStateText == null) {
        break missingId;
      }

      id = R.id.historyButton;
      ExtendedFloatingActionButton historyButton = ViewBindings.findChildViewById(rootView, id);
      if (historyButton == null) {
        break missingId;
      }

      id = R.id.manageExercisesButton;
      MaterialButton manageExercisesButton = ViewBindings.findChildViewById(rootView, id);
      if (manageExercisesButton == null) {
        break missingId;
      }

      id = R.id.titleText;
      TextView titleText = ViewBindings.findChildViewById(rootView, id);
      if (titleText == null) {
        break missingId;
      }

      id = R.id.trainingsplanRecyclerView;
      RecyclerView trainingsplanRecyclerView = ViewBindings.findChildViewById(rootView, id);
      if (trainingsplanRecyclerView == null) {
        break missingId;
      }

      return new FragmentTrainingsplanBinding((ConstraintLayout) rootView, addExerciseButton,
          debugButton, emptyStateText, historyButton, manageExercisesButton, titleText,
          trainingsplanRecyclerView);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
