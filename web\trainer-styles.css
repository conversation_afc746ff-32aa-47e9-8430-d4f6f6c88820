/* Trainer Registration Styles */
.trainer-registration-page {
    background: linear-gradient(135deg, #232946 0%, #4a4e69 100%);
    color: #fff;
}

#trainer-register-container {
    max-width: 600px;
    margin: 40px auto;
    background: #fff;
    border-radius: 10px;
    box-shadow: 0 4px 32px rgba(0,0,0,0.15);
    padding: 32px 28px;
    color: #232946;
}

.registration-header {
    text-align: center;
    margin-bottom: 32px;
}

.registration-header h1 {
    color: #232946;
    font-size: 2rem;
    margin-bottom: 16px;
}

.registration-header h2 {
    color: #232946;
    font-size: 1.5rem;
    margin-bottom: 16px;
}

.registration-header p {
    color: #4a4e69;
    font-size: 1rem;
    line-height: 1.5;
}

.error-message {
    color: #dc3545;
    margin: 16px 0;
    font-size: 0.9rem;
}

.form-footer {
    margin-top: 24px;
    text-align: center;
    font-size: 0.9rem;
}

.form-footer a {
    color: #232946;
    text-decoration: underline;
}

#verification-message {
    text-align: center;
    padding: 32px;
}

#verification-message h3 {
    margin-bottom: 16px;
    color: #232946;
}

#verification-message p {
    margin-bottom: 16px;
    color: #4a4e69;
    line-height: 1.5;
}

.terms-link {
    color: #232946;
    text-decoration: underline;
}

/* Trainer Dashboard Styles */
.trainer-dashboard {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid #e0e7ff;
}

.dashboard-title h2 {
    margin: 0;
    font-size: 1.5rem;
    color: #232946;
}

.dashboard-title p {
    margin: 8px 0 0 0;
    color: #4a4e69;
    font-size: 0.9rem;
}

.dashboard-actions {
    display: flex;
    gap: 12px;
}

.athlete-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 24px;
    margin-bottom: 32px;
}

.athlete-card {
    background: #fff;
    border-radius: 10px;
    box-shadow: 0 2px 12px rgba(35,41,70,0.08);
    padding: 24px;
    transition: transform 0.2s, box-shadow 0.2s;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.athlete-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 24px rgba(35,41,70,0.12);
}

.athlete-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, #eebbc3, #b8c1ec);
}

.athlete-name {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 8px;
    color: #232946;
}

.athlete-email {
    color: #4a4e69;
    font-size: 0.9rem;
    margin-bottom: 16px;
}

.athlete-stats {
    display: flex;
    justify-content: space-between;
    margin-top: 16px;
}

.stat-item {
    text-align: center;
    flex: 1;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: 600;
    color: #232946;
}

.stat-label {
    font-size: 0.8rem;
    color: #4a4e69;
}

.athlete-last-active {
    font-size: 0.8rem;
    color: #4a4e69;
    margin-top: 16px;
    text-align: right;
}

.empty-state {
    text-align: center;
    padding: 48px 0;
    color: #4a4e69;
}

.empty-state-icon {
    font-size: 3rem;
    margin-bottom: 16px;
    color: #b8c1ec;
}

.empty-state-message {
    font-size: 1.1rem;
    margin-bottom: 8px;
}

.empty-state-description {
    font-size: 0.9rem;
    max-width: 400px;
    margin: 0 auto;
    line-height: 1.5;
}

/* Athlete Detail View */
.athlete-detail {
    background: #fff;
    border-radius: 10px;
    box-shadow: 0 2px 12px rgba(35,41,70,0.08);
    padding: 24px;
    margin-bottom: 24px;
}

.athlete-detail-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 16px;
    border-bottom: 1px solid #e0e7ff;
}

.athlete-detail-name {
    font-size: 1.5rem;
    font-weight: 600;
    color: #232946;
    margin: 0;
}

.athlete-detail-email {
    color: #4a4e69;
    font-size: 0.9rem;
    margin: 4px 0 0 0;
}

.back-button {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #232946;
    text-decoration: none;
    font-size: 0.9rem;
    padding: 8px 16px;
    border-radius: 6px;
    background: #e0e7ff;
    transition: background 0.2s;
}

.back-button:hover {
    background: #b8c1ec;
}

.data-filters {
    display: flex;
    gap: 16px;
    margin-bottom: 24px;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    align-items: center;
    gap: 8px;
}

.filter-label {
    font-size: 0.9rem;
    color: #4a4e69;
}

.chart-container {
    margin-bottom: 32px;
    background: #fff;
    border-radius: 10px;
    box-shadow: 0 2px 12px rgba(35,41,70,0.08);
    padding: 24px;
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.chart-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #232946;
    margin: 0;
}

.chart-actions {
    display: flex;
    gap: 8px;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 16px;
}

.data-table th,
.data-table td {
    padding: 12px 16px;
    text-align: left;
    border-bottom: 1px solid #e0e7ff;
}

.data-table th {
    font-weight: 600;
    color: #232946;
    background: #f7f7fb;
}

.data-table tr:last-child td {
    border-bottom: none;
}

.data-table tr:hover td {
    background: #f7f7fb;
}

/* Export Button */
.export-btn {
    background: #232946;
    color: #fff;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 0.9rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: background 0.2s;
}

.export-btn:hover {
    background: #4a4e69;
}

/* Responsive Styles */
@media (max-width: 768px) {
    .athlete-list {
        grid-template-columns: 1fr;
    }
    
    .dashboard-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .dashboard-actions {
        margin-top: 16px;
    }
    
    .data-filters {
        flex-direction: column;
        align-items: flex-start;
    }
}
