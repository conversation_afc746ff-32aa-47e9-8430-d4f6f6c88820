<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="?attr/colorSurface"
    android:padding="16dp">

    <com.google.android.material.textview.MaterialTextView
        android:id="@+id/exerciseNameTextView"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:textAppearance="?attr/textAppearanceListItem"
        android:textSize="18sp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/editExerciseButton"
        app:layout_constraintHorizontal_bias="0.0"
        tools:text="Long Shot Potting" />

    <com.google.android.material.textview.MaterialTextView
        android:id="@+id/exerciseCategoryTextView"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:textAppearance="?attr/textAppearanceListItemSecondary"
        android:textSize="14sp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/exerciseNameTextView"
        app:layout_constraintEnd_toStartOf="@+id/editExerciseButton"
        app:layout_constraintHorizontal_bias="0.0"
        tools:text="Potting" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/deleteExerciseButton"
        style="?attr/materialButtonOutlinedStyle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/delete_button"
        app:icon="@android:drawable/ic_menu_delete"
        app:iconGravity="textStart"
        android:minWidth="0dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/editExerciseButton"
        style="?attr/materialButtonOutlinedStyle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/edit_button"
        app:icon="@android:drawable/ic_menu_edit"
        app:iconGravity="textStart"
        android:minWidth="0dp"
        android:layout_marginEnd="8dp"
        app:layout_constraintEnd_toStartOf="@id/deleteExerciseButton"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
