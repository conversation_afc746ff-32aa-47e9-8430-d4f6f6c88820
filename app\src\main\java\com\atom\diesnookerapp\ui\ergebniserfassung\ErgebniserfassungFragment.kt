package com.atom.diesnookerapp.ui.ergebniserfassung

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.atom.diesnookerapp.R
import com.atom.diesnookerapp.data.firebase.FirebaseAuthManager
import com.atom.diesnookerapp.ui.selbsteinschaetzung.AssessmentAdapter
import com.atom.diesnookerapp.ui.selbsteinschaetzung.AssessmentItem
import com.atom.diesnookerapp.ui.trainingsplan.TrainingsplanManager
import com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton
import kotlinx.coroutines.launch

class ErgebniserfassungFragment : Fragment() {

    private lateinit var trainingsplanManager: TrainingsplanManager
    private lateinit var firebaseAuthManager: FirebaseAuthManager
    private lateinit var assessmentAdapter: AssessmentAdapter
    private var itemsList = mutableListOf<AssessmentItem>()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_ergebniserfassung, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        trainingsplanManager = TrainingsplanManager(requireContext())
        firebaseAuthManager = FirebaseAuthManager()

        view.findViewById<ExtendedFloatingActionButton>(R.id.historyButton).setOnClickListener {
            findNavController().navigate(R.id.navigation_exercise_history)
        }

        val recyclerView = view.findViewById<RecyclerView>(R.id.recyclerView)
        recyclerView.layoutManager = LinearLayoutManager(requireContext())

        assessmentAdapter = AssessmentAdapter(itemsList) { item ->
            // item.id is the category name
            val bundle = Bundle().apply {
                putString("categoryName", item.id)
            }
            // Navigate to a specific fragment based on category, passing the category name.
            // This assumes that the destination fragments (BreakbuildingFragment, PottingFragment, etc.)
            // are updated to receive "categoryName" as an argument.
            // A more robust solution might involve a single destination fragment that handles
            // displaying exercises based on the passed categoryName.
            // For now, we adapt the existing navigation structure.
            when (item.id.lowercase().replace(" ", "")) { // Normalize for matching, if needed.
                "breakbuilding" -> findNavController().navigate(R.id.action_navigation_ergebniserfassung_to_breakbuildingFragment, bundle)
                "potting" -> findNavController().navigate(R.id.action_navigation_ergebniserfassung_to_pottingFragment, bundle)
                "safeties" -> findNavController().navigate(R.id.action_navigation_ergebniserfassung_to_safetiesFragment, bundle)
                "splits" -> findNavController().navigate(R.id.action_navigation_ergebniserfassung_to_splitsFragment, bundle)
                "stellungsspiel" -> findNavController().navigate(R.id.action_navigation_ergebniserfassung_to_stellungsspielFragment, bundle)
                "technik" -> findNavController().navigate(R.id.action_navigation_ergebniserfassung_to_technikFragment, bundle)
                else -> {
                    // Navigate to GenericExerciseListFragment for any other category
                    findNavController().navigate(R.id.action_ergebniserfassung_to_generic_exercise_list, bundle)
                }
            }
        }
        recyclerView.adapter = assessmentAdapter

        loadCategories()
    }

    private fun loadCategories() {
        viewLifecycleOwner.lifecycleScope.launch {
            try {
                if (firebaseAuthManager.isLoggedIn()) {
                    val categories = trainingsplanManager.getExerciseCategories()
                    if (categories.isNotEmpty()) {
                        itemsList.clear()
                        itemsList.addAll(categories.map { categoryName ->
                            AssessmentItem(id = categoryName, title = categoryName)
                        })
                        assessmentAdapter.notifyDataSetChanged() // Or use DiffUtil if adapter supports it
                    } else {
                        itemsList.clear()
                        assessmentAdapter.notifyDataSetChanged()
                        Toast.makeText(context, "No exercise categories found.", Toast.LENGTH_SHORT).show()
                    }
                } else {
                    itemsList.clear()
                    assessmentAdapter.notifyDataSetChanged()
                    Toast.makeText(context, "Please log in to see categories.", Toast.LENGTH_SHORT).show()
                }
            } catch (e: Exception) {
                itemsList.clear()
                assessmentAdapter.notifyDataSetChanged()
                Toast.makeText(context, "Failed to load categories: ${e.message}", Toast.LENGTH_LONG).show()
            }
        }
    }
}