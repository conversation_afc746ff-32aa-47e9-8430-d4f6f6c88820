package com.atom.diesnookerapp.ui.aufgaben

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.atom.diesnookerapp.R
import org.threeten.bp.LocalDate

class MatchplayFragment : Fragment() {
    private lateinit var recyclerView: RecyclerView
    private lateinit var pointsTextView: TextView
    private lateinit var titleTextView: TextView
    private lateinit var taskAdapter: TaskAdapter
    private lateinit var taskManager: TaskManager
    
    private val matchplayTasks = listOf(
        Task(
            id = "entscheidungen_frame",
            title = "Entscheidungen richtig treffen",
            description = "Frame auseinander nehmen",
            frequency = TaskFrequency.WEEKLY,
            points = 2,
            category = "matchplay"
        ),
        Task(
            id = "entscheidungen_video",
            title = "Entscheidungen richtig treffen",
            description = "Videoanalyse 30min",
            frequency = TaskFrequency.WEEKLY,
            points = 3,
            category = "matchplay"
        ),
        Task(
            id = "entscheidungen_tip",
            title = "Entscheidungen richtig treffen",
            description = "Bei den drei Frames mit Tip weiß anzeigen",
            frequency = TaskFrequency.WEEKLY,
            points = 1,
            category = "matchplay"
        ),
        Task(
            id = "matchpraxis",
            title = "Matchpraxis",
            description = "5h Sparring/Wettkampf",
            frequency = TaskFrequency.WEEKLY,
            points = 5,
            category = "matchplay"
        ),
        Task(
            id = "richtig_abschaetzen",
            title = "Richtig abschätzen, wie man spielen sollte",
            description = "Nach jedem Training Selbsteinschätzung ausfüllen",
            frequency = TaskFrequency.DAILY,
            points = 1,
            category = "matchplay"
        )
    )
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_task_list, container, false)
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        // Initialize TaskManager
        taskManager = TaskManager(requireContext())
        
        // Initialize views
        recyclerView = view.findViewById(R.id.tasksRecyclerView)
        pointsTextView = view.findViewById(R.id.pointsTextView)
        titleTextView = view.findViewById(R.id.categoryTitleTextView)
        
        // Set up RecyclerView
        recyclerView.layoutManager = LinearLayoutManager(requireContext())
        
        // Initialize tasks if needed
        taskManager.initializeTasksIfNeeded(matchplayTasks)
        
        // Get tasks from storage
        val tasks = taskManager.getTasksByCategory("matchplay")
        
        // Set up adapter
        taskAdapter = TaskAdapter(
            tasks,
            onTaskCompleted = { task ->
                taskManager.markTaskCompleted(task.id, LocalDate.now())
                updatePointsDisplay()
            },
            onTaskUncompleted = { task ->
                taskManager.markTaskUncompleted(task.id, LocalDate.now())
                updatePointsDisplay()
            },
            getFrequencyText = { task ->
                when (task.frequency) {
                    TaskFrequency.DAILY -> "täglich"
                    TaskFrequency.WEEKLY -> when (task.id) {
                        "entscheidungen_frame" -> "3x pro Woche"
                        "entscheidungen_video" -> "2x pro Woche"
                        //"entscheidungen_tip" -> "1x pro Woche"
                        //"matchpraxis" -> "1x pro Woche"
                        else -> "wöchentlich"
                    }
                    TaskFrequency.MONTHLY -> "monatlich"
                }
            },
            taskManager = taskManager  // Add this parameter
        )
        
        recyclerView.adapter = taskAdapter
        
        // Set title
        titleTextView.text = "Matchplay"
        
        // Update points display
        updatePointsDisplay()
    }
    
    private fun updatePointsDisplay() {
        val totalPoints = taskManager.getTotalPoints()
        pointsTextView.text = "Gesammelte Punkte: $totalPoints"
    }
}