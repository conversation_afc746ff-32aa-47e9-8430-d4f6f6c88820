package com.atom.diesnookerapp.ui.aufgaben

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.atom.diesnookerapp.R
import org.threeten.bp.LocalDate

class TechnikFragment : Fragment() {
    private lateinit var recyclerView: RecyclerView
    private lateinit var pointsTextView: TextView
    private lateinit var titleTextView: TextView
    private lateinit var taskAdapter: TaskAdapter
    private lateinit var taskManager: TaskManager

    private val technikTasks = listOf(
        Task(
            id = "weiss_hinten_treffen",
            title = "Weiß hinten treffen",
            description = "Beim Einspielen blau gerade spielen mit Video",
            frequency = TaskFrequency.WEEKLY,
            points = 1,
            category = "technik"
        ),
        Task(
            id = "ruhig_unten_bleiben",
            title = "Ruhig bleiben, unten bleiben",
            description = "Immer versuchen, auf Video bewerten 1-5",
            frequency = TaskFrequency.WEEKLY,
            points = 1,
            category = "technik"
        )
    )

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_task_list, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        // Initialize TaskManager
        taskManager = TaskManager(requireContext())

        // Initialize views
        recyclerView = view.findViewById(R.id.tasksRecyclerView)
        pointsTextView = view.findViewById(R.id.pointsTextView)
        titleTextView = view.findViewById(R.id.categoryTitleTextView)

        // Set up RecyclerView
        recyclerView.layoutManager = LinearLayoutManager(requireContext())

        // Initialize tasks if needed
        taskManager.initializeTasksIfNeeded(technikTasks)

        // Get tasks from storage
        val tasks = taskManager.getTasksByCategory("technik")

        // Set up adapter
        taskAdapter = TaskAdapter(
            tasks,
            onTaskCompleted = { task ->
                taskManager.markTaskCompleted(task.id, LocalDate.now())
                updatePointsDisplay()
            },
            onTaskUncompleted = { task ->
                taskManager.markTaskUncompleted(task.id, LocalDate.now())
                updatePointsDisplay()
            },
            getFrequencyText = { task ->
                when (task.frequency) {
                    TaskFrequency.DAILY -> when (task.id) {
                        "weiss_hinten_treffen", "ruhig_unten_bleiben" -> "1x pro Woche" // Changed from "unbegrenzt oft"
                        else -> "täglich"
                    }
                    TaskFrequency.WEEKLY -> "wöchentlich"
                    TaskFrequency.MONTHLY -> "monatlich"
                }
            },
            taskManager = taskManager  // Add this parameter
        )

        // Set up custom points callback
        taskAdapter.setOnTaskCompletedWithCustomPoints { task, points ->
            taskManager.markTaskCompletedWithCustomPoints(task.id, LocalDate.now(), points)
            updatePointsDisplay()
        }

        recyclerView.adapter = taskAdapter

        // Set title
        titleTextView.text = "Technik"

        // Update points display
        updatePointsDisplay()
    }

    private fun updatePointsDisplay() {
        val totalPoints = taskManager.getTotalPoints()
        pointsTextView.text = "Gesammelte Punkte: $totalPoints"
    }
}