package com.atom.diesnookerapp.ui.ergebniserfassung

import android.content.Context
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import org.threeten.bp.LocalDateTime

class ExercisePreferences(context: Context) {
    private val preferences = context.getSharedPreferences("exercise_records", Context.MODE_PRIVATE)
    private val gson = Gson()

    fun saveRecord(record: ExerciseRecord) {
        val records = getRecords().toMutableList()
        records.add(record)
        
        preferences.edit().putString("records", gson.toJson(records)).apply()
    }

    fun getRecords(): List<ExerciseRecord> {
        val json = preferences.getString("records", "[]")
        val type = object : TypeToken<List<ExerciseRecord>>() {}.type
        return gson.fromJson(json, type) ?: emptyList()
    }

    fun getAverageTime(exerciseId: String): Float {
        val records = getRecords()
            .filter { it.exerciseId == exerciseId && it.timeInMinutes != null }
            .mapNotNull { it.timeInMinutes }
        
        return if (records.isNotEmpty()) {
            records.average().toFloat()
        } else {
            0f
        }
    }

    fun getLastScore(exerciseId: String): Int? {
        return getRecords()
            .filter { it.exerciseId == exerciseId && it.score != null }
            .maxByOrNull { it.timestamp }
            ?.score
    }

    fun getAverageTimeLastSevenDays(exerciseId: String): Float {
        val sevenDaysAgo = LocalDateTime.now().minusDays(7)
        val records = getRecords()
            .filter { 
                it.exerciseId == exerciseId && 
                it.timeInMinutes != null && 
                it.timestamp.isAfter(sevenDaysAgo)
            }
            .mapNotNull { it.timeInMinutes }
        
        return if (records.isNotEmpty()) {
            records.average().toFloat()
        } else {
            0f
        }
    }

    fun updateRecord(oldRecord: ExerciseRecord, newRecord: ExerciseRecord) {
        val records = getRecords().toMutableList()
        val index = records.indexOfFirst { 
            it.exerciseId == oldRecord.exerciseId && 
            it.timestamp == oldRecord.timestamp 
        }
        if (index != -1) {
            records[index] = newRecord
            saveRecords(records)
        }
    }

    private fun saveRecords(records: List<ExerciseRecord>) {
        preferences.edit().putString("records", gson.toJson(records)).apply()
    }
} 