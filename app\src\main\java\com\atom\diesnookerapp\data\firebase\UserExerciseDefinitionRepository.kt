package com.atom.diesnookerapp.data.firebase

import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.ktx.firestore
import com.google.firebase.ktx.Firebase
import kotlinx.coroutines.tasks.await

private const val COLLECTION_NAME = "user_exercise_definitions"

class UserExerciseDefinitionRepository {

    private val db = Firebase.firestore.collection(COLLECTION_NAME)

    suspend fun getExercisesForUser(userId: String): Result<List<UserExerciseDefinition>> {
        return try {
            val querySnapshot = db.whereEqualTo("userId", userId).get().await()
            val exercises = querySnapshot.documents.mapNotNull { document ->
                document.toObject(UserExerciseDefinition::class.java)
            }
            Result.success(exercises)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    suspend fun saveExercise(exercise: UserExerciseDefinition): Result<Unit> {
        return try {
            val exerciseToSave = if (exercise.id.isBlank()) {
                exercise.copy(id = db.document().id)
            } else {
                exercise
            }
            db.document(exerciseToSave.id).set(exerciseToSave).await()
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    suspend fun deleteExercise(exerciseId: String): Result<Unit> {
        return try {
            db.document(exerciseId).delete().await()
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
}
