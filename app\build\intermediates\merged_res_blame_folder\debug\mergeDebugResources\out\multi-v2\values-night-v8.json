{"logs": [{"outputFile": "com.atom.diesnookerapp-mergeDebugResources-81:/values-night-v8/values-night-v8.xml", "map": [{"source": "D:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\src\\main\\res\\values-night\\text_input_styles.xml", "from": {"startLines": "-1,-1,101,-1,-1,118,84,-1,-1,-1,93,-1,-1,110,76,-1", "startColumns": "-1,-1,4,-1,-1,4,4,-1,-1,-1,4,-1,-1,4,4,-1", "startOffsets": "-1,-1,5277,-1,-1,6169,4377,-1,-1,-1,4854,-1,-1,5761,3964,-1", "endLines": "-1,-1,107,-1,-1,124,90,-1,-1,-1,98,-1,-1,115,81,-1", "endColumns": "-1,-1,12,-1,-1,12,12,-1,-1,-1,12,-1,-1,12,12,-1", "endOffsets": "-1,-1,5691,-1,-1,6568,4781,-1,-1,-1,5195,-1,-1,6090,4297,-1"}, "to": {"startLines": "26,31,38,45,52,59,66,73,128,133,139,145,151,157,163,169", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1236,1567,1950,2363,2736,3143,3541,3944,6927,7230,7549,7890,8197,8535,8864,9197", "endLines": "30,37,44,51,58,65,72,79,132,138,144,150,156,162,168,174", "endColumns": "12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12", "endOffsets": "1562,1945,2358,2731,3138,3536,3939,4349,7225,7544,7885,8192,8530,8859,9192,9531"}}, {"source": "D:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\src\\main\\res\\values-night\\default_dialog_styles.xml", "from": {"startLines": "-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1"}, "to": {"startLines": "192,206,213,234", "startColumns": "4,4,4,4", "startOffsets": "10484,11416,11857,13262", "endLines": "198,212,219,240", "endColumns": "12,12,12,12", "endOffsets": "10935,11852,12330,13732"}}, {"source": "D:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\src\\main\\res\\values-night\\theme_styles.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "20,23,199,220,227,248,262,269,289,292,301,304", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "969,1101,10940,12335,12796,14185,15123,15576,16830,16957,17417,17545", "endLines": "22,25,205,226,233,254,268,275,291,294,303,306", "endColumns": "12,12,12,12,12,12,12,12,12,12,12,12", "endOffsets": "1096,1231,11411,12791,13257,14648,15571,16029,16952,17081,17540,17671"}}, {"source": "D:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\src\\main\\res\\values-night\\styles.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "8,11,181,184,241,255,276,379,382", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "384,510,9874,10002,13737,14653,16034,22207,22343", "endLines": "10,13,183,186,247,261,282,381,384", "endColumns": "12,12,12,12,12,12,12,12,12", "endOffsets": "505,629,9997,10130,14180,15118,16496,22338,22472"}}, {"source": "D:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\src\\main\\res\\values-night\\popup_menu_item_styles.xml", "from": {"startLines": "-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1"}, "to": {"startLines": "307,310,314,318,322", "startColumns": "4,4,4,4,4", "startOffsets": "17676,17843,18073,18298,18537", "endLines": "309,313,317,321,325", "endColumns": "12,12,12,12,12", "endOffsets": "17838,18068,18293,18532,18768"}}, {"source": "D:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\src\\main\\res\\values-night\\popup_styles.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "187,326,331,334,337,340,343,349,355,361,367", "startColumns": "4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "10135,18773,19079,19258,19432,19620,19805,20208,20619,21020,21449", "endLines": "191,330,333,336,339,342,348,354,360,366,372", "endColumns": "12,12,12,12,12,12,12,12,12,12,12", "endOffsets": "10479,19074,19253,19427,19615,19800,20203,20614,21015,21444,21867"}}, {"source": "D:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\src\\main\\res\\values-night\\themes.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2,14,175,283,295,373,392,406,428,450,472,494,516", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,634,9536,16501,17086,21872,23109,23886,25082,26321,27548,28767,29984", "endLines": "7,19,180,288,300,378,405,427,449,471,493,515,537", "endColumns": "12,12,12,12,12,12,12,12,12,12,12,12,12", "endOffsets": "379,964,9869,16825,17412,22202,23881,25077,26316,27543,28762,29979,31245"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\edf207558568566ef6df89a74527d120\\transformed\\appcompat-1.6.1\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "385,386,387,388,389,390,391,563", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "22477,22547,22631,22715,22811,22913,23015,34011", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "22542,22626,22710,22806,22908,23010,23104,34095"}}, {"source": "D:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\src\\main\\res\\values-night\\tab_styles.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "80,86,92,98,104,110,116,122", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4354,4691,5000,5331,5628,5956,6275,6598", "endLines": "85,91,97,103,109,115,121,127", "endColumns": "12,12,12,12,12,12,12,12", "endOffsets": "4686,4995,5326,5623,5951,6270,6593,6922"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b903733d615c0b2ff05f9b9df539ba65\\transformed\\material-1.11.0\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "538,539,540,541,542,543,544,545,546,547,548,549,550,551,552,553,554,555,556,557,558,559,560,561,562,564,565,566,567,568,569,570,571", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "31250,31325,31436,31525,31626,31733,31840,31939,32046,32149,32237,32361,32463,32565,32681,32783,32897,33025,33141,33263,33399,33519,33653,33773,33885,34100,34217,34341,34471,34593,34731,34865,34981", "endColumns": "74,110,88,100,106,106,98,106,102,87,123,101,101,115,101,113,127,115,121,135,119,133,119,111,125,116,123,129,121,137,133,115,119", "endOffsets": "31320,31431,31520,31621,31728,31835,31934,32041,32144,32232,32356,32458,32560,32676,32778,32892,33020,33136,33258,33394,33514,33648,33768,33880,34006,34212,34336,34466,34588,34726,34860,34976,35096"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-mergeDebugResources-81:\\values-night-v8\\values-night-v8.xml", "map": [{"source": "D:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\src\\main\\res\\values-night\\text_input_styles.xml", "from": {"startLines": "42,58,76,67,49,3,18,34,26,10", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "2099,3009,3972,3481,2515,113,905,1715,1303,492", "endLines": "46,64,82,73,55,7,23,39,31,15", "endColumns": "12,12,12,12,12,12,12,12,12,12", "endOffsets": "2429,3393,4346,3889,2926,415,1224,2022,1641,831"}, "to": {"startLines": "26,31,38,45,52,107,112,118,124,130", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "1236,1567,1950,2323,2730,5713,6016,6335,6642,6980", "endLines": "30,37,44,51,58,111,117,123,129,135", "endColumns": "12,12,12,12,12,12,12,12,12,12", "endOffsets": "1562,1945,2318,2725,3135,6011,6330,6637,6975,7314"}}, {"source": "D:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\src\\main\\res\\values-night\\default_dialog_styles.xml", "from": {"startLines": "12,30,21,3", "startColumns": "4,4,4,4", "startOffsets": "698,1809,1245,137", "endLines": "18,36,27,9", "endColumns": "12,12,12,12", "endOffsets": "1155,2251,1724,613"}, "to": {"startLines": "153,167,174,195", "startColumns": "4,4,4,4", "startOffsets": "8267,9199,9640,11045", "endLines": "159,173,180,201", "endColumns": "12,12,12,12", "endOffsets": "8718,9635,10113,11515"}}, {"source": "D:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\src\\main\\res\\values-night\\theme_styles.xml", "from": {"startLines": "48,44,36,61,11,28,53,3,73,69,23,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2468,2328,1843,3114,572,1366,2652,105,3718,3584,1183,1047", "endLines": "50,46,42,67,17,34,59,9,75,71,25,21", "endColumns": "12,12,12,12,12,12,12,12,12,12,12,12", "endOffsets": "2597,2460,2320,3576,1039,1835,3106,564,3842,3710,1308,1175"}, "to": {"startLines": "20,23,160,181,188,209,223,230,250,253,262,265", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "969,1101,8723,10118,10579,11968,12906,13359,14613,14740,15200,15328", "endLines": "22,25,166,187,194,215,229,236,252,255,264,267", "endColumns": "12,12,12,12,12,12,12,12,12,12,12,12", "endOffsets": "1096,1231,9194,10574,11040,12431,13354,13812,14735,14864,15323,15454"}}, {"source": "D:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\src\\main\\res\\values-night\\styles.xml", "from": {"startLines": "34,30,52,48,21,39,3,16,12", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "1643,1514,2520,2382,1000,1841,122,797,658", "endLines": "36,32,54,50,27,45,9,18,14", "endColumns": "12,12,12,12,12,12,12,12,12", "endOffsets": "1766,1635,2645,2512,1449,2312,590,930,789"}, "to": {"startLines": "8,11,142,145,202,216,237,340,343", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "384,510,7657,7785,11520,12436,13817,19990,20126", "endLines": "10,13,144,147,208,222,243,342,345", "endColumns": "12,12,12,12,12,12,12,12,12", "endOffsets": "505,629,7780,7913,11963,12901,14279,20121,20255"}}, {"source": "D:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\src\\main\\res\\values-night\\popup_menu_item_styles.xml", "from": {"startLines": "3,14,26,20,8", "startColumns": "4,4,4,4,4", "startOffsets": "112,651,1259,953,348", "endLines": "5,17,29,23,11", "endColumns": "12,12,12,12,12", "endOffsets": "276,879,1482,1190,582"}, "to": {"startLines": "268,271,275,279,283", "startColumns": "4,4,4,4,4", "startOffsets": "15459,15626,15856,16081,16320", "endLines": "270,274,278,282,286", "endColumns": "12,12,12,12,12", "endOffsets": "15621,15851,16076,16315,16551"}}, {"source": "D:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\src\\main\\res\\values-night\\popup_styles.xml", "from": {"startLines": "66,43,53,61,57,49,3,19,35,27,11", "startColumns": "4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3603,2490,2993,3370,3177,2803,107,1064,2037,1544,577", "endLines": "70,47,55,63,59,51,8,24,40,32,16", "endColumns": "12,12,12,12,12,12,12,12,12,12,12", "endOffsets": "3951,2795,3169,3541,3362,2985,510,1475,2438,1973,1000"}, "to": {"startLines": "148,287,292,295,298,301,304,310,316,322,328", "startColumns": "4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7918,16556,16862,17041,17215,17403,17588,17991,18402,18803,19232", "endLines": "152,291,294,297,300,303,309,315,321,327,333", "endColumns": "12,12,12,12,12,12,12,12,12,12,12", "endOffsets": "8262,16857,17036,17210,17398,17583,17986,18397,18798,19227,19650"}}, {"source": "D:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\src\\main\\res\\values-night\\themes.xml", "from": {"startLines": "68,155,97,184,126,39,2,47,134,76,163,105,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3671,8370,5239,9923,6797,2133,57,2509,7172,4046,8746,5619,913", "endLines": "73,160,102,189,131,44,15,66,153,95,182,124,37", "endColumns": "12,12,12,12,12,12,12,12,12,12,12,12,12", "endOffsets": "4000,8705,5577,10252,7128,2468,869,3663,8362,5231,9915,6789,2125"}, "to": {"startLines": "2,14,136,244,256,334,353,367,387,407,427,447,467", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,634,7319,14284,14869,19655,20892,21669,22755,23877,24994,26095,27197", "endLines": "7,19,141,249,261,339,366,386,406,426,446,466,486", "endColumns": "12,12,12,12,12,12,12,12,12,12,12,12,12", "endOffsets": "379,964,7652,14608,15195,19985,21664,22750,23872,24989,26090,27192,28336"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\edf207558568566ef6df89a74527d120\\transformed\\appcompat-1.6.1\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,125,209,293,389,491,593,687", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "120,204,288,384,486,588,682,771"}, "to": {"startLines": "346,347,348,349,350,351,352,512", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "20260,20330,20414,20498,20594,20696,20798,31102", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "20325,20409,20493,20589,20691,20793,20887,31186"}}, {"source": "D:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\src\\main\\res\\values-night\\tab_styles.xml", "from": {"startLines": "3,19,51,35,27,59,43,11", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "107,904,2426,1674,1282,2821,2036,511", "endLines": "8,24,56,40,32,64,48,16", "endColumns": "12,12,12,12,12,12,12,12", "endOffsets": "444,1213,2757,1971,1610,3140,2359,840"}, "to": {"startLines": "59,65,71,77,83,89,95,101", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3140,3477,3786,4117,4414,4742,5061,5384", "endLines": "64,70,76,82,88,94,100,106", "endColumns": "12,12,12,12,12,12,12,12", "endOffsets": "3472,3781,4112,4409,4737,5056,5379,5708"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b903733d615c0b2ff05f9b9df539ba65\\transformed\\material-1.11.0\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,241,330,431,538,645,744,851,954,1042,1166,1268,1370,1486,1588,1702,1830,1946,2068,2204,2324,2458,2578,2690,2816,2933,3057,3187,3309,3447,3581,3697", "endColumns": "74,110,88,100,106,106,98,106,102,87,123,101,101,115,101,113,127,115,121,135,119,133,119,111,125,116,123,129,121,137,133,115,119", "endOffsets": "125,236,325,426,533,640,739,846,949,1037,1161,1263,1365,1481,1583,1697,1825,1941,2063,2199,2319,2453,2573,2685,2811,2928,3052,3182,3304,3442,3576,3692,3812"}, "to": {"startLines": "487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,513,514,515,516,517,518,519,520", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "28341,28416,28527,28616,28717,28824,28931,29030,29137,29240,29328,29452,29554,29656,29772,29874,29988,30116,30232,30354,30490,30610,30744,30864,30976,31191,31308,31432,31562,31684,31822,31956,32072", "endColumns": "74,110,88,100,106,106,98,106,102,87,123,101,101,115,101,113,127,115,121,135,119,133,119,111,125,116,123,129,121,137,133,115,119", "endOffsets": "28411,28522,28611,28712,28819,28926,29025,29132,29235,29323,29447,29549,29651,29767,29869,29983,30111,30227,30349,30485,30605,30739,30859,30971,31097,31303,31427,31557,31679,31817,31951,32067,32187"}}]}]}