package com.atom.diesnookerapp.data.firebase

import com.atom.diesnookerapp.ui.selbsteinschaetzung.DailyTrainingRecord
import com.atom.diesnookerapp.ui.selbsteinschaetzung.TrainingAssessmentItem
import com.atom.diesnookerapp.ui.selbsteinschaetzung.TrainingType

/**
 * Firebase model for training records
 */
data class FirebaseTrainingRecord(
    var date: String = "", // ISO format date string
    var type: String = "", // Enum name as string
    var items: List<FirebaseTrainingAssessmentItem> = emptyList()
) : FirebaseModel() {

    companion object {
        fun fromDailyTrainingRecord(record: DailyTrainingRecord, userId: String): FirebaseTrainingRecord {
            return FirebaseTrainingRecord(
                date = record.date.toString(),
                type = record.type.name,
                items = record.items.map { FirebaseTrainingAssessmentItem.fromTrainingAssessmentItem(it) }
            ).apply {
                this.userId = userId
                this.lastUpdated = System.currentTimeMillis()
            }
        }
    }

    fun toDailyTrainingRecord(): DailyTrainingRecord {
        return DailyTrainingRecord(
            date = org.threeten.bp.LocalDate.parse(date),
            type = TrainingType.valueOf(type),
            items = items.map { it.toTrainingAssessmentItem() }
        )
    }
}

/**
 * Firebase model for training assessment items
 */
data class FirebaseTrainingAssessmentItem(
    var id: Int = 0,
    var title: String = "",
    var score: Int? = null
) {
    companion object {
        fun fromTrainingAssessmentItem(item: TrainingAssessmentItem): FirebaseTrainingAssessmentItem {
            return FirebaseTrainingAssessmentItem(
                id = item.id,
                title = item.title,
                score = item.score
            )
        }
    }

    fun toTrainingAssessmentItem(): TrainingAssessmentItem {
        return TrainingAssessmentItem(
            id = id,
            title = title,
            score = score
        )
    }
}
