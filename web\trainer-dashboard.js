// Firebase configuration
const firebaseConfig = {
    apiKey: "AIzaSyB-fFmAiherpGg3dbecT3v_Z-368kSEMPY",
    authDomain: "die-snooker-app.firebaseapp.com",
    projectId: "die-snooker-app",
    storageBucket: "die-snooker-app.firebasestorage.app",
    messagingSenderId: "547283642216",
    appId: "1:547283642216:web:7f2fdc23dab5ce8430d8dd",
    measurementId: "G-GTFVZZ4LJ"
};

// Initialize Firebase
firebase.initializeApp(firebaseConfig);
const auth = firebase.auth();
const db = firebase.firestore();

// DOM Elements
const loginContainer = document.getElementById('login-container');
const appContainer = document.getElementById('app-container');
const loginForm = document.getElementById('login-form');
const userEmail = document.getElementById('user-email');
const userRole = document.getElementById('user-role');
const logoutBtn = document.getElementById('logout-btn');
const navLinks = document.querySelectorAll('nav a');
const sections = document.querySelectorAll('section');
const trainerTypeDisplay = document.getElementById('trainer-type-display').querySelector('span');
const athletesList = document.getElementById('athletes-list');
const emptyAthletes = document.getElementById('empty-athletes');
const refreshBtn = document.getElementById('refresh-btn');
const backToAthletesBtn = document.getElementById('back-to-athletes');
const athleteDetailSection = document.getElementById('athlete-detail-section');
const athletesSection = document.getElementById('athletes-section');
const detailAthleteName = document.getElementById('detail-athlete-name');
const detailAthleteEmail = document.getElementById('detail-athlete-email');
const trainerCharts = document.getElementById('trainer-charts');
const mentalTrainerCharts = document.getElementById('mental-trainer-charts');
const dateRangePicker = document.getElementById('date-range-picker');
const exerciseTypeSelect = document.getElementById('exercise-type-select');
const exerciseTypeFilter = document.getElementById('exercise-type-filter');
const profileName = document.getElementById('profile-name');
const profileEmail = document.getElementById('profile-email');
const profileRole = document.getElementById('profile-role');
const profileExperience = document.getElementById('profile-experience');
const saveProfileBtn = document.getElementById('save-profile-btn');

// Chart instances
let completionRateChart = null;
let trainingsplanAdherenceChart = null;
let performanceTrendsChart = null;
let selfAssessmentChart = null;
let questionAnalysisChart = null;
let emotionalStateChart = null;

// Current user and selected athlete
let currentUser = null;
let currentUserProfile = null;
let selectedAthlete = null;
let selectedDateRange = {
    startDate: moment().subtract(30, 'days'),
    endDate: moment()
};

// Event Listeners
if (loginForm) {
    loginForm.addEventListener('submit', handleLogin);
}
if (logoutBtn) {
    logoutBtn.addEventListener('click', handleLogout);
}
if (navLinks) {
    navLinks.forEach(link => {
        link.addEventListener('click', handleNavigation);
    });
}
if (refreshBtn) {
    refreshBtn.addEventListener('click', loadAthletes);
}
if (backToAthletesBtn) {
    backToAthletesBtn.addEventListener('click', showAthletesList);
}
if (saveProfileBtn) {
    saveProfileBtn.addEventListener('click', saveProfile);
}

// Initialize date range picker
if (dateRangePicker) {
    $(dateRangePicker).daterangepicker({
        startDate: selectedDateRange.startDate,
        endDate: selectedDateRange.endDate,
        locale: {
            format: 'DD.MM.YYYY'
        },
        ranges: {
            'Letzte 7 Tage': [moment().subtract(6, 'days'), moment()],
            'Letzte 30 Tage': [moment().subtract(29, 'days'), moment()],
            'Dieser Monat': [moment().startOf('month'), moment().endOf('month')],
            'Letzter Monat': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')],
            'Letzte 3 Monate': [moment().subtract(3, 'month'), moment()],
            'Letzte 6 Monate': [moment().subtract(6, 'month'), moment()]
        }
    }, function(start, end) {
        selectedDateRange.startDate = start;
        selectedDateRange.endDate = end;
        if (selectedAthlete) {
            loadAthleteData(selectedAthlete);
        }
    });
}

// Check auth state
auth.onAuthStateChanged(user => {
    if (user) {
        // User is signed in
        console.log("User signed in:", user.email);
        currentUser = user;

        // Check if email is verified
        if (!user.emailVerified) {
            alert('Bitte bestätigen Sie Ihre E-Mail-Adresse, um fortzufahren.');
            auth.signOut();
            return;
        }

        // Check if user is a trainer
        checkUserRole(user)
            .then(isTrainer => {
                if (isTrainer) {
                    console.log("User is a trainer, showing trainer dashboard");
                    showApp(user);
                    loadAthletes();
                    loadProfile();
                } else {
                    console.log("User is not a trainer, redirecting to main app");
                    alert('Nur Trainer können auf dieses Dashboard zugreifen.');
                    // Redirect to main app
                    window.location.href = 'index.html';
                }
            })
            .catch(error => {
                console.error("Error checking user role:", error);
                alert('Fehler beim Überprüfen der Benutzerrolle. Bitte versuchen Sie es später erneut.');
                auth.signOut();
            });
    } else {
        // User is signed out
        console.log("User signed out");
        showLogin();
    }
});

// Functions
function handleLogin(e) {
    e.preventDefault();

    const email = document.getElementById('email').value;
    const password = document.getElementById('password').value;

    // Show loading indicator
    const loginButton = document.querySelector('#login-form button');
    const originalButtonText = loginButton.textContent;
    loginButton.textContent = "Anmeldung läuft...";
    loginButton.disabled = true;

    auth.signInWithEmailAndPassword(email, password)
        .then(userCredential => {
            console.log("Login successful");
        })
        .catch(error => {
            console.error("Login failed:", error);
            alert(`Anmeldung fehlgeschlagen: ${error.message}`);

            // Reset button
            loginButton.textContent = originalButtonText;
            loginButton.disabled = false;
        });
}

function handleLogout() {
    auth.signOut();
}

function showLogin() {
    loginContainer.classList.remove('hidden');
    appContainer.classList.add('hidden');
}

function showApp(user) {
    loginContainer.classList.add('hidden');
    appContainer.classList.remove('hidden');
    userEmail.textContent = user.email;
}

function handleNavigation(e) {
    e.preventDefault();

    // Remove active class from all links
    navLinks.forEach(link => {
        link.classList.remove('active');
    });

    // Add active class to clicked link
    e.target.classList.add('active');

    // Hide all sections
    sections.forEach(section => {
        section.classList.remove('active');
        section.classList.add('hidden');
    });

    // Show selected section
    const sectionId = `${e.target.dataset.section}-section`;
    const section = document.getElementById(sectionId);
    if (section) {
        section.classList.add('active');
        section.classList.remove('hidden');
    }
}

// Check if user is a trainer
async function checkUserRole(user) {
    try {
        const doc = await db.collection('user_profiles').doc(user.uid).get();

        if (doc.exists) {
            const profile = doc.data();
            currentUserProfile = { ...profile, id: doc.id };

            // Update UI with role
            const roleName = getRoleDisplayName(profile.role);
            userRole.textContent = roleName;
            trainerTypeDisplay.textContent = roleName;

            return profile.role === 'TRAINER' || profile.role === 'MENTAL_TRAINER';
        } else {
            console.log('No user profile found');
            return false;
        }
    } catch (error) {
        console.error('Error checking user role:', error);
        throw error;
    }
}

// Load athletes connected to the trainer
async function loadAthletes() {
    if (!currentUser) return;

    try {
        // Show loading state
        athletesList.innerHTML = '<div class="loading">Loading...</div>';

        // Get connections where the trainer is the target
        const snapshot = await db.collection('user_connections')
            .where('targetId', '==', currentUser.uid)
            .where('status', '==', 'ACTIVE')
            .get();

        // Clear athletes list
        athletesList.innerHTML = '';

        if (snapshot.empty) {
            emptyAthletes.style.display = 'block';
            return;
        }

        emptyAthletes.style.display = 'none';

        // Process each connection
        const athletePromises = snapshot.docs.map(async doc => {
            const connection = doc.data();

            // Get athlete profile
            const athleteDoc = await db.collection('user_profiles').doc(connection.initiatorId).get();
            if (!athleteDoc.exists) return null;

            const athlete = athleteDoc.data();

            // Get athlete stats
            const stats = await getAthleteStats(connection.initiatorId, connection);

            return {
                id: connection.initiatorId,
                name: athlete.displayName || athlete.email.split('@')[0],
                email: athlete.email,
                connection: connection,
                stats: stats,
                lastActive: athlete.lastUpdated || 0
            };
        });

        // Wait for all promises to resolve
        const athletes = (await Promise.all(athletePromises)).filter(a => a !== null);

        // Sort athletes by last active date (most recent first)
        athletes.sort((a, b) => b.lastActive - a.lastActive);

        // Render athletes
        athletes.forEach(athlete => {
            renderAthleteCard(athlete);
        });

    } catch (error) {
        console.error('Error loading athletes:', error);
        athletesList.innerHTML = `<div class="error">Fehler beim Laden der Athleten: ${error.message}</div>`;
    }
}

// Get athlete statistics based on connection type
async function getAthleteStats(athleteId, connection) {
    const stats = {
        exerciseCount: 0,
        completionRate: 0,
        trainingsplanAdherence: 0,
        selfAssessmentScore: 0,
        questionCount: 0
    };

    try {
        // For regular trainers, get exercise and trainingsplan stats
        if (connection.trainerAccess) {
            // Get exercise records count
            const exerciseSnapshot = await db.collection('exercise_records')
                .where('userId', '==', athleteId)
                .get();

            stats.exerciseCount = exerciseSnapshot.size;

            // Calculate completion rate from exercise records
            if (exerciseSnapshot.size > 0) {
                let completed = 0;
                let total = 0;

                exerciseSnapshot.forEach(doc => {
                    const record = doc.data();
                    if (record.completed) completed++;
                    total++;
                });

                stats.completionRate = total > 0 ? Math.round((completed / total) * 100) : 0;
            }

            // Get trainingsplan adherence
            const trainingsplanSnapshot = await db.collection('trainingsplan_history')
                .where('userId', '==', athleteId)
                .orderBy('lastUpdated', 'desc')
                .limit(1)
                .get();

            if (!trainingsplanSnapshot.empty) {
                const trainingsplan = trainingsplanSnapshot.docs[0].data();
                if (trainingsplan.items && trainingsplan.items.length > 0) {
                    const checkedItems = trainingsplan.items.filter(item => item.isChecked).length;
                    stats.trainingsplanAdherence = Math.round((checkedItems / trainingsplan.items.length) * 100);
                }
            }
        }

        // For mental trainers, get self-assessment stats
        if (connection.mentalTrainerAccess) {
            // Get training records (self-assessment)
            const trainingSnapshot = await db.collection('training_records')
                .where('userId', '==', athleteId)
                .get();

            if (trainingSnapshot.size > 0) {
                let totalScore = 0;
                let scoreCount = 0;

                trainingSnapshot.forEach(doc => {
                    const record = doc.data();
                    if (record.items && record.items.length > 0) {
                        record.items.forEach(item => {
                            if (item.score) {
                                totalScore += item.score;
                                scoreCount++;
                            }
                        });
                    }
                });

                stats.selfAssessmentScore = scoreCount > 0 ? Math.round((totalScore / scoreCount) * 10) / 10 : 0;
            }

            // Get question records count
            const questionSnapshot = await db.collection('question_records')
                .where('userId', '==', athleteId)
                .get();

            stats.questionCount = questionSnapshot.size;
        }

        return stats;
    } catch (error) {
        console.error('Error getting athlete stats:', error);
        return stats;
    }
}

// Render athlete card
function renderAthleteCard(athlete) {
    const card = document.createElement('div');
    card.className = 'athlete-card';
    card.dataset.id = athlete.id;

    // Format last active date
    const lastActive = athlete.lastActive ? moment(athlete.lastActive).format('DD.MM.YYYY HH:mm') : 'Nie';

    // Determine which stats to show based on connection permissions
    let statsHtml = '';

    if (athlete.connection.trainerAccess) {
        statsHtml += `
            <div class="stat-item">
                <div class="stat-value">${athlete.stats.exerciseCount}</div>
                <div class="stat-label">Übungen</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">${athlete.stats.completionRate}%</div>
                <div class="stat-label">Abschlussrate</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">${athlete.stats.trainingsplanAdherence}%</div>
                <div class="stat-label">Trainingsplan</div>
            </div>
        `;
    }

    if (athlete.connection.mentalTrainerAccess) {
        statsHtml += `
            <div class="stat-item">
                <div class="stat-value">${athlete.stats.selfAssessmentScore}</div>
                <div class="stat-label">Ø Bewertung</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">${athlete.stats.questionCount}</div>
                <div class="stat-label">Fragen</div>
            </div>
        `;
    }

    card.innerHTML = `
        <div class="athlete-name">${athlete.name}</div>
        <div class="athlete-email">${athlete.email}</div>
        <div class="athlete-stats">
            ${statsHtml}
        </div>
        <div class="athlete-last-active">Zuletzt aktiv: ${lastActive}</div>
    `;

    // Add click event to show athlete details
    card.addEventListener('click', () => {
        showAthleteDetails(athlete);
    });

    athletesList.appendChild(card);
}

// Show athlete details
function showAthleteDetails(athlete) {
    selectedAthlete = athlete;

    // Update UI
    detailAthleteName.textContent = athlete.name;
    detailAthleteEmail.textContent = athlete.email;

    // Show appropriate charts based on trainer type
    if (athlete.connection.trainerAccess) {
        trainerCharts.style.display = 'block';
        exerciseTypeFilter.style.display = 'block';
    } else {
        trainerCharts.style.display = 'none';
        exerciseTypeFilter.style.display = 'none';
    }

    if (athlete.connection.mentalTrainerAccess) {
        mentalTrainerCharts.style.display = 'block';
    } else {
        mentalTrainerCharts.style.display = 'none';
    }

    // Log data access
    logDataAccess(athlete.id);

    // Load athlete data
    loadAthleteData(athlete);

    // Show athlete detail section
    athletesSection.classList.remove('active');
    athletesSection.classList.add('hidden');
    athleteDetailSection.classList.remove('hidden');
    athleteDetailSection.classList.add('active');
}

// Log data access for audit purposes
async function logDataAccess(athleteId) {
    if (!currentUser || !currentUserProfile) return;

    try {
        // Create access log
        await db.collection('access_logs').add({
            trainerId: currentUser.uid,
            trainerEmail: currentUser.email,
            trainerRole: currentUserProfile.role,
            dataOwnerId: athleteId,
            timestamp: firebase.firestore.FieldValue.serverTimestamp(),
            accessType: currentUserProfile.role === 'TRAINER' ? 'EXERCISE_DATA' : 'SELF_ASSESSMENT_DATA',
            userAgent: navigator.userAgent
        });

        console.log('Data access logged successfully');
    } catch (error) {
        console.error('Error logging data access:', error);
        // Continue even if logging fails
    }
}

// Show athletes list
function showAthletesList() {
    athleteDetailSection.classList.remove('active');
    athleteDetailSection.classList.add('hidden');
    athletesSection.classList.remove('hidden');
    athletesSection.classList.add('active');

    // Clear selected athlete
    selectedAthlete = null;
}

// Load athlete data for charts
async function loadAthleteData(athlete) {
    if (!athlete) return;

    try {
        // Convert date range to timestamps
        const startTimestamp = selectedDateRange.startDate.valueOf();
        const endTimestamp = selectedDateRange.endDate.valueOf();

        // Load data based on trainer type
        if (athlete.connection.trainerAccess) {
            await loadExerciseTypes(athlete.id);
            await loadExerciseData(athlete.id, startTimestamp, endTimestamp);
            await loadTrainingsplanData(athlete.id, startTimestamp, endTimestamp);
        }

        if (athlete.connection.mentalTrainerAccess) {
            await loadSelfAssessmentData(athlete.id, startTimestamp, endTimestamp);
            await loadQuestionData(athlete.id, startTimestamp, endTimestamp);
        }

    } catch (error) {
        console.error('Error loading athlete data:', error);
        alert(`Fehler beim Laden der Athletendaten: ${error.message}`);
    }
}

// Load exercise types for filter
async function loadExerciseTypes(athleteId) {
    try {
        const snapshot = await db.collection('exercise_records')
            .where('userId', '==', athleteId)
            .get();

        // Clear select options except "All"
        while (exerciseTypeSelect.options.length > 1) {
            exerciseTypeSelect.remove(1);
        }

        if (snapshot.empty) return;

        // Extract unique exercise types
        const exerciseTypes = new Set();

        snapshot.forEach(doc => {
            const record = doc.data();
            if (record.exerciseId) {
                exerciseTypes.add(record.exerciseId);
            }
        });

        // Add options to select
        exerciseTypes.forEach(type => {
            const option = document.createElement('option');
            option.value = type;
            option.textContent = getExerciseDisplayName(type);
            exerciseTypeSelect.appendChild(option);
        });

        // Add change event listener
        exerciseTypeSelect.addEventListener('change', () => {
            if (selectedAthlete) {
                loadExerciseData(selectedAthlete.id, selectedDateRange.startDate.valueOf(), selectedDateRange.endDate.valueOf());
            }
        });

    } catch (error) {
        console.error('Error loading exercise types:', error);
    }
}

// Get display name for exercise ID
function getExerciseDisplayName(exerciseId) {
    // Map of exercise IDs to display names
    const exerciseNames = {
        't_break': 'T Break',
        'y_break': 'Y Break',
        'line_up': 'Line Up',
        'eight_reds': '8reds Break',
        'five_reds': '5reds clear',
        'fifty_plus': '50+ Break',
        'frames': 'Frames',
        'x_break': 'X Break',
        'black_routines': 'Black Routines',
        'brown_to_reds': 'Brown to reds',
        'bc_to_reds': 'BC to reds',
        'pj_routines': 'PJ Routines',
        'random_bilder': 'Random Bilder',
        'blue_to_pink': 'Blau zu Pink',
        'long_shots': '8 Long shots',
        'medium_to_blue': 'Medium zu Blau',
        'rest': 'Rest',
        'three_reds_save': '3reds Save',
        'snooker_legen': 'Snooker legen',
        'black_to_bc': 'Von Schwarz zu BC',
        'blue_pink_black': 'Blau pink Schwarz',
        'yellow_splits': 'Gelb splits',
        'green': 'Grün',
        'brown': 'Braun',
        'blue': 'Blau',
        'black': 'Schwarz',
        'blue_doubletouch': 'Blue Doubletouch',
        'langer_stoss': 'Langer Stoß',
        'gerader_stoss_2_kreiden': 'Gerader Stoß 2 Kreiden',
        'stellungsspiel_gelb': 'Gelb',
        'stellungsspiel_gruen': 'Grün',
        'stellungsspiel_braun': 'Braun',
        'blau_durch_bc': 'Blau durch BC',
        'hohe_schwarze': 'Hohe Schwarze'
    };

    return exerciseNames[exerciseId] || exerciseId;
}

// Load exercise data for charts
async function loadExerciseData(athleteId, startTimestamp, endTimestamp) {
    try {
        // Get selected exercise type
        const selectedExerciseType = exerciseTypeSelect.value;

        // Query exercise records
        let query = db.collection('exercise_records')
            .where('userId', '==', athleteId)
            .where('lastUpdated', '>=', startTimestamp)
            .where('lastUpdated', '<=', endTimestamp);

        // Add exercise type filter if not "all"
        if (selectedExerciseType !== 'all') {
            query = query.where('exerciseId', '==', selectedExerciseType);
        }

        const snapshot = await query.orderBy('lastUpdated', 'asc').get();

        if (snapshot.empty) {
            // No data, show empty chart
            renderCompletionRateChart([], []);
            renderPerformanceTrendsChart([], [], []);
            return;
        }

        // Process data for charts
        const dateLabels = [];
        const completionRates = [];
        const performanceData = {};

        // Group by date
        const recordsByDate = {};

        snapshot.forEach(doc => {
            const record = doc.data();
            const date = moment(record.lastUpdated).format('DD.MM.YYYY');

            if (!recordsByDate[date]) {
                recordsByDate[date] = [];
            }

            recordsByDate[date].push(record);

            // Track exercise types for performance trends
            if (record.exerciseId && !performanceData[record.exerciseId]) {
                performanceData[record.exerciseId] = {
                    label: getExerciseDisplayName(record.exerciseId),
                    data: [],
                    dates: []
                };
            }

            // Add performance data if available
            if (record.exerciseId && record.score !== undefined) {
                performanceData[record.exerciseId].data.push(record.score);
                performanceData[record.exerciseId].dates.push(date);
            }
        });

        // Calculate completion rate by date
        Object.entries(recordsByDate).forEach(([date, records]) => {
            dateLabels.push(date);

            const completed = records.filter(r => r.completed).length;
            const total = records.length;
            const rate = total > 0 ? (completed / total) * 100 : 0;

            completionRates.push(rate);
        });

        // Render charts
        renderCompletionRateChart(dateLabels, completionRates);

        // Prepare performance trends data
        const datasets = Object.values(performanceData).map((data, index) => {
            // Generate a color based on index
            const hue = (index * 137) % 360; // Golden angle approximation for good distribution
            const color = `hsl(${hue}, 70%, 60%)`;

            return {
                label: data.label,
                data: data.data,
                borderColor: color,
                backgroundColor: color + '20', // Add transparency
                tension: 0.2,
                fill: false,
                pointRadius: 4
            };
        });

        // Get all unique dates for performance chart
        const allDates = [...new Set(Object.values(performanceData).flatMap(d => d.dates))].sort();

        renderPerformanceTrendsChart(allDates, datasets);

    } catch (error) {
        console.error('Error loading exercise data:', error);
    }
}

// Load trainingsplan data for charts
async function loadTrainingsplanData(athleteId, startTimestamp, endTimestamp) {
    try {
        const snapshot = await db.collection('trainingsplan_history')
            .where('userId', '==', athleteId)
            .where('lastUpdated', '>=', startTimestamp)
            .where('lastUpdated', '<=', endTimestamp)
            .orderBy('lastUpdated', 'asc')
            .get();

        if (snapshot.empty) {
            // No data, show empty chart
            renderTrainingsplanAdherenceChart([], []);
            return;
        }

        // Process data for chart
        const dateLabels = [];
        const adherenceRates = [];

        snapshot.forEach(doc => {
            const trainingsplan = doc.data();
            const date = moment(trainingsplan.lastUpdated).format('DD.MM.YYYY');

            dateLabels.push(date);

            if (trainingsplan.items && trainingsplan.items.length > 0) {
                const checkedItems = trainingsplan.items.filter(item => item.isChecked).length;
                const adherenceRate = Math.round((checkedItems / trainingsplan.items.length) * 100);
                adherenceRates.push(adherenceRate);
            } else {
                adherenceRates.push(0);
            }
        });

        // Render chart
        renderTrainingsplanAdherenceChart(dateLabels, adherenceRates);

    } catch (error) {
        console.error('Error loading trainingsplan data:', error);
    }
}

// Load self-assessment data for charts
async function loadSelfAssessmentData(athleteId, startTimestamp, endTimestamp) {
    try {
        const snapshot = await db.collection('training_records')
            .where('userId', '==', athleteId)
            .where('lastUpdated', '>=', startTimestamp)
            .where('lastUpdated', '<=', endTimestamp)
            .orderBy('lastUpdated', 'asc')
            .get();

        if (snapshot.empty) {
            // No data, show empty chart
            renderSelfAssessmentChart([], []);
            renderEmotionalStateChart([], []);
            return;
        }

        // Process data for charts
        const dateLabels = [];
        const avgScores = [];
        const emotionalStates = {
            'Vor Training': [],
            'Nach Training': []
        };

        snapshot.forEach(doc => {
            const record = doc.data();
            const date = moment(record.lastUpdated).format('DD.MM.YYYY');

            // Skip if no items
            if (!record.items || record.items.length === 0) return;

            // Calculate average score
            let totalScore = 0;
            let scoreCount = 0;

            record.items.forEach(item => {
                if (item.score !== undefined) {
                    totalScore += item.score;
                    scoreCount++;
                }
            });

            if (scoreCount > 0) {
                const avgScore = Math.round((totalScore / scoreCount) * 10) / 10;

                dateLabels.push(date);
                avgScores.push(avgScore);

                // Track emotional state by training type
                if (record.type === 'VOR_TRAINING') {
                    emotionalStates['Vor Training'].push({
                        date: date,
                        score: avgScore
                    });
                } else if (record.type === 'NACH_TRAINING') {
                    emotionalStates['Nach Training'].push({
                        date: date,
                        score: avgScore
                    });
                }
            }
        });

        // Render self-assessment chart
        renderSelfAssessmentChart(dateLabels, avgScores);

        // Prepare emotional state data
        const datasets = Object.entries(emotionalStates).map(([label, data], index) => {
            const color = index === 0 ? '#4a90e2' : '#e27c4a';

            return {
                label: label,
                data: data.map(d => ({ x: d.date, y: d.score })),
                borderColor: color,
                backgroundColor: color + '20',
                tension: 0.2,
                fill: false,
                pointRadius: 4
            };
        });

        // Get all unique dates for emotional state chart
        const allDates = [...new Set([
            ...emotionalStates['Vor Training'].map(d => d.date),
            ...emotionalStates['Nach Training'].map(d => d.date)
        ])].sort();

        renderEmotionalStateChart(allDates, datasets);

    } catch (error) {
        console.error('Error loading self-assessment data:', error);
    }
}

// Load question data for charts
async function loadQuestionData(athleteId, startTimestamp, endTimestamp) {
    try {
        const snapshot = await db.collection('question_records')
            .where('userId', '==', athleteId)
            .where('lastUpdated', '>=', startTimestamp)
            .where('lastUpdated', '<=', endTimestamp)
            .orderBy('lastUpdated', 'asc')
            .get();

        if (snapshot.empty) {
            // No data, show empty chart
            renderQuestionAnalysisChart([], []);
            return;
        }

        // Process data for chart
        const questionCounts = {};

        snapshot.forEach(doc => {
            const record = doc.data();

            // Skip if no questions
            if (!record.questions || record.questions.length === 0) return;

            // Count questions by title
            record.questions.forEach(question => {
                if (!question.title) return;

                if (!questionCounts[question.title]) {
                    questionCounts[question.title] = 0;
                }

                questionCounts[question.title]++;
            });
        });

        // Sort questions by count (descending)
        const sortedQuestions = Object.entries(questionCounts)
            .sort((a, b) => b[1] - a[1])
            .slice(0, 10); // Show top 10 questions

        const labels = sortedQuestions.map(([title]) => title);
        const counts = sortedQuestions.map(([, count]) => count);

        // Render chart
        renderQuestionAnalysisChart(labels, counts);

    } catch (error) {
        console.error('Error loading question data:', error);
    }
}

// Render completion rate chart
function renderCompletionRateChart(labels, data) {
    const ctx = document.getElementById('completion-rate-chart').getContext('2d');

    if (completionRateChart) {
        completionRateChart.destroy();
    }

    completionRateChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,
            datasets: [{
                label: 'Abschlussrate (%)',
                data: data,
                borderColor: '#4a90e2',
                backgroundColor: 'rgba(74, 144, 226, 0.1)',
                tension: 0.2,
                fill: true,
                pointRadius: 4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: true,
                    position: 'top'
                },
                tooltip: {
                    mode: 'index',
                    intersect: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100,
                    title: {
                        display: true,
                        text: 'Abschlussrate (%)'
                    }
                },
                x: {
                    title: {
                        display: true,
                        text: 'Datum'
                    }
                }
            }
        }
    });
}

// Render trainingsplan adherence chart
function renderTrainingsplanAdherenceChart(labels, data) {
    const ctx = document.getElementById('trainingsplan-adherence-chart').getContext('2d');

    if (trainingsplanAdherenceChart) {
        trainingsplanAdherenceChart.destroy();
    }

    trainingsplanAdherenceChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: labels,
            datasets: [{
                label: 'Trainingsplan-Einhaltung (%)',
                data: data,
                backgroundColor: 'rgba(101, 186, 105, 0.7)',
                borderColor: 'rgba(101, 186, 105, 1)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: true,
                    position: 'top'
                },
                tooltip: {
                    mode: 'index',
                    intersect: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100,
                    title: {
                        display: true,
                        text: 'Einhaltung (%)'
                    }
                },
                x: {
                    title: {
                        display: true,
                        text: 'Datum'
                    }
                }
            }
        }
    });
}

// Render performance trends chart
function renderPerformanceTrendsChart(labels, datasets) {
    const ctx = document.getElementById('performance-trends-chart').getContext('2d');

    if (performanceTrendsChart) {
        performanceTrendsChart.destroy();
    }

    performanceTrendsChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,
            datasets: datasets
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: true,
                    position: 'top'
                },
                tooltip: {
                    mode: 'index',
                    intersect: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Leistung'
                    }
                },
                x: {
                    title: {
                        display: true,
                        text: 'Datum'
                    }
                }
            }
        }
    });
}

// Render self-assessment chart
function renderSelfAssessmentChart(labels, data) {
    const ctx = document.getElementById('self-assessment-chart').getContext('2d');

    if (selfAssessmentChart) {
        selfAssessmentChart.destroy();
    }

    selfAssessmentChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,
            datasets: [{
                label: 'Selbsteinschätzung (Ø)',
                data: data,
                borderColor: '#9c27b0',
                backgroundColor: 'rgba(156, 39, 176, 0.1)',
                tension: 0.2,
                fill: true,
                pointRadius: 4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: true,
                    position: 'top'
                },
                tooltip: {
                    mode: 'index',
                    intersect: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    max: 10,
                    title: {
                        display: true,
                        text: 'Durchschnittliche Bewertung'
                    }
                },
                x: {
                    title: {
                        display: true,
                        text: 'Datum'
                    }
                }
            }
        }
    });
}

// Render question analysis chart
function renderQuestionAnalysisChart(labels, data) {
    const ctx = document.getElementById('question-analysis-chart').getContext('2d');

    if (questionAnalysisChart) {
        questionAnalysisChart.destroy();
    }

    // Generate colors for each bar
    const colors = labels.map((_, index) => {
        const hue = (index * 137) % 360;
        return `hsl(${hue}, 70%, 60%)`;
    });

    questionAnalysisChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: labels,
            datasets: [{
                label: 'Anzahl der Antworten',
                data: data,
                backgroundColor: colors,
                borderColor: colors.map(color => color.replace('60%', '70%')),
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            indexAxis: 'y',
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    mode: 'index',
                    intersect: false
                }
            },
            scales: {
                x: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Anzahl'
                    }
                },
                y: {
                    title: {
                        display: true,
                        text: 'Frage'
                    }
                }
            }
        }
    });
}

// Render emotional state chart
function renderEmotionalStateChart(labels, datasets) {
    const ctx = document.getElementById('emotional-state-chart').getContext('2d');

    if (emotionalStateChart) {
        emotionalStateChart.destroy();
    }

    emotionalStateChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,
            datasets: datasets
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: true,
                    position: 'top'
                },
                tooltip: {
                    mode: 'index',
                    intersect: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    max: 10,
                    title: {
                        display: true,
                        text: 'Emotionaler Zustand'
                    }
                },
                x: {
                    title: {
                        display: true,
                        text: 'Datum'
                    }
                }
            }
        }
    });
}

// Load profile data
function loadProfile() {
    if (!currentUserProfile) return;

    profileName.value = currentUserProfile.displayName || '';
    profileEmail.value = currentUserProfile.email || '';
    profileRole.value = getRoleDisplayName(currentUserProfile.role) || '';
    profileExperience.value = currentUserProfile.experience || '';
}

// Save profile data
async function saveProfile() {
    if (!currentUser || !currentUserProfile) return;

    try {
        // Show loading state
        saveProfileBtn.disabled = true;
        saveProfileBtn.textContent = 'Speichern...';

        // Update profile
        await db.collection('user_profiles').doc(currentUser.uid).update({
            experience: profileExperience.value,
            lastUpdated: firebase.firestore.FieldValue.serverTimestamp()
        });

        // Update local profile
        currentUserProfile.experience = profileExperience.value;

        // Show success message
        alert('Profil erfolgreich gespeichert.');

    } catch (error) {
        console.error('Error saving profile:', error);
        alert(`Fehler beim Speichern des Profils: ${error.message}`);
    } finally {
        // Reset button
        saveProfileBtn.disabled = false;
        saveProfileBtn.textContent = 'Speichern';
    }
}

// Helper function to get role display name
function getRoleDisplayName(role) {
    switch (role) {
        case 'TRAINER':
            return 'Trainer';
        case 'MENTAL_TRAINER':
            return 'Mental Trainer';
        case 'PLAYER':
            return 'Spieler';
        default:
            return role;
    }
}

// Add export functionality
document.querySelectorAll('.export-btn').forEach(btn => {
    btn.addEventListener('click', () => {
        const chartId = btn.dataset.chart;
        exportChartData(chartId);
    });
});

// Export chart data to CSV
function exportChartData(chartId) {
    if (!selectedAthlete) return;

    let chart;
    let fileName;

    switch (chartId) {
        case 'completion-rate':
            chart = completionRateChart;
            fileName = `Abschlussrate_${selectedAthlete.name}_${moment().format('YYYY-MM-DD')}.csv`;
            break;
        case 'trainingsplan-adherence':
            chart = trainingsplanAdherenceChart;
            fileName = `Trainingsplan_${selectedAthlete.name}_${moment().format('YYYY-MM-DD')}.csv`;
            break;
        case 'performance-trends':
            chart = performanceTrendsChart;
            fileName = `Leistungstrends_${selectedAthlete.name}_${moment().format('YYYY-MM-DD')}.csv`;
            break;
        case 'self-assessment':
            chart = selfAssessmentChart;
            fileName = `Selbsteinschätzung_${selectedAthlete.name}_${moment().format('YYYY-MM-DD')}.csv`;
            break;
        case 'question-analysis':
            chart = questionAnalysisChart;
            fileName = `Fragenanalyse_${selectedAthlete.name}_${moment().format('YYYY-MM-DD')}.csv`;
            break;
        case 'emotional-state':
            chart = emotionalStateChart;
            fileName = `EmotionalerZustand_${selectedAthlete.name}_${moment().format('YYYY-MM-DD')}.csv`;
            break;
        default:
            return;
    }

    if (!chart || !chart.data) return;

    // Prepare CSV content
    let csvContent = 'data:text/csv;charset=utf-8,';

    // Add headers
    const headers = ['Datum'];
    chart.data.datasets.forEach(dataset => {
        headers.push(dataset.label);
    });
    csvContent += headers.join(',') + '\n';

    // Add data rows
    chart.data.labels.forEach((label, index) => {
        const row = [label];
        chart.data.datasets.forEach(dataset => {
            row.push(dataset.data[index] !== undefined ? dataset.data[index] : '');
        });
        csvContent += row.join(',') + '\n';
    });

    // Create download link
    const encodedUri = encodeURI(csvContent);
    const link = document.createElement('a');
    link.setAttribute('href', encodedUri);
    link.setAttribute('download', fileName);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}
