{"logs": [{"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-mergeDebugResources-81:\\values-de\\values-de.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\edf207558568566ef6df89a74527d120\\transformed\\appcompat-1.6.1\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,308,420,506,612,727,805,880,972,1066,1162,1263,1370,1470,1574,1672,1770,1867,1949,2060,2162,2260,2367,2470,2574,2730,2832", "endColumns": "104,97,111,85,105,114,77,74,91,93,95,100,106,99,103,97,97,96,81,110,101,97,106,102,103,155,101,81", "endOffsets": "205,303,415,501,607,722,800,875,967,1061,1157,1258,1365,1465,1569,1667,1765,1862,1944,2055,2157,2255,2362,2465,2569,2725,2827,2909"}, "to": {"startLines": "11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,250", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "576,681,779,891,977,1083,1198,1276,1351,1443,1537,1633,1734,1841,1941,2045,2143,2241,2338,2420,2531,2633,2731,2838,2941,3045,3201,23554", "endColumns": "104,97,111,85,105,114,77,74,91,93,95,100,106,99,103,97,97,96,81,110,101,97,106,102,103,155,101,81", "endOffsets": "676,774,886,972,1078,1193,1271,1346,1438,1532,1628,1729,1836,1936,2040,2138,2236,2333,2415,2526,2628,2726,2833,2936,3040,3196,3298,23631"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\960c7054b802f15a0b75b85e164cffa8\\transformed\\core-1.15.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,455,563,668,786", "endColumns": "97,101,99,99,107,104,117,100", "endOffsets": "148,250,350,450,558,663,781,882"}, "to": {"startLines": "46,47,48,49,50,51,52,252", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3945,4043,4145,4245,4345,4453,4558,23711", "endColumns": "97,101,99,99,107,104,117,100", "endOffsets": "4038,4140,4240,4340,4448,4553,4671,23807"}}, {"source": "D:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\src\\main\\res\\values-de\\strings.xml", "from": {"startLines": "52,36,8,1,4,46,29,50,51,17,40,39,6,9,12,43,10,5,21,41,22,20,37,38,19,42,18,11,31,30,14,15,48,13,32,7,34,26,23,44,28,25,27,24,3,62,16,60,59,58,57,49,61,45,33", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3956,2522,360,17,124,3467,2136,3773,3899,991,2933,2805,229,432,656,3222,504,177,1330,3043,1420,1240,2605,2693,1172,3144,1103,577,2297,2219,768,822,3554,723,2384,280,2483,1806,1510,3294,2045,1698,1941,1588,73,4720,882,4529,4409,4309,4204,3676,4627,3400,2447", "endLines": "56,36,8,1,4,46,29,50,51,17,40,39,6,9,12,43,10,5,21,41,22,20,37,38,19,42,18,11,31,30,14,15,48,13,32,7,34,26,23,44,28,25,27,24,3,62,16,60,59,58,57,49,61,45,33", "endColumns": "19,81,70,52,51,83,81,124,55,110,108,126,49,70,65,70,71,50,88,99,88,88,86,110,66,76,67,77,85,76,52,58,120,43,61,78,35,133,76,104,89,106,102,108,49,78,107,96,118,98,103,95,91,65,34", "endOffsets": "4198,2599,426,65,171,3546,2213,3893,3950,1097,3037,2927,274,498,717,3288,571,223,1414,3138,1504,1324,2687,2799,1234,3216,1166,650,2378,2291,816,876,3670,762,2441,354,2514,1935,1582,3394,2130,1800,2039,1692,118,4794,985,4621,4523,4403,4303,3767,4714,3461,2477"}, "to": {"startLines": "2,38,39,40,53,54,78,79,81,83,84,85,87,88,89,90,91,93,94,96,97,98,99,100,101,102,103,108,112,113,115,116,117,118,119,176,235,236,237,238,239,244,245,246,249,256,257,258,259,260,261,262,263,266,267", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3303,3385,3456,4676,4728,7651,7733,7962,8116,8227,8336,8563,8613,8684,8750,8821,8980,9031,9186,9286,9375,9464,9551,9662,9729,9806,10280,10605,10691,10827,10880,10939,11060,11104,17431,22279,22315,22449,22526,22631,23020,23127,23230,23504,24077,24156,24264,24361,24480,24579,24683,24779,25048,25114", "endLines": "6,38,39,40,53,54,78,79,81,83,84,85,87,88,89,90,91,93,94,96,97,98,99,100,101,102,103,108,112,113,115,116,117,118,119,176,235,236,237,238,239,244,245,246,249,256,257,258,259,260,261,262,263,266,267", "endColumns": "19,81,70,52,51,83,81,124,55,110,108,126,49,70,65,70,71,50,88,99,88,88,86,110,66,76,67,77,85,76,52,58,120,43,61,78,35,133,76,104,89,106,102,108,49,78,107,96,118,98,103,95,91,65,34", "endOffsets": "388,3380,3451,3504,4723,4807,7728,7853,8013,8222,8331,8458,8608,8679,8745,8816,8888,9026,9115,9281,9370,9459,9546,9657,9724,9801,9869,10353,10686,10763,10875,10934,11055,11099,11161,17505,22310,22444,22521,22626,22716,23122,23225,23334,23549,24151,24259,24356,24475,24574,24678,24774,24866,25109,25144"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b903733d615c0b2ff05f9b9df539ba65\\transformed\\material-1.11.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,283,374,463,547,637,719,820,942,1023,1089,1183,1253,1312,1420,1486,1555,1613,1685,1749,1803,1931,1991,2053,2107,2185,2322,2414,2498,2643,2727,2813,2946,3036,3115,3172,3223,3289,3363,3445,3538,3613,3687,3765,3837,3911,4021,4113,4195,4284,4373,4447,4525,4611,4666,4745,4812,4892,4976,5038,5102,5165,5234,5341,5448,5547,5653,5714,5769", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,90,88,83,89,81,100,121,80,65,93,69,58,107,65,68,57,71,63,53,127,59,61,53,77,136,91,83,144,83,85,132,89,78,56,50,65,73,81,92,74,73,77,71,73,109,91,81,88,88,73,77,85,54,78,66,79,83,61,63,62,68,106,106,98,105,60,54,81", "endOffsets": "278,369,458,542,632,714,815,937,1018,1084,1178,1248,1307,1415,1481,1550,1608,1680,1744,1798,1926,1986,2048,2102,2180,2317,2409,2493,2638,2722,2808,2941,3031,3110,3167,3218,3284,3358,3440,3533,3608,3682,3760,3832,3906,4016,4108,4190,4279,4368,4442,4520,4606,4661,4740,4807,4887,4971,5033,5097,5160,5229,5336,5443,5542,5648,5709,5764,5846"}, "to": {"startLines": "7,41,42,43,44,45,55,56,57,95,104,109,114,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,243", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "393,3509,3600,3689,3773,3863,4812,4913,5035,9120,9874,10358,10768,17510,17618,17684,17753,17811,17883,17947,18001,18129,18189,18251,18305,18383,18520,18612,18696,18841,18925,19011,19144,19234,19313,19370,19421,19487,19561,19643,19736,19811,19885,19963,20035,20109,20219,20311,20393,20482,20571,20645,20723,20809,20864,20943,21010,21090,21174,21236,21300,21363,21432,21539,21646,21745,21851,21912,22938", "endLines": "10,41,42,43,44,45,55,56,57,95,104,109,114,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,243", "endColumns": "12,90,88,83,89,81,100,121,80,65,93,69,58,107,65,68,57,71,63,53,127,59,61,53,77,136,91,83,144,83,85,132,89,78,56,50,65,73,81,92,74,73,77,71,73,109,91,81,88,88,73,77,85,54,78,66,79,83,61,63,62,68,106,106,98,105,60,54,81", "endOffsets": "571,3595,3684,3768,3858,3940,4908,5030,5111,9181,9963,10423,10822,17613,17679,17748,17806,17878,17942,17996,18124,18184,18246,18300,18378,18515,18607,18691,18836,18920,19006,19139,19229,19308,19365,19416,19482,19556,19638,19731,19806,19880,19958,20030,20104,20214,20306,20388,20477,20566,20640,20718,20804,20859,20938,21005,21085,21169,21231,21295,21358,21427,21534,21641,21740,21846,21907,21962,23015"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2f74f887e8617a184c260b963fe8585b\\transformed\\play-services-base-18.0.1\\res\\values-de\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,298,458,582,690,864,991,1108,1223,1399,1507,1672,1799,1957,2129,2196,2255", "endColumns": "104,159,123,107,173,126,116,114,175,107,164,126,157,171,66,58,75", "endOffsets": "297,457,581,689,863,990,1107,1222,1398,1506,1671,1798,1956,2128,2195,2254,2330"}, "to": {"startLines": "60,61,62,63,64,65,66,67,69,70,71,72,73,74,75,76,77", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5300,5409,5573,5701,5813,5991,6122,6243,6507,6687,6799,6968,7099,7261,7437,7508,7571", "endColumns": "108,163,127,111,177,130,120,118,179,111,168,130,161,175,70,62,79", "endOffsets": "5404,5568,5696,5808,5986,6117,6238,6357,6682,6794,6963,7094,7256,7432,7503,7566,7646"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3063590ab377558e02f95f2978ee188b\\transformed\\material3-release\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,186,315,424,553,663,758,870,1014,1132,1288,1373,1478,1573,1675,1793,1919,2029,2165,2302,2437,2616,2744,2867,2995,3120,3216,3314,3434,3563,3663,3768,3870,4011,4159,4265,4367,4447,4543,4638,4724,4813,4914,5002,5088,5188,5294,5389,5490,5578,5687,5788,5892,6030,6119,6224", "endColumns": "130,128,108,128,109,94,111,143,117,155,84,104,94,101,117,125,109,135,136,134,178,127,122,127,124,95,97,119,128,99,104,101,140,147,105,101,79,95,94,85,88,100,87,85,99,105,94,100,87,108,100,103,137,88,104,95", "endOffsets": "181,310,419,548,658,753,865,1009,1127,1283,1368,1473,1568,1670,1788,1914,2024,2160,2297,2432,2611,2739,2862,2990,3115,3211,3309,3429,3558,3658,3763,3865,4006,4154,4260,4362,4442,4538,4633,4719,4808,4909,4997,5083,5183,5289,5384,5485,5573,5682,5783,5887,6025,6114,6219,6315"}, "to": {"startLines": "120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "11166,11297,11426,11535,11664,11774,11869,11981,12125,12243,12399,12484,12589,12684,12786,12904,13030,13140,13276,13413,13548,13727,13855,13978,14106,14231,14327,14425,14545,14674,14774,14879,14981,15122,15270,15376,15478,15558,15654,15749,15835,15924,16025,16113,16199,16299,16405,16500,16601,16689,16798,16899,17003,17141,17230,17335", "endColumns": "130,128,108,128,109,94,111,143,117,155,84,104,94,101,117,125,109,135,136,134,178,127,122,127,124,95,97,119,128,99,104,101,140,147,105,101,79,95,94,85,88,100,87,85,99,105,94,100,87,108,100,103,137,88,104,95", "endOffsets": "11292,11421,11530,11659,11769,11864,11976,12120,12238,12394,12479,12584,12679,12781,12899,13025,13135,13271,13408,13543,13722,13850,13973,14101,14226,14322,14420,14540,14669,14769,14874,14976,15117,15265,15371,15473,15553,15649,15744,15830,15919,16020,16108,16194,16294,16400,16495,16596,16684,16793,16894,16998,17136,17225,17330,17426"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2e188628e5386314399d09afe6f3689d\\transformed\\browser-1.4.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,159,260,371", "endColumns": "103,100,110,99", "endOffsets": "154,255,366,466"}, "to": {"startLines": "80,105,106,107", "startColumns": "4,4,4,4", "startOffsets": "7858,9968,10069,10180", "endColumns": "103,100,110,99", "endOffsets": "7957,10064,10175,10275"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f935ad6ff8f4a372e6e4c3926f45186a\\transformed\\foundation-release\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,142", "endColumns": "86,89", "endOffsets": "137,227"}, "to": {"startLines": "264,265", "startColumns": "4,4", "startOffsets": "24871,24958", "endColumns": "86,89", "endOffsets": "24953,25043"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\4e8c4906aeb6063dad76cbfa2a875519\\transformed\\ui-release\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,201,289,387,487,574,659,751,840,928,993,1057,1138,1222,1297,1376,1442", "endColumns": "95,87,97,99,86,84,91,88,87,64,63,80,83,74,78,65,119", "endOffsets": "196,284,382,482,569,654,746,835,923,988,1052,1133,1217,1292,1371,1437,1557"}, "to": {"startLines": "58,59,82,86,92,110,111,234,240,241,242,247,248,251,253,254,255", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5116,5212,8018,8463,8893,10428,10513,22190,22721,22809,22874,23339,23420,23636,23812,23891,23957", "endColumns": "95,87,97,99,86,84,91,88,87,64,63,80,83,74,78,65,119", "endOffsets": "5207,5295,8111,8558,8975,10508,10600,22274,22804,22869,22933,23415,23499,23706,23886,23952,24072"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\92e8c801482673d28198346e411930c4\\transformed\\play-services-basement-18.1.0\\res\\values-de\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "140", "endOffsets": "335"}, "to": {"startLines": "68", "startColumns": "4", "startOffsets": "6362", "endColumns": "144", "endOffsets": "6502"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b064642397aead0c4b677d2bb09b1c2b\\transformed\\navigation-ui-2.7.7\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,159", "endColumns": "103,118", "endOffsets": "154,273"}, "to": {"startLines": "232,233", "startColumns": "4,4", "startOffsets": "21967,22071", "endColumns": "103,118", "endOffsets": "22066,22185"}}]}, {"outputFile": "com.atom.diesnookerapp-mergeDebugResources-81:/values-de/values-de.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\edf207558568566ef6df89a74527d120\\transformed\\appcompat-1.6.1\\res\\values-de\\values-de.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,255", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "576,681,779,891,977,1083,1198,1276,1351,1443,1537,1633,1734,1841,1941,2045,2143,2241,2338,2420,2531,2633,2731,2838,2941,3045,3201,23856", "endColumns": "104,97,111,85,105,114,77,74,91,93,95,100,106,99,103,97,97,96,81,110,101,97,106,102,103,155,101,81", "endOffsets": "676,774,886,972,1078,1193,1271,1346,1438,1532,1628,1729,1836,1936,2040,2138,2236,2333,2415,2526,2628,2726,2833,2936,3040,3196,3298,23933"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\960c7054b802f15a0b75b85e164cffa8\\transformed\\core-1.15.0\\res\\values-de\\values-de.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "46,47,48,49,50,51,52,257", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3945,4043,4145,4245,4345,4453,4558,24013", "endColumns": "97,101,99,99,107,104,117,100", "endOffsets": "4038,4140,4240,4340,4448,4553,4671,24109"}}, {"source": "D:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\src\\main\\res\\values-de\\strings.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,16,18,19,17,-1,-1,-1,-1,-1,-1,15,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,4,4,4,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,881,999,1056,938,-1,-1,-1,-1,-1,-1,822,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "endColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,55,55,71,59,-1,-1,-1,-1,-1,-1,57,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "endOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,932,1050,1123,993,-1,-1,-1,-1,-1,-1,875,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2,38,39,40,53,54,78,79,81,83,84,85,87,88,89,90,91,93,94,96,97,98,99,100,101,102,103,104,105,106,107,112,116,117,119,120,121,122,123,124,181,240,241,242,243,244,249,250,251,254,261,262,263,264,265,266,267,268,271,272", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3303,3385,3456,4676,4728,7651,7733,7962,8116,8227,8336,8563,8613,8684,8750,8821,8980,9031,9186,9286,9375,9464,9551,9662,9729,9806,9874,9930,9986,10058,10524,10849,10935,11071,11124,11183,11304,11362,11406,17733,22581,22617,22751,22828,22933,23322,23429,23532,23806,24379,24458,24566,24663,24782,24881,24985,25081,25350,25416", "endLines": "6,38,39,40,53,54,78,79,81,83,84,85,87,88,89,90,91,93,94,96,97,98,99,100,101,102,103,104,105,106,107,112,116,117,119,120,121,122,123,124,181,240,241,242,243,244,249,250,251,254,261,262,263,264,265,266,267,268,271,272", "endColumns": "19,81,70,52,51,83,81,124,55,110,108,126,49,70,65,70,71,50,88,99,88,88,86,110,66,76,67,55,55,71,59,77,85,76,52,58,120,57,43,61,78,35,133,76,104,89,106,102,108,49,78,107,96,118,98,103,95,91,65,34", "endOffsets": "388,3380,3451,3504,4723,4807,7728,7853,8013,8222,8331,8458,8608,8679,8745,8816,8888,9026,9115,9281,9370,9459,9546,9657,9724,9801,9869,9925,9981,10053,10113,10597,10930,11007,11119,11178,11299,11357,11401,11463,17807,22612,22746,22823,22928,23018,23424,23527,23636,23851,24453,24561,24658,24777,24876,24980,25076,25168,25411,25446"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b903733d615c0b2ff05f9b9df539ba65\\transformed\\material-1.11.0\\res\\values-de\\values-de.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "7,41,42,43,44,45,55,56,57,95,108,113,118,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,248", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "393,3509,3600,3689,3773,3863,4812,4913,5035,9120,10118,10602,11012,17812,17920,17986,18055,18113,18185,18249,18303,18431,18491,18553,18607,18685,18822,18914,18998,19143,19227,19313,19446,19536,19615,19672,19723,19789,19863,19945,20038,20113,20187,20265,20337,20411,20521,20613,20695,20784,20873,20947,21025,21111,21166,21245,21312,21392,21476,21538,21602,21665,21734,21841,21948,22047,22153,22214,23240", "endLines": "10,41,42,43,44,45,55,56,57,95,108,113,118,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,248", "endColumns": "12,90,88,83,89,81,100,121,80,65,93,69,58,107,65,68,57,71,63,53,127,59,61,53,77,136,91,83,144,83,85,132,89,78,56,50,65,73,81,92,74,73,77,71,73,109,91,81,88,88,73,77,85,54,78,66,79,83,61,63,62,68,106,106,98,105,60,54,81", "endOffsets": "571,3595,3684,3768,3858,3940,4908,5030,5111,9181,10207,10667,11066,17915,17981,18050,18108,18180,18244,18298,18426,18486,18548,18602,18680,18817,18909,18993,19138,19222,19308,19441,19531,19610,19667,19718,19784,19858,19940,20033,20108,20182,20260,20332,20406,20516,20608,20690,20779,20868,20942,21020,21106,21161,21240,21307,21387,21471,21533,21597,21660,21729,21836,21943,22042,22148,22209,22264,23317"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2f74f887e8617a184c260b963fe8585b\\transformed\\play-services-base-18.0.1\\res\\values-de\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "60,61,62,63,64,65,66,67,69,70,71,72,73,74,75,76,77", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5300,5409,5573,5701,5813,5991,6122,6243,6507,6687,6799,6968,7099,7261,7437,7508,7571", "endColumns": "108,163,127,111,177,130,120,118,179,111,168,130,161,175,70,62,79", "endOffsets": "5404,5568,5696,5808,5986,6117,6238,6357,6682,6794,6963,7094,7256,7432,7503,7566,7646"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3063590ab377558e02f95f2978ee188b\\transformed\\material3-release\\res\\values-de\\values-de.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "11468,11599,11728,11837,11966,12076,12171,12283,12427,12545,12701,12786,12891,12986,13088,13206,13332,13442,13578,13715,13850,14029,14157,14280,14408,14533,14629,14727,14847,14976,15076,15181,15283,15424,15572,15678,15780,15860,15956,16051,16137,16226,16327,16415,16501,16601,16707,16802,16903,16991,17100,17201,17305,17443,17532,17637", "endColumns": "130,128,108,128,109,94,111,143,117,155,84,104,94,101,117,125,109,135,136,134,178,127,122,127,124,95,97,119,128,99,104,101,140,147,105,101,79,95,94,85,88,100,87,85,99,105,94,100,87,108,100,103,137,88,104,95", "endOffsets": "11594,11723,11832,11961,12071,12166,12278,12422,12540,12696,12781,12886,12981,13083,13201,13327,13437,13573,13710,13845,14024,14152,14275,14403,14528,14624,14722,14842,14971,15071,15176,15278,15419,15567,15673,15775,15855,15951,16046,16132,16221,16322,16410,16496,16596,16702,16797,16898,16986,17095,17196,17300,17438,17527,17632,17728"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2e188628e5386314399d09afe6f3689d\\transformed\\browser-1.4.0\\res\\values-de\\values-de.xml", "from": {"startLines": "-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1"}, "to": {"startLines": "80,109,110,111", "startColumns": "4,4,4,4", "startOffsets": "7858,10212,10313,10424", "endColumns": "103,100,110,99", "endOffsets": "7957,10308,10419,10519"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f935ad6ff8f4a372e6e4c3926f45186a\\transformed\\foundation-release\\res\\values-de\\values-de.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "269,270", "startColumns": "4,4", "startOffsets": "25173,25260", "endColumns": "86,89", "endOffsets": "25255,25345"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\4e8c4906aeb6063dad76cbfa2a875519\\transformed\\ui-release\\res\\values-de\\values-de.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "58,59,82,86,92,114,115,239,245,246,247,252,253,256,258,259,260", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5116,5212,8018,8463,8893,10672,10757,22492,23023,23111,23176,23641,23722,23938,24114,24193,24259", "endColumns": "95,87,97,99,86,84,91,88,87,64,63,80,83,74,78,65,119", "endOffsets": "5207,5295,8111,8558,8975,10752,10844,22576,23106,23171,23235,23717,23801,24008,24188,24254,24374"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\92e8c801482673d28198346e411930c4\\transformed\\play-services-basement-18.1.0\\res\\values-de\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "68", "startColumns": "4", "startOffsets": "6362", "endColumns": "144", "endOffsets": "6502"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b064642397aead0c4b677d2bb09b1c2b\\transformed\\navigation-ui-2.7.7\\res\\values-de\\values-de.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "237,238", "startColumns": "4,4", "startOffsets": "22269,22373", "endColumns": "103,118", "endOffsets": "22368,22487"}}]}]}