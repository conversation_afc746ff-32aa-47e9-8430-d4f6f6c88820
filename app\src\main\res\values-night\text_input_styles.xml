<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Base TextInputLayout style for night mode -->
    <style name="CustomTextInputLayout" parent="Widget.MaterialComponents.TextInputLayout.OutlinedBox">
        <item name="boxStrokeColor">?attr/colorPrimary</item>
        <item name="hintTextColor">?attr/colorPrimary</item>
        <item name="android:textColorHint">@color/white</item>
    </style>
    
    <!-- Snooker theme TextInputLayout style for night mode -->
    <style name="CustomTextInputLayout.Snooker">
        <item name="boxStrokeColor">@color/snooker_red_200</item>
        <item name="hintTextColor">@color/snooker_red_200</item>
        <item name="boxBackgroundColor">@color/snooker_card_background_dark</item>
        <item name="android:textColorHint">@color/white</item>
    </style>
    
    <!-- Blue theme TextInputLayout style for night mode -->
    <style name="CustomTextInputLayout.Blue">
        <item name="boxStrokeColor">@color/blue_200</item>
        <item name="hintTextColor">@color/blue_200</item>
        <item name="boxBackgroundColor">@color/blue_card_background_dark</item>
        <item name="android:textColorHint">@color/white</item>
    </style>
    
    <!-- Dark Blue theme TextInputLayout style for night mode -->
    <style name="CustomTextInputLayout.DarkBlue">
        <item name="boxStrokeColor">@color/dark_blue_200</item>
        <item name="hintTextColor">@color/dark_blue_200</item>
        <item name="boxBackgroundColor">@color/dark_blue_card_background_dark</item>
        <item name="android:textColorHint">@color/white</item>
    </style>
    
    <!-- Dark theme TextInputLayout style for night mode -->
    <style name="CustomTextInputLayout.Dark">
        <item name="boxStrokeColor">@color/purple_200</item>
        <item name="hintTextColor">@color/purple_200</item>
        <item name="boxBackgroundColor">@color/dark_gray</item>
        <item name="android:textColorHint">@color/white</item>
    </style>
    
    <!-- Base Dropdown TextInputLayout style for night mode -->
    <style name="CustomDropdownTextInputLayout" parent="Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu">
        <item name="boxStrokeColor">?attr/colorPrimary</item>
        <item name="hintTextColor">?attr/colorPrimary</item>
        <item name="android:textColorHint">@color/white</item>
    </style>
    
    <!-- Snooker theme Dropdown TextInputLayout style for night mode -->
    <style name="CustomDropdownTextInputLayout.Snooker">
        <item name="boxStrokeColor">@color/snooker_red_200</item>
        <item name="hintTextColor">@color/snooker_red_200</item>
        <item name="boxBackgroundColor">@color/snooker_card_background_dark</item>
        <item name="endIconTint">@color/snooker_red_200</item>
        <item name="android:textColorHint">@color/white</item>
    </style>
    
    <!-- Blue theme Dropdown TextInputLayout style for night mode -->
    <style name="CustomDropdownTextInputLayout.Blue">
        <item name="boxStrokeColor">@color/blue_200</item>
        <item name="hintTextColor">@color/blue_200</item>
        <item name="boxBackgroundColor">@color/blue_card_background_dark</item>
        <item name="endIconTint">@color/blue_200</item>
        <item name="android:textColorHint">@color/white</item>
    </style>
    
    <!-- Dark Blue theme Dropdown TextInputLayout style for night mode -->
    <style name="CustomDropdownTextInputLayout.DarkBlue">
        <item name="boxStrokeColor">@color/dark_blue_200</item>
        <item name="hintTextColor">@color/dark_blue_200</item>
        <item name="boxBackgroundColor">@color/dark_blue_card_background_dark</item>
        <item name="endIconTint">@color/dark_blue_200</item>
        <item name="android:textColorHint">@color/white</item>
    </style>
    
    <!-- Dark theme Dropdown TextInputLayout style for night mode -->
    <style name="CustomDropdownTextInputLayout.Dark">
        <item name="boxStrokeColor">@color/purple_200</item>
        <item name="hintTextColor">@color/purple_200</item>
        <item name="boxBackgroundColor">@color/dark_gray</item>
        <item name="endIconTint">@color/purple_200</item>
        <item name="android:textColorHint">@color/white</item>
    </style>
</resources>
