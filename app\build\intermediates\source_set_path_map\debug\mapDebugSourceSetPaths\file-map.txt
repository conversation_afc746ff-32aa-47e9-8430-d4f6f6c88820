com.atom.diesnookerapp-annotation-experimental-1.4.1-0 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0720eb107e89c923d3bf183f803b709a\transformed\annotation-experimental-1.4.1\res
com.atom.diesnookerapp-core-runtime-2.2.0-1 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\08800e8822abfbb6c6677901ff35004e\transformed\core-runtime-2.2.0\res
com.atom.diesnookerapp-lifecycle-livedata-2.8.7-2 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\095d12f777f8d88c9b4836652b403fa4\transformed\lifecycle-livedata-2.8.7\res
com.atom.diesnookerapp-activity-1.10.0-3 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c3f9c036a4b978d45aa46e441d17c9d\transformed\activity-1.10.0\res
com.atom.diesnookerapp-ui-test-manifest-1.6.6-4 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0f80cdb95ae6a2948d9ffde64c419845\transformed\ui-test-manifest-1.6.6\res
com.atom.diesnookerapp-recyclerview-1.1.0-5 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1078977e84cf2f81e9b64ac9497bef28\transformed\recyclerview-1.1.0\res
com.atom.diesnookerapp-ui-graphics-release-6 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13b61402139f7407d176cf88d856f408\transformed\ui-graphics-release\res
com.atom.diesnookerapp-navigation-ui-ktx-2.7.7-7 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\14ba56137749021c9a2be664170d7ac7\transformed\navigation-ui-ktx-2.7.7\res
com.atom.diesnookerapp-viewpager2-1.0.0-8 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1807240b62a7a8a8286290202eecc98f\transformed\viewpager2-1.0.0\res
com.atom.diesnookerapp-slidingpanelayout-1.2.0-9 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\203bebdaf46ee5720eee11b108418f1d\transformed\slidingpanelayout-1.2.0\res
com.atom.diesnookerapp-lifecycle-viewmodel-savedstate-2.8.7-10 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\220e66dfbaf79c535786699abfffffd8\transformed\lifecycle-viewmodel-savedstate-2.8.7\res
com.atom.diesnookerapp-lifecycle-livedata-core-ktx-2.8.7-11 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2922db0595fb1e5606cfbb7d37671928\transformed\lifecycle-livedata-core-ktx-2.8.7\res
com.atom.diesnookerapp-ui-tooling-preview-release-12 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2965a251595db0b0aea7828df95d2c22\transformed\ui-tooling-preview-release\res
com.atom.diesnookerapp-navigation-common-2.7.7-13 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\29b36062e5fe2b6a9996eb16f4854927\transformed\navigation-common-2.7.7\res
com.atom.diesnookerapp-emoji2-1.3.0-14 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2daf4fa1f3706cd82523bfe6089ca9b2\transformed\emoji2-1.3.0\res
com.atom.diesnookerapp-browser-1.4.0-15 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2e188628e5386314399d09afe6f3689d\transformed\browser-1.4.0\res
com.atom.diesnookerapp-play-services-base-18.0.1-16 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2f74f887e8617a184c260b963fe8585b\transformed\play-services-base-18.0.1\res
com.atom.diesnookerapp-material3-release-17 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3063590ab377558e02f95f2978ee188b\transformed\material3-release\res
com.atom.diesnookerapp-lifecycle-livedata-core-2.8.7-18 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\323fccee1568262fa96249e1496cc48f\transformed\lifecycle-livedata-core-2.8.7\res
com.atom.diesnookerapp-animation-core-release-19 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3345a7fbe747144276fa9999773cd59e\transformed\animation-core-release\res
com.atom.diesnookerapp-foundation-layout-release-20 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\342fc164069752ca474125786d028d25\transformed\foundation-layout-release\res
com.atom.diesnookerapp-coordinatorlayout-1.1.0-21 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3477c63391d771930e606dbf0a8d42a5\transformed\coordinatorlayout-1.1.0\res
com.atom.diesnookerapp-tracing-1.2.0-22 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3615514103634996edc945592e2d2f6c\transformed\tracing-1.2.0\res
com.atom.diesnookerapp-savedstate-1.2.1-23 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39d3ae8d4a330c67607b288e5601c24e\transformed\savedstate-1.2.1\res
com.atom.diesnookerapp-work-runtime-ktx-2.10.0-24 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\476165fc5437857fd89974625a791c5a\transformed\work-runtime-ktx-2.10.0\res
com.atom.diesnookerapp-ui-release-25 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4e8c4906aeb6063dad76cbfa2a875519\transformed\ui-release\res
com.atom.diesnookerapp-navigation-runtime-2.7.7-26 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4f2b5a48b3fe5567547a5485090d7705\transformed\navigation-runtime-2.7.7\res
com.atom.diesnookerapp-material-release-27 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\545b8561523e2b1f4d0d7838087a8a04\transformed\material-release\res
com.atom.diesnookerapp-drawerlayout-1.1.1-28 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\58dea54d1ca4f9d9a165d72ef873c4cc\transformed\drawerlayout-1.1.1\res
com.atom.diesnookerapp-room-runtime-2.6.1-29 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5a5b100d33267656477d10d382e016a1\transformed\room-runtime-2.6.1\res
com.atom.diesnookerapp-profileinstaller-1.4.0-30 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c6b1be78318e20ba9389e13b1e315fd\transformed\profileinstaller-1.4.0\res
com.atom.diesnookerapp-lifecycle-runtime-ktx-release-31 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\61951ba496fe13479e7e43963c62b9cb\transformed\lifecycle-runtime-ktx-release\res
com.atom.diesnookerapp-constraintlayout-2.1.4-32 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62593010688224dfc5b372405b080b58\transformed\constraintlayout-2.1.4\res
com.atom.diesnookerapp-lifecycle-viewmodel-ktx-2.8.7-33 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62fbe1e14622130bc6e17cd2544681b7\transformed\lifecycle-viewmodel-ktx-2.8.7\res
com.atom.diesnookerapp-ui-text-release-34 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\630a6b64cf0b99c94039eb94430e9039\transformed\ui-text-release\res
com.atom.diesnookerapp-lifecycle-runtime-release-35 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\67e1c84be8318a5402cb5635d76c8e98\transformed\lifecycle-runtime-release\res
com.atom.diesnookerapp-navigation-fragment-2.7.7-36 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6b085cf425b5208770b1733a512f3bf9\transformed\navigation-fragment-2.7.7\res
com.atom.diesnookerapp-animation-release-37 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6ddfac4953b40634319c2ee646316b01\transformed\animation-release\res
com.atom.diesnookerapp-transition-1.4.1-38 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71ed0c9230eab0bc603dcc68cfab7b18\transformed\transition-1.4.1\res
com.atom.diesnookerapp-lifecycle-service-2.8.7-39 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\72cd04d57cf61f97c873831b15834ca5\transformed\lifecycle-service-2.8.7\res
com.atom.diesnookerapp-ui-util-release-40 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\746f9ea1c7e141b7cd1c54c6bb6bb0fa\transformed\ui-util-release\res
com.atom.diesnookerapp-core-ktx-1.15.0-41 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\755be06f9b0e6e8f28aaf6e4fbac3477\transformed\core-ktx-1.15.0\res
com.atom.diesnookerapp-emoji2-views-helper-1.3.0-42 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c2724dca3c98e276efaab131df51d3a\transformed\emoji2-views-helper-1.3.0\res
com.atom.diesnookerapp-navigation-common-ktx-2.7.7-43 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7eb0b971608d67ff1f47a47c81bdc2c0\transformed\navigation-common-ktx-2.7.7\res
com.atom.diesnookerapp-navigation-fragment-ktx-2.7.7-44 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\85b578f36c5215c705f2b8fa39faf60e\transformed\navigation-fragment-ktx-2.7.7\res
com.atom.diesnookerapp-window-1.0.0-45 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\91a05db1f70fcc1560e99a1a2e5a1e0b\transformed\window-1.0.0\res
com.atom.diesnookerapp-runtime-saveable-release-46 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\91aefd5272af96834250923d7b3d532b\transformed\runtime-saveable-release\res
com.atom.diesnookerapp-activity-compose-1.10.0-47 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\92450d02dfb2767d0073e6e1885d63e5\transformed\activity-compose-1.10.0\res
com.atom.diesnookerapp-play-services-basement-18.1.0-48 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\92e8c801482673d28198346e411930c4\transformed\play-services-basement-18.1.0\res
com.atom.diesnookerapp-core-1.15.0-49 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\960c7054b802f15a0b75b85e164cffa8\transformed\core-1.15.0\res
com.atom.diesnookerapp-runtime-release-50 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9c6802c9b562899c207089fd57e4a1fc\transformed\runtime-release\res
com.atom.diesnookerapp-ui-unit-release-51 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d4b7da40d5e42e728aab7fb52f7c68c\transformed\ui-unit-release\res
com.atom.diesnookerapp-firebase-common-20.4.2-52 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d609ecd27a49a88d1a4cf0b224bff07\transformed\firebase-common-20.4.2\res
com.atom.diesnookerapp-sqlite-framework-2.4.0-53 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a08d0afea0fb8d04257f17bda2a4020b\transformed\sqlite-framework-2.4.0\res
com.atom.diesnookerapp-ui-tooling-release-54 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a4d885512c0beef2ec8aea2204c9f1c7\transformed\ui-tooling-release\res
com.atom.diesnookerapp-tracing-ktx-1.2.0-55 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a56bf67678845e6b8d8185b57b0bcaff\transformed\tracing-ktx-1.2.0\res
com.atom.diesnookerapp-cardview-1.0.0-56 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a75f9814effd09ab0114ecc4773eaaa3\transformed\cardview-1.0.0\res
com.atom.diesnookerapp-savedstate-ktx-1.2.1-57 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\acfd5b96537540834722577deee275fe\transformed\savedstate-ktx-1.2.1\res
com.atom.diesnookerapp-sqlite-2.4.0-58 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\adc8c372d1b4d5985567fdce3c1ff1a0\transformed\sqlite-2.4.0\res
com.atom.diesnookerapp-navigation-ui-2.7.7-59 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b064642397aead0c4b677d2bb09b1c2b\transformed\navigation-ui-2.7.7\res
com.atom.diesnookerapp-material-icons-core-release-60 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b0c3da69700d8dfa40aae4cd8e12e270\transformed\material-icons-core-release\res
com.atom.diesnookerapp-lifecycle-viewmodel-release-61 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b1fbaf21c106f939b47d187adb1c0fac\transformed\lifecycle-viewmodel-release\res
com.atom.diesnookerapp-ui-geometry-release-62 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b415f8502827851acc5588034aaa524f\transformed\ui-geometry-release\res
com.atom.diesnookerapp-room-ktx-2.6.1-63 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b4e6d9afd0ba598fc2f0f129d06b1e7b\transformed\room-ktx-2.6.1\res
com.atom.diesnookerapp-material-1.11.0-64 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b903733d615c0b2ff05f9b9df539ba65\transformed\material-1.11.0\res
com.atom.diesnookerapp-fragment-1.6.2-65 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6254bcc2d81321e92f6d0c196b1d48d\transformed\fragment-1.6.2\res
com.atom.diesnookerapp-work-runtime-2.10.0-66 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7073675096451f775baf2db63e24c54\transformed\work-runtime-2.10.0\res
com.atom.diesnookerapp-appcompat-resources-1.6.1-67 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cec108e13a9d083e64db08e1b6145385\transformed\appcompat-resources-1.6.1\res
com.atom.diesnookerapp-startup-runtime-1.1.1-68 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d4ae6dc4e7b1dfb5b9ce574b1ad21a1c\transformed\startup-runtime-1.1.1\res
com.atom.diesnookerapp-activity-ktx-1.10.0-69 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d844f6e44315539624fbb7ebb020ef4d\transformed\activity-ktx-1.10.0\res
com.atom.diesnookerapp-fragment-ktx-1.6.2-70 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dfd8ce3ca23c7f1b2cd539ccd756cc0a\transformed\fragment-ktx-1.6.2\res
com.atom.diesnookerapp-navigation-runtime-ktx-2.7.7-71 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e20a0b3acc200aaf9aba33ac523d0336\transformed\navigation-runtime-ktx-2.7.7\res
com.atom.diesnookerapp-customview-poolingcontainer-1.0.0-72 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e380358256c1e9482b3d8f26b625fdd2\transformed\customview-poolingcontainer-1.0.0\res
com.atom.diesnookerapp-appcompat-1.6.1-73 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\edf207558568566ef6df89a74527d120\transformed\appcompat-1.6.1\res
com.atom.diesnookerapp-lifecycle-process-2.8.7-74 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f06ba6d15bb51653c2986f9faea87ba3\transformed\lifecycle-process-2.8.7\res
com.atom.diesnookerapp-material-ripple-release-75 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f0852f393a02041fb6485b4bdfdaba16\transformed\material-ripple-release\res
com.atom.diesnookerapp-foundation-release-76 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f935ad6ff8f4a372e6e4c3926f45186a\transformed\foundation-release\res
com.atom.diesnookerapp-ui-tooling-data-release-77 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fb53497875ff465f7931d65e5429b5b8\transformed\ui-tooling-data-release\res
com.atom.diesnookerapp-pngs-78 D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\build\generated\res\pngs\debug
com.atom.diesnookerapp-res-79 D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\build\generated\res\processDebugGoogleServices
com.atom.diesnookerapp-resValues-80 D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\build\generated\res\resValues\debug
com.atom.diesnookerapp-packageDebugResources-81 D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\build\intermediates\incremental\debug\packageDebugResources\merged.dir
com.atom.diesnookerapp-packageDebugResources-82 D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\build\intermediates\incremental\debug\packageDebugResources\stripped.dir
com.atom.diesnookerapp-debug-83 D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\build\intermediates\merged_res\debug\mergeDebugResources
com.atom.diesnookerapp-debug-84 D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\debug\res
com.atom.diesnookerapp-main-85 D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res
