package com.atom.diesnookerapp.ui.aufgaben

import android.content.Context
import android.content.SharedPreferences
import android.util.Log
import com.google.gson.Gson
import com.google.gson.GsonBuilder
import com.google.gson.JsonDeserializationContext
import com.google.gson.JsonDeserializer
import com.google.gson.JsonElement
import com.google.gson.JsonPrimitive
import com.google.gson.JsonSerializationContext
import com.google.gson.JsonSerializer
import com.google.gson.reflect.TypeToken
import org.threeten.bp.LocalDate
import org.threeten.bp.format.DateTimeFormatter
import java.lang.reflect.Type

class TaskManager(context: Context) {
    private val TAG = "TaskManager"
    private val sharedPreferences: SharedPreferences = context.getSharedPreferences("tasks_prefs", Context.MODE_PRIVATE)
    private val gson: Gson
    private val dateFormatter = DateTimeFormatter.ISO_LOCAL_DATE

    init {
        // Create a custom Gson instance with LocalDate type adapters
        gson = GsonBuilder()
            .registerTypeAdapter(LocalDate::class.java, LocalDateSerializer())
            .registerTypeAdapter(LocalDate::class.java, LocalDateDeserializer())
            .create()
    }

    // Custom serializer for LocalDate
    private class LocalDateSerializer : JsonSerializer<LocalDate> {
        override fun serialize(src: LocalDate?, typeOfSrc: Type?, context: JsonSerializationContext?): JsonElement {
            return JsonPrimitive(src?.format(DateTimeFormatter.ISO_LOCAL_DATE))
        }
    }

    // Custom deserializer for LocalDate
    private class LocalDateDeserializer : JsonDeserializer<LocalDate> {
        override fun deserialize(json: JsonElement?, typeOfT: Type?, context: JsonDeserializationContext?): LocalDate? {
            try {
                // Handle null case
                if (json == null || json.isJsonNull) {
                    return null
                }

                // Handle primitive case (string)
                if (json.isJsonPrimitive) {
                    val dateStr = json.asString
                    if (dateStr.isNullOrEmpty()) {
                        return null
                    }
                    return LocalDate.parse(dateStr, DateTimeFormatter.ISO_LOCAL_DATE)
                }

                // If we get here, it's not a format we can handle
                Log.e("TaskManager", "Unexpected JSON format for LocalDate: $json")
                return null
            } catch (e: Exception) {
                Log.e("TaskManager", "Error deserializing date: ${e.message}")
                return null
            }
        }
    }

    // Get all tasks
    fun getAllTasks(): List<Task> {
        val tasksJson = sharedPreferences.getString("tasks", null)
        if (tasksJson.isNullOrEmpty()) {
            return emptyList()
        }

        try {
            val type = object : TypeToken<List<Task>>() {}.type
            return gson.fromJson(tasksJson, type)
        } catch (e: Exception) {
            Log.e(TAG, "Error parsing tasks: ${e.message}")
            // If there's an error, clear the corrupted data and return empty list
            sharedPreferences.edit().remove("tasks").apply()
            return emptyList()
        }
    }

    // Get tasks by category
    fun getTasksByCategory(category: String): List<Task> {
        return getAllTasks().filter { it.category == category }
    }

    // Save all tasks
    private fun saveAllTasks(tasks: List<Task>) {
        try {
            val tasksJson = gson.toJson(tasks)
            sharedPreferences.edit().putString("tasks", tasksJson).apply()
        } catch (e: Exception) {
            Log.e(TAG, "Error saving tasks: ${e.message}")
        }
    }

    // Initialize tasks if they don't exist yet
    fun initializeTasksIfNeeded(newTasks: List<Task>) {
        try {
            val existingTasks = getAllTasks()

            if (existingTasks.isEmpty()) {
                // First time initialization
                saveAllTasks(newTasks)
                return
            }

            // Check if we need to add any new tasks that weren't in the original set
            val existingIds = existingTasks.map { it.id }.toSet()
            val tasksToAdd = newTasks.filter { it.id !in existingIds }

            // Update existing tasks to ensure they have the customPoints field
            val updatedExistingTasks = existingTasks.map { existingTask ->
                // If customPoints is null, initialize it with an empty map
                if (existingTask.customPoints == null) {
                    existingTask.copy(customPoints = mutableMapOf())
                } else {
                    existingTask
                }
            }

            if (tasksToAdd.isNotEmpty() || updatedExistingTasks != existingTasks) {
                saveAllTasks(updatedExistingTasks + tasksToAdd)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error initializing tasks: ${e.message}")
            // If there's an error during initialization, reset and try again with new tasks
            sharedPreferences.edit().remove("tasks").apply()
            saveAllTasks(newTasks)
        }
    }

    // Mark a task as completed for a specific date
    // Add these new methods to TaskManager class

    /**
     * Checks if a task can be completed today based on its frequency
     */
    fun canCompleteTaskToday(taskId: String): Boolean {
        val tasks = getAllTasks()
        val task = tasks.find { it.id == taskId } ?: return false
        val today = LocalDate.now()

        // Get completions for the relevant period
        when (task.frequency) {
            TaskFrequency.DAILY -> {
                // Check if already completed today
                return !(task.completions?.contains(today) ?: false)
            }
            TaskFrequency.WEEKLY -> {
                // Get completions for this week (using Monday-based week)
                val thisWeekCompletions = task.completions?.filter {
                    it.yearWeekKeyMonday() == today.yearWeekKeyMonday()
                } ?: emptyList()

                // Get max completions allowed for this task
                val maxCompletions = getMaxCompletionsForTask(task)

                // Check if we've reached the limit
                return thisWeekCompletions.size < maxCompletions
            }
            TaskFrequency.MONTHLY -> {
                // Monthly tasks remain the same
                val thisMonthCompletions = task.completions?.filter {
                    it.yearMonthKey() == today.yearMonthKey()
                } ?: emptyList()

                val maxCompletions = getMaxCompletionsForTask(task)
                return thisMonthCompletions.size < maxCompletions
            }
        }
    }

    /**
     * Gets the maximum number of completions allowed for a task based on its ID
     */
    private fun getMaxCompletionsForTask(task: Task): Int {
        // Default is 1 completion per period
        return when (task.id) {
            // Matchplay tasks
            "entscheidungen_frame" -> 3
            "entscheidungen_video" -> 2
            "entscheidungen_tip" -> 1
            "matchpraxis" -> 1

            // Safeties tasks
            "wenig_baelle" -> 2
            "escapes" -> 2
            "mit_druck" -> 4

            // Konzentration tasks
            "all_balls_equal" -> 2
            "balance_unterberger" -> 3

            // Breakbuilding tasks
            "zhao_x" -> 2

            // Wichtige Bälle
            "entscheidende_baelle" -> 2

            // Add other task IDs with custom frequencies here

            // Default is 1
            else -> 1
        }
    }

    /**
     * Gets the current completion status for a task
     * @return Pair of (current completions, max completions)
     */
    fun getTaskCompletionStatus(taskId: String): Pair<Int, Int> {
        val tasks = getAllTasks()
        val task = tasks.find { it.id == taskId } ?: return Pair(0, 1)
        val today = LocalDate.now()

        // Get max completions allowed for this task
        val maxCompletions = getMaxCompletionsForTask(task)

        // Get current completions for the relevant period
        val currentCompletions = when (task.frequency) {
            TaskFrequency.DAILY -> {
                // For daily tasks, it's either 0 or 1 for today
                if (task.completions?.contains(today) == true) 1 else 0
            }
            TaskFrequency.WEEKLY -> {
                // Count completions for this week (using Monday-based week)
                task.completions?.filter {
                    it.yearWeekKeyMonday() == today.yearWeekKeyMonday()
                }?.size ?: 0
            }
            TaskFrequency.MONTHLY -> {
                // Count completions for this month
                task.completions?.filter {
                    it.yearMonthKey() == today.yearMonthKey()
                }?.size ?: 0
            }
        }

        return Pair(currentCompletions, maxCompletions)
    }

    /**
     * Resets all tasks for the new week and archives the results
     * @return true if reset was performed, false otherwise
     */
    fun resetTasksForNewWeek(): Boolean {
        try {
            val today = LocalDate.now()
            val lastResetKey = "last_weekly_reset"
            val lastResetStr = sharedPreferences.getString(lastResetKey, null)

            // If we have a last reset date, check if it's from a previous week
            if (lastResetStr != null) {
                val lastReset = LocalDate.parse(lastResetStr)

                // If we're still in the same week (using Monday as first day), don't reset
                if (lastReset.yearWeekKeyMonday() == today.yearWeekKeyMonday()) {
                    return false
                }
            }

            // Get all tasks before resetting
            val tasks = getAllTasks()

            // Archive the current week's results before resetting
            archiveTaskResults(tasks)

            // Reset points counter
            sharedPreferences.edit().putInt("total_points", 0).apply()

            // Reset all weekly task completions
            val updatedTasks = tasks.map { task ->
                // For weekly tasks, clear completions from previous weeks
                if (task.frequency == TaskFrequency.WEEKLY) {
                    // Create a new task with empty completions list
                    task.copy(completions = mutableListOf())
                } else if (task.frequency == TaskFrequency.DAILY) {
                    // For daily tasks, also clear completions
                    task.copy(completions = mutableListOf())
                } else {
                    // For monthly tasks, keep completions from the current month
                    val currentMonth = today.yearMonthKey()
                    val filteredCompletions = task.completions?.filter { date ->
                        date.yearMonthKey() == currentMonth
                    }?.toMutableList() ?: mutableListOf()

                    task.copy(completions = filteredCompletions)
                }
            }

            // Save the updated tasks
            saveAllTasks(updatedTasks)

            // Update the last reset date
            sharedPreferences.edit().putString(lastResetKey, today.toString()).apply()

            return true
        } catch (e: Exception) {
            Log.e(TAG, "Error resetting tasks: ${e.message}")
            return false
        }
    }

    /**
     * Archives the current task results to history
     */
    private fun archiveTaskResults(tasks: List<Task>) {
        try {
            val today = LocalDate.now()
            val weekKey = today.yearWeekKeyMonday() // Use Monday-based week key

            // Calculate total points for the week
            val totalPoints = getTotalPoints()

            // Log the tasks for debugging
            Log.d(TAG, "Archiving tasks: ${tasks.size} tasks found")

            // Debug log to check completions
            tasks.forEach { task ->
                val completionsCount = task.completions?.size ?: 0
                if (completionsCount > 0) {
                    Log.d(TAG, "Task ${task.title} has $completionsCount completions")
                    task.completions?.forEach { date ->
                        Log.d(TAG, "  - Completion date: $date, week key: ${date.yearWeekKeyMonday()}")
                    }
                }
            }

            // Create a history entry with filtering for current week only
            val historyEntry = TaskHistoryEntry(
                weekKey = weekKey,
                endDate = today,
                totalPoints = totalPoints,
                taskCompletions = tasks.filter { task ->
                    // Only include tasks that have any completions
                    val hasCompletions = task.completions?.isNotEmpty() ?: false
                    if (hasCompletions) {
                        Log.d(TAG, "Task ${task.title} has completions")
                    }
                    hasCompletions
                }.map { task ->
                    // For each task, create a summary with all completions
                    // We'll count all completions since we're resetting them anyway
                    val completionsCount = task.completions?.size ?: 0

                    // In the archiveTaskResults method, when creating TaskCompletionSummary objects:

                    TaskCompletionSummary(
                        taskId = task.id,
                        category = task.category,
                        title = task.title,
                        completions = completionsCount,
                        maxCompletions = if (task.frequency == TaskFrequency.DAILY) 7 else getMaxCompletionsForTask(task),
                        points = task.points
                    )
                }.filter { it.completions > 0 } // Only include tasks with completions
            )

            // Log the filtered tasks
            Log.d(TAG, "Filtered tasks for history: ${historyEntry.taskCompletions.size} tasks")

            // Only add to history if there are actual completions
            if (historyEntry.taskCompletions.isNotEmpty()) {
                // Get existing history
                val historyJson = sharedPreferences.getString("task_history", null)
                val historyType = object : TypeToken<MutableList<TaskHistoryEntry>>() {}.type
                val history: MutableList<TaskHistoryEntry> = if (historyJson.isNullOrEmpty()) {
                    mutableListOf()
                } else {
                    try {
                        gson.fromJson(historyJson, historyType)
                    } catch (e: Exception) {
                        Log.e(TAG, "Error parsing history JSON: ${e.message}")
                        mutableListOf()
                    }
                }

                // Add new entry and save
                history.add(0, historyEntry) // Add at the beginning for newest first
                sharedPreferences.edit().putString("task_history", gson.toJson(history)).apply()

                // Log success
                Log.d(TAG, "Successfully archived ${historyEntry.taskCompletions.size} task completions")
            } else {
                Log.d(TAG, "No task completions to archive")
            }

        } catch (e: Exception) {
            Log.e(TAG, "Error archiving task results: ${e.message}")
            e.printStackTrace() // Add stack trace for more detailed debugging
        }
    }

    /**
     * Gets the task history
     */
    fun getTaskHistory(): List<TaskHistoryEntry> {
        val historyJson = sharedPreferences.getString("task_history", null)
        if (historyJson.isNullOrEmpty()) {
            return emptyList()
        }

        try {
            val historyType = object : TypeToken<List<TaskHistoryEntry>>() {}.type
            val history = gson.fromJson<List<TaskHistoryEntry>>(historyJson, historyType)

            // Log the history for debugging
            Log.d(TAG, "Retrieved ${history.size} history entries")

            return history
        } catch (e: Exception) {
            Log.e(TAG, "Error retrieving task history: ${e.message}")
            return emptyList()
        }
    }

    // Update the markTaskCompleted method to check frequency limits
    fun markTaskCompleted(taskId: String, date: LocalDate) {
        // Check if the task can be completed today
        if (!canCompleteTaskToday(taskId)) {
            // Task has reached its frequency limit
            return
        }

        val tasks = getAllTasks().toMutableList()
        val taskIndex = tasks.indexOfFirst { it.id == taskId }

        if (taskIndex != -1) {
            val task = tasks[taskIndex]

            // Create a new completions list if it's null
            val completions = task.completions?.toMutableList() ?: mutableListOf()

            // Only add if not already completed for this date
            if (!completions.contains(date)) {
                completions.add(date)
                tasks[taskIndex] = task.copy(completions = completions)
                saveAllTasks(tasks)
            }
        }
    }

    // Mark a task as completed with custom points
    fun markTaskCompletedWithCustomPoints(taskId: String, date: LocalDate, points: Int) {
        // Check if the task can be completed today
        if (!canCompleteTaskToday(taskId)) {
            // Task has reached its frequency limit
            return
        }

        val tasks = getAllTasks().toMutableList()
        val taskIndex = tasks.indexOfFirst { it.id == taskId }

        if (taskIndex != -1) {
            val task = tasks[taskIndex]

            // Create a new completions list if it's null
            val completions = task.completions?.toMutableList() ?: mutableListOf()

            // Create a new custom points map
            val customPoints = task.customPoints.toMutableMap()

            // Only add if not already completed for this date
            if (!completions.contains(date)) {
                completions.add(date)
                // Store the date as a string key with the custom points
                customPoints[date.toString()] = points
                tasks[taskIndex] = task.copy(completions = completions, customPoints = customPoints)
                saveAllTasks(tasks)
            }
        }
    }

    // Mark a task as uncompleted for a specific date
    fun markTaskUncompleted(taskId: String, date: LocalDate) {
        val tasks = getAllTasks().toMutableList()
        val taskIndex = tasks.indexOfFirst { it.id == taskId }

        if (taskIndex != -1) {
            val task = tasks[taskIndex]
            val completions = task.completions?.toMutableList() ?: mutableListOf()
            completions.removeIf { it == date }

            // Also remove any custom points for this date
            val customPoints = task.customPoints.toMutableMap()
            customPoints.remove(date.toString())

            tasks[taskIndex] = task.copy(completions = completions, customPoints = customPoints)
            saveAllTasks(tasks)
        }
    }

    // Check if a task is completed for a specific date
    fun isTaskCompletedForDate(taskId: String, date: LocalDate): Boolean {
        val tasks = getAllTasks()
        val task = tasks.find { it.id == taskId } ?: return false
        return task.completions?.contains(date) == true
    }

    // Get custom points for a task on a specific date, or null if no custom points are set
    fun getCustomPointsForTask(taskId: String, date: LocalDate): Int? {
        val tasks = getAllTasks()
        val task = tasks.find { it.id == taskId } ?: return null
        // Check if customPoints is null before accessing it
        return if (task.customPoints != null) task.customPoints[date.toString()] else null
    }

    // Check if a task should use custom points selection
    fun shouldUseCustomPoints(taskId: String): Boolean {
        return taskId in listOf("ruhig_unten_bleiben", "stoss_vorbereitung", "laecheln")
    }

    // Get total points earned
    fun getTotalPoints(): Int {
        try {
            val tasks = getAllTasks()
            var totalPoints = 0

            for (task in tasks) {
                val completions = task.completions ?: continue

                // Count completions based on frequency
                when (task.frequency) {
                    TaskFrequency.DAILY -> {
                        // Count each daily completion
                        for (date in completions) {
                            // Check if there are custom points for this date
                            val customPointsForDate = task.customPoints[date.toString()]
                            if (customPointsForDate != null) {
                                totalPoints += customPointsForDate
                            } else {
                                totalPoints += task.points
                            }
                        }
                    }
                    TaskFrequency.WEEKLY -> {
                        // Count weekly completions (group by week)
                        val weeklyCompletions = completions.groupBy { date ->
                            // Use a safer way to identify weeks
                            "${date.year}-W${date.dayOfYear / 7}"
                        }

                        // For each week, add points
                        for ((_, datesInWeek) in weeklyCompletions) {
                            // For weekly tasks, we only count one completion per week
                            // If there are custom points for any completion in this week, use the first one with custom points
                            val dateWithCustomPoints = datesInWeek.find { date -> task.customPoints.containsKey(date.toString()) }

                            if (dateWithCustomPoints != null) {
                                totalPoints += task.customPoints[dateWithCustomPoints.toString()] ?: task.points
                            } else {
                                totalPoints += task.points
                            }
                        }
                    }
                    TaskFrequency.MONTHLY -> {
                        // Count monthly completions (group by month)
                        val monthlyCompletions = completions.groupBy { date ->
                            "${date.year}-${date.monthValue}"
                        }

                        // For each month, add points
                        for ((_, datesInMonth) in monthlyCompletions) {
                            // For monthly tasks, we only count one completion per month
                            // If there are custom points for any completion in this month, use the first one with custom points
                            val dateWithCustomPoints = datesInMonth.find { date -> task.customPoints.containsKey(date.toString()) }

                            if (dateWithCustomPoints != null) {
                                totalPoints += task.customPoints[dateWithCustomPoints.toString()] ?: task.points
                            } else {
                                totalPoints += task.points
                            }
                        }
                    }
                }
            }

            return totalPoints
        } catch (e: Exception) {
            Log.e(TAG, "Error calculating points: ${e.message}")
            return 0
        }
    }
}