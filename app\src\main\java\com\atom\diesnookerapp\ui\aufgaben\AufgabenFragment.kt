package com.atom.diesnookerapp.ui.aufgaben

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.atom.diesnookerapp.R

class AufgabenFragment : Fragment() {
    private lateinit var recyclerView: RecyclerView
    private lateinit var taskManager: TaskManager  // Add this line

    // Define the task categories
    private val taskCategories = listOf(
        TaskCategory("Konzentration", "concentration"),
        TaskCategory("Technik", "technik"),
        TaskCategory("Matchplay", "matchplay"),
        TaskCategory("<PERSON>ichtige Bälle", "wichtige_baelle"),
        TaskCategory("Safeties", "safeties"),
        TaskCategory("Mental", "mental"),
        TaskCategory("Körpersprache", "koerpersprache"),
        TaskCategory("Breakbuilding", "breakbuilding"),
        TaskCategory("Fitness & Ernährung", "fitness_ernaehrung")
    )

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_aufgaben, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        // Initialize TaskManager
        taskManager = TaskManager(requireContext())

        // Check if tasks need to be reset for the new week
        val wasReset = taskManager.resetTasksForNewWeek()
        if (wasReset) {
            Toast.makeText(
                requireContext(),
                "Neue Woche begonnen! Aufgaben wurden zurückgesetzt.",
                Toast.LENGTH_LONG
            ).show()
        }

        // Initialize RecyclerView
        recyclerView = view.findViewById(R.id.taskCategoriesRecyclerView)
        recyclerView.layoutManager = LinearLayoutManager(requireContext())

        // Check if historyButton exists before trying to set a listener
        val historyButton = view.findViewById<ExtendedFloatingActionButton>(R.id.historyButton)
        historyButton.setOnClickListener {
            findNavController().navigate(R.id.action_navigation_aufgaben_to_taskHistoryFragment)
        }

        // Set up adapter
        val adapter = TaskCategoryAdapter(taskCategories) { category ->
            try {
                when (category.id) {
                    "concentration" -> {
                        findNavController().navigate(R.id.action_navigation_aufgaben_to_konzentrationFragment)
                    }
                    "technik" -> {
                        findNavController().navigate(R.id.action_navigation_aufgaben_to_technikFragment)
                    }
                    "matchplay" -> {
                        findNavController().navigate(R.id.action_navigation_aufgaben_to_matchplayFragment)
                    }
                    "wichtige_baelle" -> {
                        findNavController().navigate(R.id.action_navigation_aufgaben_to_wichtigeBaelleFragment)
                    }
                    "safeties" -> {
                        findNavController().navigate(R.id.action_navigation_aufgaben_to_safetiesFragment)
                    }
                    "mental" -> {
                        findNavController().navigate(R.id.action_navigation_aufgaben_to_mentalFragment)
                    }
                    "koerpersprache" -> {
                        findNavController().navigate(R.id.action_navigation_aufgaben_to_koerperspracheFragment)
                    }
                    "breakbuilding" -> {
                        findNavController().navigate(R.id.action_navigation_aufgaben_to_breakbuildingFragment)
                    }
                    "fitness_ernaehrung" -> {
                        findNavController().navigate(R.id.action_navigation_aufgaben_to_fitnessErnaehrungFragment)
                    }
                    else -> {
                        Toast.makeText(
                            requireContext(),
                            "Selected: ${category.name}",
                            Toast.LENGTH_SHORT
                        ).show()
                    }
                }
            } catch (e: Exception) {
                // Log the error and show a toast
                e.printStackTrace()
                Toast.makeText(
                    requireContext(),
                    "Navigation error: ${e.message}",
                    Toast.LENGTH_SHORT
                ).show()
            }
        }

        recyclerView.adapter = adapter
    }
}

// Data class to represent a task category
data class TaskCategory(
    val name: String,
    val id: String
)