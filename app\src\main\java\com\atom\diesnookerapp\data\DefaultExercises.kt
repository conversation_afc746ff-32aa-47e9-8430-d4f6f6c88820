package com.atom.diesnookerapp.data

import com.atom.diesnookerapp.data.firebase.UserExerciseDefinition

object DefaultExercises {

    fun getDefaults(): List<UserExerciseDefinition> {
        return listOf(
            // Breakbuilding
            // Breakbuilding
            UserExerciseDefinition(
                id = "default_line_up",
                name = "Line Up", // Often kept in German context
                category = "Breakbuilding", // Often kept in German context
                description = "Alle Roten in einer Aufstellung lochen, gef<PERSON><PERSON> von <PERSON>ben. Fokus auf Stellung.",
                isCustom = false,
                isDeletable = false,
                userId = "" // Placeholder, will be set dynamically
            ),
            UserExerciseDefinition(
                id = "default_t_break",
                name = "T-Break", // Often kept in German context
                category = "Breakbuilding",
                description = "Die Roten in einer 'T'-Formation abräumen, Farben nach jeder Roten nehmen.",
                isCustom = false,
                isDeletable = false,
                userId = ""
            ),
            // Potting (Lochen)
            UserExerciseDefinition(
                id = "default_long_pots",
                name = "Lange Bälle",
                category = "Potting", // Or "Lochen"
                description = "Üben, lange gerade Rote zu lochen.",
                isCustom = false,
                isDeletable = false,
                userId = ""
            ),
            UserExerciseDefinition(
                id = "default_blue_to_pink",
                name = "Blau auf Pink",
                category = "Potting", // Or "Lochen"
                description = "Blau lochen, dann Stellung auf Pink spielen und Pink lochen. Wiederholen.",
                isCustom = false,
                isDeletable = false,
                userId = ""
            ),
            // Safeties (Sicherheitsstöße)
            UserExerciseDefinition(
                id = "default_safety_from_baulk",
                name = "Safety aus dem Kopffeld",
                category = "Safeties", // Or "Sicherheit"
                description = "Einen Sicherheitsstoß aus dem Kopffeld spielen, um den Spielball sicher hinter einer Kopffarbe zu platzieren.",
                isCustom = false,
                isDeletable = false,
                userId = ""
            ),
            // Splits (Splits)
            UserExerciseDefinition(
                id = "default_red_splits",
                name = "Rote Splits",
                category = "Splits", // Often kept in German context
                description = "Üben, den Pulk der Roten beim Lochen einer Roten effektiv zu öffnen.",
                isCustom = false,
                isDeletable = false,
                userId = ""
            ),
            // Technik (Technique)
            UserExerciseDefinition(
                id = "default_straight_cueing",
                name = "Gerader Stoß",
                category = "Technik", // Already German
                description = "Bälle entlang einer geraden Linie lochen, um die Stoßführung zu verbessern.",
                isCustom = false,
                isDeletable = false,
                userId = ""
            ),
            // Stellungsspiel (Positional Play)
            UserExerciseDefinition(
                id = "default_colour_sequence",
                name = "Farbserie",
                category = "Stellungsspiel", // Already German
                description = "Die Farben in Reihenfolge (Gelb bis Schwarz) abräumen. Fokus auf Stellung für den nächsten Ball.",
                isCustom = false,
                isDeletable = false,
                userId = ""
            )
        )
    }
}
