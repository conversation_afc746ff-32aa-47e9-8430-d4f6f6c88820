package com.atom.diesnookerapp.data

import com.atom.diesnookerapp.data.firebase.UserExerciseDefinition

object DefaultExercises {

    fun getDefaults(): List<UserExerciseDefinition> {
        return listOf(
            // Breakbuilding
            UserExerciseDefinition(
                id = "default_t_break",
                name = "T Break",
                category = "Breakbuilding",
                description = "Die Roten in einer 'T'-Formation abräumen, Farben nach jeder Roten nehmen.",
                exerciseType = "normal",
                isCustom = false,
                isDeletable = false,
                userId = ""
            ),
            UserExerciseDefinition(
                id = "default_y_break",
                name = "Y Break",
                category = "Breakbuilding",
                description = "Die Roten in einer 'Y'-Formation abräumen.",
                exerciseType = "normal",
                isCustom = false,
                isDeletable = false,
                userId = ""
            ),
            UserExerciseDefinition(
                id = "default_line_up",
                name = "Line Up",
                category = "Breakbuilding",
                description = "Alle Roten in einer Aufstellung lochen, g<PERSON><PERSON><PERSON> von <PERSON>ben. Fokus auf Stellung.",
                exerciseType = "normal",
                isCustom = false,
                isDeletable = false,
                userId = ""
            ),
            UserExerciseDefinition(
                id = "default_8reds_break",
                name = "8reds Break",
                category = "Breakbuilding",
                description = "Break mit 8 roten Bällen aufbauen.",
                exerciseType = "normal",
                isCustom = false,
                isDeletable = false,
                userId = ""
            ),
            UserExerciseDefinition(
                id = "default_5reds_clear",
                name = "5reds clear",
                category = "Breakbuilding",
                description = "5 rote Bälle komplett abräumen.",
                exerciseType = "normal",
                isCustom = false,
                isDeletable = false,
                userId = ""
            ),
            UserExerciseDefinition(
                id = "default_50_break",
                name = "50+ Break",
                category = "Breakbuilding",
                description = "Einen Break von mindestens 50 Punkten erzielen.",
                exerciseType = "normal",
                isCustom = false,
                isDeletable = false,
                userId = ""
            ),
            UserExerciseDefinition(
                id = "default_frames",
                name = "Frames",
                category = "Breakbuilding",
                description = "Komplette Frames spielen.",
                exerciseType = "normal",
                isCustom = false,
                isDeletable = false,
                userId = ""
            ),
            UserExerciseDefinition(
                id = "default_x_break",
                name = "X Break",
                category = "Breakbuilding",
                description = "Break in X-Formation aufbauen.",
                exerciseType = "normal",
                isCustom = false,
                isDeletable = false,
                userId = ""
            ),
            UserExerciseDefinition(
                id = "default_black_routines",
                name = "Black Routines",
                category = "Breakbuilding",
                description = "Routinen mit dem schwarzen Ball trainieren.",
                exerciseType = "timeonly",
                isCustom = false,
                isDeletable = false,
                userId = ""
            ),
            UserExerciseDefinition(
                id = "default_brown_to_reds",
                name = "Brown to reds",
                category = "Breakbuilding",
                description = "Von Braun zu den roten Bällen spielen.",
                exerciseType = "timeonly",
                isCustom = false,
                isDeletable = false,
                userId = ""
            ),
            UserExerciseDefinition(
                id = "default_bc_to_reds",
                name = "BC to reds",
                category = "Breakbuilding",
                description = "Von Blau/Braun zu den roten Bällen spielen.",
                exerciseType = "timeonly",
                isCustom = false,
                isDeletable = false,
                userId = ""
            ),
            UserExerciseDefinition(
                id = "default_pj_routines",
                name = "PJ Routines",
                category = "Breakbuilding",
                description = "Spezielle PJ Trainingsroutinen.",
                exerciseType = "timeonly",
                isCustom = false,
                isDeletable = false,
                userId = ""
            ),
            UserExerciseDefinition(
                id = "default_random_bilder",
                name = "Random Bilder",
                category = "Breakbuilding",
                description = "Zufällige Ballstellungen trainieren.",
                exerciseType = "timeonly",
                isCustom = false,
                isDeletable = false,
                userId = ""
            ),

            // Potting
            UserExerciseDefinition(
                id = "default_blau_zu_pink",
                name = "Blau zu Pink",
                category = "Potting",
                description = "Blau lochen, dann Stellung auf Pink spielen und Pink lochen. Wiederholen.",
                exerciseType = "normal",
                isCustom = false,
                isDeletable = false,
                userId = ""
            ),
            UserExerciseDefinition(
                id = "default_8_long_shots",
                name = "8 Long shots",
                category = "Potting",
                description = "8 lange Bälle hintereinander lochen.",
                exerciseType = "timeonly",
                isCustom = false,
                isDeletable = false,
                userId = ""
            ),
            UserExerciseDefinition(
                id = "default_halblange_baelle_zu_blau",
                name = "Halblange Bälle zu Blau",
                category = "Potting",
                description = "Halblange Bälle lochen mit Stellung auf Blau.",
                exerciseType = "timeonly",
                isCustom = false,
                isDeletable = false,
                userId = ""
            ),
            UserExerciseDefinition(
                id = "default_rest",
                name = "Rest",
                category = "Potting",
                description = "Übungen mit dem Rest (Hilfsqueue).",
                exerciseType = "timeonly",
                isCustom = false,
                isDeletable = false,
                userId = ""
            ),

            // Safeties
            UserExerciseDefinition(
                id = "default_3reds_save",
                name = "3reds Save",
                category = "Safeties",
                description = "Sicherheitsstöße mit 3 roten Bällen.",
                exerciseType = "normal",
                isCustom = false,
                isDeletable = false,
                userId = ""
            ),
            UserExerciseDefinition(
                id = "default_snooker_legen",
                name = "Snooker legen",
                category = "Safeties",
                description = "Den Gegner in einen Snooker bringen.",
                exerciseType = "timeonly",
                isCustom = false,
                isDeletable = false,
                userId = ""
            ),
            UserExerciseDefinition(
                id = "default_von_schwarz_zu_bc",
                name = "Von Schwarz zu BC",
                category = "Safeties",
                description = "Sicherheitsstöße von Schwarz zu Blau/Braun.",
                exerciseType = "timeonly",
                isCustom = false,
                isDeletable = false,
                userId = ""
            ),
            UserExerciseDefinition(
                id = "default_blau_pink_schwarz",
                name = "Blau pink Schwarz",
                category = "Safeties",
                description = "Sicherheitsstöße mit den hohen Farben.",
                exerciseType = "timeonly",
                isCustom = false,
                isDeletable = false,
                userId = ""
            ),

            // Splits
            UserExerciseDefinition(
                id = "default_gelb_splits",
                name = "Gelb splits",
                category = "Splits",
                description = "Splits-Übungen mit dem gelben Ball.",
                exerciseType = "splits",
                isCustom = false,
                isDeletable = false,
                userId = ""
            ),
            UserExerciseDefinition(
                id = "default_gruen_splits",
                name = "Grün",
                category = "Splits",
                description = "Splits-Übungen mit dem grünen Ball.",
                exerciseType = "splits",
                isCustom = false,
                isDeletable = false,
                userId = ""
            ),
            UserExerciseDefinition(
                id = "default_braun_splits",
                name = "Braun",
                category = "Splits",
                description = "Splits-Übungen mit dem braunen Ball.",
                exerciseType = "splits",
                isCustom = false,
                isDeletable = false,
                userId = ""
            ),
            UserExerciseDefinition(
                id = "default_blau_splits",
                name = "Blau",
                category = "Splits",
                description = "Splits-Übungen mit dem blauen Ball.",
                exerciseType = "splits",
                isCustom = false,
                isDeletable = false,
                userId = ""
            ),
            UserExerciseDefinition(
                id = "default_schwarz_splits",
                name = "Schwarz",
                category = "Splits",
                description = "Splits-Übungen mit dem schwarzen Ball.",
                exerciseType = "splits",
                isCustom = false,
                isDeletable = false,
                userId = ""
            ),

            // Technik
            UserExerciseDefinition(
                id = "default_blue_doubletouch",
                name = "Blue Doubletouch",
                category = "Technik",
                description = "Doppelberührung mit dem blauen Ball vermeiden.",
                exerciseType = "normal",
                isCustom = false,
                isDeletable = false,
                userId = ""
            ),
            UserExerciseDefinition(
                id = "default_langer_stoss",
                name = "Langer Stoß",
                category = "Technik",
                description = "Technik für lange Stöße verbessern.",
                exerciseType = "timeonly",
                isCustom = false,
                isDeletable = false,
                userId = ""
            ),
            UserExerciseDefinition(
                id = "default_gerader_stoss_2_kreiden",
                name = "Gerader Stoß 2 Kreiden",
                category = "Technik",
                description = "Gerader Stoß über 2 Kreidenlängen.",
                exerciseType = "timeonly",
                isCustom = false,
                isDeletable = false,
                userId = ""
            ),

            // Stellungsspiel
            UserExerciseDefinition(
                id = "default_stellungsspiel_gelb",
                name = "Gelb",
                category = "Stellungsspiel",
                description = "Stellungsspiel-Übungen mit dem gelben Ball.",
                exerciseType = "stellungsspiel",
                isCustom = false,
                isDeletable = false,
                userId = ""
            ),
            UserExerciseDefinition(
                id = "default_stellungsspiel_gruen",
                name = "Grün",
                category = "Stellungsspiel",
                description = "Stellungsspiel-Übungen mit dem grünen Ball.",
                exerciseType = "stellungsspiel",
                isCustom = false,
                isDeletable = false,
                userId = ""
            ),
            UserExerciseDefinition(
                id = "default_stellungsspiel_braun",
                name = "Braun",
                category = "Stellungsspiel",
                description = "Stellungsspiel-Übungen mit dem braunen Ball.",
                exerciseType = "stellungsspiel",
                isCustom = false,
                isDeletable = false,
                userId = ""
            ),
            UserExerciseDefinition(
                id = "default_blau_durch_bc",
                name = "Blau durch BC",
                category = "Stellungsspiel",
                description = "Blau durch Braun/Blau spielen.",
                exerciseType = "normal",
                isCustom = false,
                isDeletable = false,
                userId = ""
            ),
            UserExerciseDefinition(
                id = "default_hohe_schwarze",
                name = "Hohe Schwarze",
                category = "Stellungsspiel",
                description = "Stellungsspiel mit hohen schwarzen Bällen.",
                exerciseType = "stellungsspiel",
                isCustom = false,
                isDeletable = false,
                userId = ""
            )
        )
    }}
