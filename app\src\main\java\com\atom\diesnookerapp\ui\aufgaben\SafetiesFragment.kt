package com.atom.diesnookerapp.ui.aufgaben

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.atom.diesnookerapp.R
import org.threeten.bp.LocalDate

class SafetiesFragment : Fragment() {
    private lateinit var recyclerView: RecyclerView
    private lateinit var pointsTextView: TextView
    private lateinit var titleTextView: TextView
    private lateinit var taskAdapter: TaskAdapter
    private lateinit var taskManager: TaskManager

    private val safetiesTasks = listOf(
        Task(
            id = "wenig_baelle",
            title = "Mit wenig Bällen",
            description = "Respotted black 20min",
            frequency = TaskFrequency.WEEKLY,
            points = 2,
            category = "safeties"
        ),
        Task(
            id = "escapes",
            title = "Escapes",
            description = "Versch Escapes hinlegen 20min",
            frequency = TaskFrequency.WEEKLY,
            points = 2,
            category = "safeties"
        )
    )

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_task_list, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        // Initialize TaskManager
        taskManager = TaskManager(requireContext())

        // Initialize views
        recyclerView = view.findViewById(R.id.tasksRecyclerView)
        pointsTextView = view.findViewById(R.id.pointsTextView)
        titleTextView = view.findViewById(R.id.categoryTitleTextView)

        // Set up RecyclerView
        recyclerView.layoutManager = LinearLayoutManager(requireContext())

        // Initialize tasks if needed
        taskManager.initializeTasksIfNeeded(safetiesTasks)

        // Get tasks from storage
        val tasks = taskManager.getTasksByCategory("safeties")

        // Set up adapter
        taskAdapter = TaskAdapter(
            tasks,
            onTaskCompleted = { task ->
                taskManager.markTaskCompleted(task.id, LocalDate.now())
                updatePointsDisplay()
            },
            onTaskUncompleted = { task ->
                taskManager.markTaskUncompleted(task.id, LocalDate.now())
                updatePointsDisplay()
            },
            getFrequencyText = { task ->
                when (task.frequency) {
                    TaskFrequency.DAILY -> "täglich"
                    TaskFrequency.WEEKLY -> when (task.id) {
                        "wenig_baelle" -> "2x pro Woche"
                        "escapes" -> "2x pro Woche"
                        else -> "wöchentlich"
                    }
                    TaskFrequency.MONTHLY -> "monatlich"
                }
            },
            taskManager = taskManager
        )

        recyclerView.adapter = taskAdapter

        // Set title
        titleTextView.text = "Safeties"

        // Update points display
        updatePointsDisplay()
    }

    private fun updatePointsDisplay() {
        val totalPoints = taskManager.getTotalPoints()
        pointsTextView.text = "Gesammelte Punkte: $totalPoints"
    }
}