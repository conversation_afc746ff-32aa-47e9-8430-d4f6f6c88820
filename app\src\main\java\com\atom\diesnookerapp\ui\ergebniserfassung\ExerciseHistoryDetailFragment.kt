package com.atom.diesnookerapp.ui.ergebniserfassung

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.atom.diesnookerapp.R
import org.threeten.bp.LocalDate
import org.threeten.bp.format.DateTimeFormatter
import java.util.Locale

class ExerciseHistoryDetailFragment : Fragment() {
    private lateinit var exercisePreferences: ExercisePreferences
    private lateinit var adapter: ExerciseHistoryAdapter
    private var selectedDate: LocalDate? = null
    private val dateFormatter = DateTimeFormatter.ofPattern("dd.MM.yyyy", Locale.GERMAN)

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.getString("date")?.let {
            selectedDate = LocalDate.parse(it)
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_exercise_history_detail, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        exercisePreferences = ExercisePreferences(requireContext())

        view.findViewById<TextView>(R.id.dateText).text = selectedDate?.format(dateFormatter)

        val recyclerView = view.findViewById<RecyclerView>(R.id.recyclerView)
        recyclerView.layoutManager = LinearLayoutManager(requireContext())
        adapter = ExerciseHistoryAdapter(emptyList())
        recyclerView.adapter = adapter

        selectedDate?.let { loadExercisesForDate(it) }
    }

    private fun loadExercisesForDate(date: LocalDate) {
        val startOfDay = date.atStartOfDay()
        val endOfDay = date.plusDays(1).atStartOfDay()

        val records = exercisePreferences.getRecords()
            .filter { record ->
                record.timestamp.isAfter(startOfDay) &&
                record.timestamp.isBefore(endOfDay)
            }
            .groupBy { it.exerciseId }
            .map { (exerciseId, exerciseRecords) ->
                val title = getExerciseTitle(exerciseId)
                val category = getExerciseCategory(exerciseId)

                // Convert all records to attempts, preserving completion markers
                val attempts = exerciseRecords
                    .sortedBy { it.timestamp }
                    .mapIndexed { index, record ->
                        Attempt(
                            number = index + 1,
                            score = record.score,
                            timeInMinutes = record.timeInMinutes,
                            spelt = record.spelt,
                            isCompletionMarker = record.isCompletionMarker
                        )
                    }

                ExerciseHistoryItem(
                    exerciseId = exerciseId,
                    exerciseTitle = title,
                    category = category,
                    attempts = attempts
                )
            }
            .groupBy { it.category }
            .flatMap { (category, items) ->
                listOf(
                    ExerciseHistoryItem(
                        exerciseId = "",
                        exerciseTitle = "",
                        category = category,
                        attempts = emptyList(),
                        isHeader = true
                    )
                ) + items.sortedBy { it.exerciseTitle }
            }

        adapter.updateItems(records)
    }

    private fun getExerciseTitle(exerciseId: String): String {
        return when (exerciseId) {
            // Potting exercises
            "blue_to_pink" -> "Blau zu Pink"
            "long_shots" -> "8 Long shots"
            "medium_to_blue" -> "Halblange Bälle zu Blau"
            "rest" -> "Rest"
            "blau_durch_bc" -> "Blau durch BC"

            // Breakbuilding exercises
            "line_up" -> "Line Up"
            "clearance" -> "Clearance"
            "sequence" -> "Sequence"
            "break_50" -> "Break 50"
            "break_75" -> "Break 75"
            "break_100" -> "Break 100"
            "break_125" -> "Break 125"
            "break_150" -> "Break 150"
            "pj_routines" -> "PJ Routines"
            "random_bilder" -> "Random Bilder"

            // Safeties exercises
            "safety_shots" -> "Safety Shots"
            "snooker_escape" -> "Snooker Escape"
            "defensive_position" -> "Defensive Position"
            "three_reds_save" -> "Three Reds Save"
            "snooker_legen" -> "Snooker legen"
            "black_to_bc" -> "Black to BC"
            "blue_pink_black" -> "Blue Pink Black"

            // Splits exercises
            "split_shots" -> "Split Shots"
            "cluster_break" -> "Cluster Break"
            "controlled_split" -> "Controlled Split"
            "black" -> "Schwarz"
            "blue" -> "Blau"
            "brown" -> "Braun"
            "yellow_splits" -> "Gelb"
            "green" -> "Grün"

            // Stellungsspiel exercises
            "stellungsspiel_gelb" -> "Gelb"
            "stellungsspiel_gruen" -> "Grün"
            "stellungsspiel_braun" -> "Braun"
            "hohe_schwarze" -> "Hohe Schwarze"

            // Technik exercises
            "blue_doubletouch" -> "Blue Doubletouch"
            "langer_stoss" -> "Langer Stoß"
            "gerader_stoss_2_kreiden" -> "Gerader Stoß 2 Kreiden"

            else -> exerciseId.split('_')
                .joinToString(" ") { it.capitalize() }
        }
    }

    private fun getExerciseCategory(exerciseId: String): String {
        val category = when {
            // Potting exercises
            exerciseId in listOf(
                "blue_to_pink", "long_shots", "medium_to_blue", "rest"
            ) -> "Potting"

            // Breakbuilding exercises
            exerciseId in listOf(
                "line_up", "clearance", "sequence",
                "break_50", "break_75", "break_100", "break_125", "break_150",
                "t_break", "fifty_plus", "x_break", "y_break", // Added y_break
                "eight_reds", "five_reds",  // Added five_reds
                "frames", "black_routines",  // Added frames and black_routines
                "brown_to_reds", "bc_to_reds", // Added these potting exercises
                "pj_routines", "random_bilder"
            ) -> {
                println("DEBUG: Found breakbuilding exercise: $exerciseId")
                "Breakbuilding"
            }

            // Safeties exercises
            exerciseId in listOf(
                "safety_shots", "snooker_escape", "defensive_position",
                "three_reds_save", "snooker_legen", "black_to_bc",
                "blue_pink_black"
            ) -> "Safeties"

            // Splits exercises
            exerciseId in listOf(
                "split_shots", "cluster_break", "controlled_split",
                "black", "blue", "brown", "yellow_splits", "green"
            ) -> "Splits"

            // Stellungsspiel exercises
            exerciseId in listOf(
                "stellungsspiel_gelb", "stellungsspiel_gruen", "stellungsspiel_braun", "blau_durch_bc", "hohe_schwarze"
            ) -> "Stellungsspiel"

            exerciseId in listOf(
                "blue_doubletouch", "langer_stoss", "gerader_stoss_2_kreiden"
            ) -> "Technik"

            else -> {
                println("DEBUG: Exercise '$exerciseId' not found in any category")
                "Andere"
            }
        }
        println("DEBUG: Exercise '$exerciseId' categorized as '$category'")
        return category
    }
}