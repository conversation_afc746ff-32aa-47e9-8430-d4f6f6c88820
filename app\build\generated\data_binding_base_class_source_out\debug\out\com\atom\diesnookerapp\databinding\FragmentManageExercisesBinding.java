// Generated by view binder compiler. Do not edit!
package com.atom.diesnookerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.atom.diesnookerapp.R;
import com.google.android.material.floatingactionbutton.FloatingActionButton;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentManageExercisesBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final FloatingActionButton addExerciseFab;

  @NonNull
  public final TextView emptyView;

  @NonNull
  public final RecyclerView exercisesRecyclerView;

  private FragmentManageExercisesBinding(@NonNull ConstraintLayout rootView,
      @NonNull FloatingActionButton addExerciseFab, @NonNull TextView emptyView,
      @NonNull RecyclerView exercisesRecyclerView) {
    this.rootView = rootView;
    this.addExerciseFab = addExerciseFab;
    this.emptyView = emptyView;
    this.exercisesRecyclerView = exercisesRecyclerView;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentManageExercisesBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentManageExercisesBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_manage_exercises, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentManageExercisesBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.addExerciseFab;
      FloatingActionButton addExerciseFab = ViewBindings.findChildViewById(rootView, id);
      if (addExerciseFab == null) {
        break missingId;
      }

      id = R.id.emptyView;
      TextView emptyView = ViewBindings.findChildViewById(rootView, id);
      if (emptyView == null) {
        break missingId;
      }

      id = R.id.exercisesRecyclerView;
      RecyclerView exercisesRecyclerView = ViewBindings.findChildViewById(rootView, id);
      if (exercisesRecyclerView == null) {
        break missingId;
      }

      return new FragmentManageExercisesBinding((ConstraintLayout) rootView, addExerciseFab,
          emptyView, exercisesRecyclerView);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
