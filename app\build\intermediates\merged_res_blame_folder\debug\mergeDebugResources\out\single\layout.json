[{"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-mergeDebugResources-82:\\layout\\theme_preview_dark_blue.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\theme_preview_dark_blue.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-mergeDebugResources-82:\\layout\\item_user_connection.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\item_user_connection.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-mergeDebugResources-82:\\layout\\item_exercise_definition.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\item_exercise_definition.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-mergeDebugResources-82:\\layout\\fragment_exercise_graph.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\fragment_exercise_graph.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-mergeDebugResources-82:\\layout\\fragment_exercise_detail_timeonly.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\fragment_exercise_detail_timeonly.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-mergeDebugResources-82:\\layout\\fragment_potting.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\fragment_potting.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-mergeDebugResources-82:\\layout\\fragment_task_list.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\fragment_task_list.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-mergeDebugResources-82:\\layout\\fragment_profile.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\fragment_profile.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-mergeDebugResources-82:\\layout\\dialog_add_exercise.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\dialog_add_exercise.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-mergeDebugResources-82:\\layout\\dialog_exercise_selection.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\dialog_exercise_selection.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-mergeDebugResources-82:\\layout\\fragment_before_tournament.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\fragment_before_tournament.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-mergeDebugResources-82:\\layout\\item_history_detail.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\item_history_detail.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-mergeDebugResources-82:\\layout\\exercise_selection_item.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\exercise_selection_item.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-mergeDebugResources-82:\\layout\\trainingsplan_history_item.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\trainingsplan_history_item.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-mergeDebugResources-82:\\layout\\item_task_completion.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\item_task_completion.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-mergeDebugResources-82:\\layout\\item_task.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\item_task.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-mergeDebugResources-82:\\layout\\item_assessment.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\item_assessment.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-mergeDebugResources-82:\\layout\\fragment_exercise_history.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\fragment_exercise_history.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-mergeDebugResources-82:\\layout\\dialog_custom_timeframe.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\dialog_custom_timeframe.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-mergeDebugResources-82:\\layout\\dialog_select_points.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\dialog_select_points.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-mergeDebugResources-82:\\layout\\fragment_trainingsplan.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\fragment_trainingsplan.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-mergeDebugResources-82:\\layout\\item_exercise_history.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\item_exercise_history.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-mergeDebugResources-82:\\layout\\fragment_task_history.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\fragment_task_history.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-mergeDebugResources-82:\\layout\\fragment_user_management.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\fragment_user_management.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-mergeDebugResources-82:\\layout\\item_training_assessment.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\item_training_assessment.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-mergeDebugResources-82:\\layout\\theme_preview_light.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\theme_preview_light.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-mergeDebugResources-82:\\layout\\fragment_exercise_detail_stellungsspiel.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\fragment_exercise_detail_stellungsspiel.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-mergeDebugResources-82:\\layout\\fragment_after_training.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\fragment_after_training.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-mergeDebugResources-82:\\layout\\fragment_splits.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\fragment_splits.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-mergeDebugResources-82:\\layout\\dialog_legend.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\dialog_legend.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-mergeDebugResources-82:\\layout\\fragment_technik.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\fragment_technik.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-mergeDebugResources-82:\\layout\\fragment_graph.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\fragment_graph.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-mergeDebugResources-82:\\layout\\item_task_history.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\item_task_history.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-mergeDebugResources-82:\\layout\\fragment_exercise_detail.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\fragment_exercise_detail.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-mergeDebugResources-82:\\layout\\fragment_questions.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\fragment_questions.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-mergeDebugResources-82:\\layout\\fragment_history_detail.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\fragment_history_detail.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-mergeDebugResources-82:\\layout\\fragment_exercise_history_detail.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\fragment_exercise_history_detail.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-mergeDebugResources-82:\\layout\\fragment_breakbuilding.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\fragment_breakbuilding.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-mergeDebugResources-82:\\layout\\fragment_trainingsplan_history.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\fragment_trainingsplan_history.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-mergeDebugResources-82:\\layout\\item_question.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\item_question.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-mergeDebugResources-82:\\layout\\item_legend.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\item_legend.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-mergeDebugResources-82:\\layout\\theme_preview_ocean.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\theme_preview_ocean.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-mergeDebugResources-82:\\layout\\fragment_aufgaben.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\fragment_aufgaben.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-mergeDebugResources-82:\\layout\\item_task_category.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\item_task_category.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-mergeDebugResources-82:\\layout\\theme_preview_neon.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\theme_preview_neon.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-mergeDebugResources-82:\\layout\\fragment_stellungsspiel.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\fragment_stellungsspiel.xml"}, {"merged": "com.atom.diesnookerapp-mergeDebugResources-82:/layout/dropdown_item.xml", "source": "com.atom.diesnookerapp-main-85:/layout/dropdown_item.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-mergeDebugResources-82:\\layout\\fragment_exercise_detail_splits.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\fragment_exercise_detail_splits.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-mergeDebugResources-82:\\layout\\theme_preview_blue.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\theme_preview_blue.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-mergeDebugResources-82:\\layout\\trainingsplan_item.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\trainingsplan_item.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-mergeDebugResources-82:\\layout\\dropdown_item.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\dropdown_item.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-mergeDebugResources-82:\\layout\\activity_main.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\activity_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-mergeDebugResources-82:\\layout\\theme_preview_crimson.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\theme_preview_crimson.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-mergeDebugResources-82:\\layout\\item_exercise_category.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\item_exercise_category.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-mergeDebugResources-82:\\layout\\fragment_ergebniserfassung.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\fragment_ergebniserfassung.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-mergeDebugResources-82:\\layout\\fragment_selbsteinschaetzung.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\fragment_selbsteinschaetzung.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-mergeDebugResources-82:\\layout\\dialog_edit_connection.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\dialog_edit_connection.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-mergeDebugResources-82:\\layout\\item_breakbuilding.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\item_breakbuilding.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-mergeDebugResources-82:\\layout\\theme_preview_snooker.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\theme_preview_snooker.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-mergeDebugResources-82:\\layout\\fragment_register.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\fragment_register.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-mergeDebugResources-82:\\layout\\item_task_category_header.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\item_task_category_header.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-mergeDebugResources-82:\\layout\\fragment_login.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\fragment_login.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-mergeDebugResources-82:\\layout\\fragment_graph_detail.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\fragment_graph_detail.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-mergeDebugResources-82:\\layout\\fragment_manage_exercises.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\fragment_manage_exercises.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-mergeDebugResources-82:\\layout\\fragment_history.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\fragment_history.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-mergeDebugResources-82:\\layout\\fragment_questions_before_tournament.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\fragment_questions_before_tournament.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-mergeDebugResources-82:\\layout\\item_task_completion_header.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\item_task_completion_header.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-mergeDebugResources-82:\\layout\\fragment_safeties.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\fragment_safeties.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-mergeDebugResources-82:\\layout\\fragment_generic_exercise_list.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\fragment_generic_exercise_list.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-mergeDebugResources-82:\\layout\\fragment_after_tournament.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\fragment_after_tournament.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-mergeDebugResources-82:\\layout\\item_month_header.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\item_month_header.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-mergeDebugResources-82:\\layout\\fragment_questions_before_training.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\fragment_questions_before_training.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-mergeDebugResources-82:\\layout\\fragment_exercise_selection.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\fragment_exercise_selection.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-mergeDebugResources-82:\\layout\\item_exercise.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\item_exercise.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-mergeDebugResources-82:\\layout\\fragment_before_training.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\fragment_before_training.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-mergeDebugResources-82:\\layout\\marker_view.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\marker_view.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-mergeDebugResources-82:\\layout\\item_date.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\item_date.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-mergeDebugResources-82:\\layout\\fragment_questions_after_tournament.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\fragment_questions_after_tournament.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-mergeDebugResources-82:\\layout\\dialog_custom_exercise.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\dialog_custom_exercise.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-mergeDebugResources-82:\\layout\\fragment_settings.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\fragment_settings.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-mergeDebugResources-82:\\layout\\fragment_questions_after_training.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\fragment_questions_after_training.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-mergeDebugResources-82:\\layout\\theme_preview_dark.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\theme_preview_dark.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-mergeDebugResources-82:\\layout\\item_history.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\item_history.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-mergeDebugResources-82:\\layout\\dialog_add_edit_exercise.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.atom.diesnookerapp-main-85:\\layout\\dialog_add_edit_exercise.xml"}]