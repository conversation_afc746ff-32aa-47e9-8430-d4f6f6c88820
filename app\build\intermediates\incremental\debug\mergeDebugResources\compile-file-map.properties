#Tue Jun 03 21:55:34 CEST 2025
com.atom.diesnookerapp-main-85\:/anim/slide_down.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\anim_slide_down.xml.flat
com.atom.diesnookerapp-main-85\:/anim/slide_up.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\anim_slide_up.xml.flat
com.atom.diesnookerapp-main-85\:/drawable/baseline_attractions_24.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_baseline_attractions_24.xml.flat
com.atom.diesnookerapp-main-85\:/drawable/dropdown_background.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_dropdown_background.xml.flat
com.atom.diesnookerapp-main-85\:/drawable/dropdown_background_blue.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_dropdown_background_blue.xml.flat
com.atom.diesnookerapp-main-85\:/drawable/dropdown_background_blue_dark.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_dropdown_background_blue_dark.xml.flat
com.atom.diesnookerapp-main-85\:/drawable/dropdown_background_crimson.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_dropdown_background_crimson.xml.flat
com.atom.diesnookerapp-main-85\:/drawable/dropdown_background_crimson_dark.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_dropdown_background_crimson_dark.xml.flat
com.atom.diesnookerapp-main-85\:/drawable/dropdown_background_dark.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_dropdown_background_dark.xml.flat
com.atom.diesnookerapp-main-85\:/drawable/dropdown_background_dark_blue.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_dropdown_background_dark_blue.xml.flat
com.atom.diesnookerapp-main-85\:/drawable/dropdown_background_dark_blue_dark.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_dropdown_background_dark_blue_dark.xml.flat
com.atom.diesnookerapp-main-85\:/drawable/dropdown_background_neon.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_dropdown_background_neon.xml.flat
com.atom.diesnookerapp-main-85\:/drawable/dropdown_background_neon_dark.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_dropdown_background_neon_dark.xml.flat
com.atom.diesnookerapp-main-85\:/drawable/dropdown_background_ocean.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_dropdown_background_ocean.xml.flat
com.atom.diesnookerapp-main-85\:/drawable/dropdown_background_ocean_dark.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_dropdown_background_ocean_dark.xml.flat
com.atom.diesnookerapp-main-85\:/drawable/dropdown_background_snooker.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_dropdown_background_snooker.xml.flat
com.atom.diesnookerapp-main-85\:/drawable/dropdown_background_snooker_dark.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_dropdown_background_snooker_dark.xml.flat
com.atom.diesnookerapp-main-85\:/drawable/dropdown_background_solid_blue.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_dropdown_background_solid_blue.xml.flat
com.atom.diesnookerapp-main-85\:/drawable/dropdown_background_solid_crimson.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_dropdown_background_solid_crimson.xml.flat
com.atom.diesnookerapp-main-85\:/drawable/dropdown_background_solid_dark.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_dropdown_background_solid_dark.xml.flat
com.atom.diesnookerapp-main-85\:/drawable/dropdown_background_solid_dark_blue.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_dropdown_background_solid_dark_blue.xml.flat
com.atom.diesnookerapp-main-85\:/drawable/dropdown_background_solid_default.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_dropdown_background_solid_default.xml.flat
com.atom.diesnookerapp-main-85\:/drawable/dropdown_background_solid_neon.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_dropdown_background_solid_neon.xml.flat
com.atom.diesnookerapp-main-85\:/drawable/dropdown_background_solid_ocean.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_dropdown_background_solid_ocean.xml.flat
com.atom.diesnookerapp-main-85\:/drawable/dropdown_background_solid_snooker.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_dropdown_background_solid_snooker.xml.flat
com.atom.diesnookerapp-main-85\:/drawable/ic_add.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_add.xml.flat
com.atom.diesnookerapp-main-85\:/drawable/ic_add_exercise_trainingsplan.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_add_exercise_trainingsplan.xml.flat
com.atom.diesnookerapp-main-85\:/drawable/ic_arrow_forward.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_arrow_forward.xml.flat
com.atom.diesnookerapp-main-85\:/drawable/ic_assessment.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_assessment.xml.flat
com.atom.diesnookerapp-main-85\:/drawable/ic_assessment_new.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_assessment_new.xml.flat
com.atom.diesnookerapp-main-85\:/drawable/ic_calendar.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_calendar.xml.flat
com.atom.diesnookerapp-main-85\:/drawable/ic_delete.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_delete.xml.flat
com.atom.diesnookerapp-main-85\:/drawable/ic_fitness.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_fitness.xml.flat
com.atom.diesnookerapp-main-85\:/drawable/ic_graph.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_graph.xml.flat
com.atom.diesnookerapp-main-85\:/drawable/ic_history.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_history.xml.flat
com.atom.diesnookerapp-main-85\:/drawable/ic_launcher_background.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_launcher_background.xml.flat
com.atom.diesnookerapp-main-85\:/drawable/ic_launcher_foreground.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_launcher_foreground.xml.flat
com.atom.diesnookerapp-main-85\:/drawable/ic_notification.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_notification.xml.flat
com.atom.diesnookerapp-main-85\:/drawable/ic_remove_exercise_trainingsplan.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_remove_exercise_trainingsplan.xml.flat
com.atom.diesnookerapp-main-85\:/drawable/ic_results.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_results.xml.flat
com.atom.diesnookerapp-main-85\:/drawable/ic_results_new.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_results_new.xml.flat
com.atom.diesnookerapp-main-85\:/drawable/ic_save.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_save.xml.flat
com.atom.diesnookerapp-main-85\:/drawable/ic_settings.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_settings.xml.flat
com.atom.diesnookerapp-main-85\:/drawable/ic_tasks.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_tasks.xml.flat
com.atom.diesnookerapp-main-85\:/drawable/ic_tasks_new.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_tasks_new.xml.flat
com.atom.diesnookerapp-main-85\:/drawable/ic_timer.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_timer.xml.flat
com.atom.diesnookerapp-main-85\:/drawable/ic_training_new.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_training_new.xml.flat
com.atom.diesnookerapp-main-85\:/drawable/marker_background.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_marker_background.xml.flat
com.atom.diesnookerapp-main-85\:/menu/bottom_nav_menu.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\menu_bottom_nav_menu.xml.flat
com.atom.diesnookerapp-main-85\:/menu/menu_history.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\menu_menu_history.xml.flat
com.atom.diesnookerapp-main-85\:/menu/menu_timeframe.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\menu_menu_timeframe.xml.flat
com.atom.diesnookerapp-main-85\:/menu/menu_timeframe_simple.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\menu_menu_timeframe_simple.xml.flat
com.atom.diesnookerapp-main-85\:/mipmap-anydpi-v26/ic_launcher.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-anydpi-v26_ic_launcher.xml.flat
com.atom.diesnookerapp-main-85\:/mipmap-anydpi-v26/ic_launcher_round.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-anydpi-v26_ic_launcher_round.xml.flat
com.atom.diesnookerapp-main-85\:/mipmap-hdpi/ic_launcher.webp=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-hdpi_ic_launcher.webp.flat
com.atom.diesnookerapp-main-85\:/mipmap-hdpi/ic_launcher_background.webp=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-hdpi_ic_launcher_background.webp.flat
com.atom.diesnookerapp-main-85\:/mipmap-hdpi/ic_launcher_foreground.webp=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-hdpi_ic_launcher_foreground.webp.flat
com.atom.diesnookerapp-main-85\:/mipmap-hdpi/ic_launcher_round.webp=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-hdpi_ic_launcher_round.webp.flat
com.atom.diesnookerapp-main-85\:/mipmap-mdpi/ic_launcher.webp=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-mdpi_ic_launcher.webp.flat
com.atom.diesnookerapp-main-85\:/mipmap-mdpi/ic_launcher_background.webp=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-mdpi_ic_launcher_background.webp.flat
com.atom.diesnookerapp-main-85\:/mipmap-mdpi/ic_launcher_foreground.webp=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-mdpi_ic_launcher_foreground.webp.flat
com.atom.diesnookerapp-main-85\:/mipmap-mdpi/ic_launcher_round.webp=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-mdpi_ic_launcher_round.webp.flat
com.atom.diesnookerapp-main-85\:/mipmap-xhdpi/ic_launcher.webp=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xhdpi_ic_launcher.webp.flat
com.atom.diesnookerapp-main-85\:/mipmap-xhdpi/ic_launcher_background.webp=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xhdpi_ic_launcher_background.webp.flat
com.atom.diesnookerapp-main-85\:/mipmap-xhdpi/ic_launcher_foreground.webp=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xhdpi_ic_launcher_foreground.webp.flat
com.atom.diesnookerapp-main-85\:/mipmap-xhdpi/ic_launcher_round.webp=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xhdpi_ic_launcher_round.webp.flat
com.atom.diesnookerapp-main-85\:/mipmap-xxhdpi/ic_launcher.webp=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxhdpi_ic_launcher.webp.flat
com.atom.diesnookerapp-main-85\:/mipmap-xxhdpi/ic_launcher_background.webp=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxhdpi_ic_launcher_background.webp.flat
com.atom.diesnookerapp-main-85\:/mipmap-xxhdpi/ic_launcher_foreground.webp=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxhdpi_ic_launcher_foreground.webp.flat
com.atom.diesnookerapp-main-85\:/mipmap-xxhdpi/ic_launcher_round.webp=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxhdpi_ic_launcher_round.webp.flat
com.atom.diesnookerapp-main-85\:/mipmap-xxxhdpi/ic_launcher.webp=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxxhdpi_ic_launcher.webp.flat
com.atom.diesnookerapp-main-85\:/mipmap-xxxhdpi/ic_launcher_background.webp=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxxhdpi_ic_launcher_background.webp.flat
com.atom.diesnookerapp-main-85\:/mipmap-xxxhdpi/ic_launcher_foreground.webp=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxxhdpi_ic_launcher_foreground.webp.flat
com.atom.diesnookerapp-main-85\:/mipmap-xxxhdpi/ic_launcher_round.webp=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxxhdpi_ic_launcher_round.webp.flat
com.atom.diesnookerapp-main-85\:/navigation/auth_nav_graph.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\navigation_auth_nav_graph.xml.flat
com.atom.diesnookerapp-main-85\:/navigation/mobile_navigation.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\navigation_mobile_navigation.xml.flat
com.atom.diesnookerapp-main-85\:/navigation/nav_graph.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\navigation_nav_graph.xml.flat
com.atom.diesnookerapp-main-85\:/xml/backup_rules.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_backup_rules.xml.flat
com.atom.diesnookerapp-main-85\:/xml/data_extraction_rules.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_data_extraction_rules.xml.flat
com.atom.diesnookerapp-mergeDebugResources-82\:/layout/activity_main.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_main.xml.flat
com.atom.diesnookerapp-mergeDebugResources-82\:/layout/dialog_add_edit_exercise.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_add_edit_exercise.xml.flat
com.atom.diesnookerapp-mergeDebugResources-82\:/layout/dialog_add_exercise.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_add_exercise.xml.flat
com.atom.diesnookerapp-mergeDebugResources-82\:/layout/dialog_custom_exercise.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_custom_exercise.xml.flat
com.atom.diesnookerapp-mergeDebugResources-82\:/layout/dialog_custom_timeframe.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_custom_timeframe.xml.flat
com.atom.diesnookerapp-mergeDebugResources-82\:/layout/dialog_edit_connection.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_edit_connection.xml.flat
com.atom.diesnookerapp-mergeDebugResources-82\:/layout/dialog_exercise_selection.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_exercise_selection.xml.flat
com.atom.diesnookerapp-mergeDebugResources-82\:/layout/dialog_legend.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_legend.xml.flat
com.atom.diesnookerapp-mergeDebugResources-82\:/layout/dialog_select_points.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_select_points.xml.flat
com.atom.diesnookerapp-mergeDebugResources-82\:/layout/dropdown_item.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dropdown_item.xml.flat
com.atom.diesnookerapp-mergeDebugResources-82\:/layout/exercise_selection_item.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_exercise_selection_item.xml.flat
com.atom.diesnookerapp-mergeDebugResources-82\:/layout/fragment_after_tournament.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_after_tournament.xml.flat
com.atom.diesnookerapp-mergeDebugResources-82\:/layout/fragment_after_training.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_after_training.xml.flat
com.atom.diesnookerapp-mergeDebugResources-82\:/layout/fragment_aufgaben.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_aufgaben.xml.flat
com.atom.diesnookerapp-mergeDebugResources-82\:/layout/fragment_before_tournament.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_before_tournament.xml.flat
com.atom.diesnookerapp-mergeDebugResources-82\:/layout/fragment_before_training.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_before_training.xml.flat
com.atom.diesnookerapp-mergeDebugResources-82\:/layout/fragment_breakbuilding.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_breakbuilding.xml.flat
com.atom.diesnookerapp-mergeDebugResources-82\:/layout/fragment_ergebniserfassung.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_ergebniserfassung.xml.flat
com.atom.diesnookerapp-mergeDebugResources-82\:/layout/fragment_exercise_detail.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_exercise_detail.xml.flat
com.atom.diesnookerapp-mergeDebugResources-82\:/layout/fragment_exercise_detail_splits.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_exercise_detail_splits.xml.flat
com.atom.diesnookerapp-mergeDebugResources-82\:/layout/fragment_exercise_detail_stellungsspiel.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_exercise_detail_stellungsspiel.xml.flat
com.atom.diesnookerapp-mergeDebugResources-82\:/layout/fragment_exercise_detail_timeonly.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_exercise_detail_timeonly.xml.flat
com.atom.diesnookerapp-mergeDebugResources-82\:/layout/fragment_exercise_graph.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_exercise_graph.xml.flat
com.atom.diesnookerapp-mergeDebugResources-82\:/layout/fragment_exercise_history.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_exercise_history.xml.flat
com.atom.diesnookerapp-mergeDebugResources-82\:/layout/fragment_exercise_history_detail.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_exercise_history_detail.xml.flat
com.atom.diesnookerapp-mergeDebugResources-82\:/layout/fragment_exercise_selection.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_exercise_selection.xml.flat
com.atom.diesnookerapp-mergeDebugResources-82\:/layout/fragment_generic_exercise_list.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_generic_exercise_list.xml.flat
com.atom.diesnookerapp-mergeDebugResources-82\:/layout/fragment_graph.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_graph.xml.flat
com.atom.diesnookerapp-mergeDebugResources-82\:/layout/fragment_graph_detail.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_graph_detail.xml.flat
com.atom.diesnookerapp-mergeDebugResources-82\:/layout/fragment_history.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_history.xml.flat
com.atom.diesnookerapp-mergeDebugResources-82\:/layout/fragment_history_detail.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_history_detail.xml.flat
com.atom.diesnookerapp-mergeDebugResources-82\:/layout/fragment_login.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_login.xml.flat
com.atom.diesnookerapp-mergeDebugResources-82\:/layout/fragment_manage_exercises.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_manage_exercises.xml.flat
com.atom.diesnookerapp-mergeDebugResources-82\:/layout/fragment_potting.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_potting.xml.flat
com.atom.diesnookerapp-mergeDebugResources-82\:/layout/fragment_profile.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_profile.xml.flat
com.atom.diesnookerapp-mergeDebugResources-82\:/layout/fragment_questions.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_questions.xml.flat
com.atom.diesnookerapp-mergeDebugResources-82\:/layout/fragment_questions_after_tournament.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_questions_after_tournament.xml.flat
com.atom.diesnookerapp-mergeDebugResources-82\:/layout/fragment_questions_after_training.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_questions_after_training.xml.flat
com.atom.diesnookerapp-mergeDebugResources-82\:/layout/fragment_questions_before_tournament.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_questions_before_tournament.xml.flat
com.atom.diesnookerapp-mergeDebugResources-82\:/layout/fragment_questions_before_training.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_questions_before_training.xml.flat
com.atom.diesnookerapp-mergeDebugResources-82\:/layout/fragment_register.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_register.xml.flat
com.atom.diesnookerapp-mergeDebugResources-82\:/layout/fragment_safeties.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_safeties.xml.flat
com.atom.diesnookerapp-mergeDebugResources-82\:/layout/fragment_selbsteinschaetzung.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_selbsteinschaetzung.xml.flat
com.atom.diesnookerapp-mergeDebugResources-82\:/layout/fragment_settings.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_settings.xml.flat
com.atom.diesnookerapp-mergeDebugResources-82\:/layout/fragment_splits.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_splits.xml.flat
com.atom.diesnookerapp-mergeDebugResources-82\:/layout/fragment_stellungsspiel.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_stellungsspiel.xml.flat
com.atom.diesnookerapp-mergeDebugResources-82\:/layout/fragment_task_history.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_task_history.xml.flat
com.atom.diesnookerapp-mergeDebugResources-82\:/layout/fragment_task_list.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_task_list.xml.flat
com.atom.diesnookerapp-mergeDebugResources-82\:/layout/fragment_technik.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_technik.xml.flat
com.atom.diesnookerapp-mergeDebugResources-82\:/layout/fragment_trainingsplan.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_trainingsplan.xml.flat
com.atom.diesnookerapp-mergeDebugResources-82\:/layout/fragment_trainingsplan_history.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_trainingsplan_history.xml.flat
com.atom.diesnookerapp-mergeDebugResources-82\:/layout/fragment_user_management.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_user_management.xml.flat
com.atom.diesnookerapp-mergeDebugResources-82\:/layout/item_assessment.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_assessment.xml.flat
com.atom.diesnookerapp-mergeDebugResources-82\:/layout/item_breakbuilding.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_breakbuilding.xml.flat
com.atom.diesnookerapp-mergeDebugResources-82\:/layout/item_date.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_date.xml.flat
com.atom.diesnookerapp-mergeDebugResources-82\:/layout/item_exercise.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_exercise.xml.flat
com.atom.diesnookerapp-mergeDebugResources-82\:/layout/item_exercise_category.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_exercise_category.xml.flat
com.atom.diesnookerapp-mergeDebugResources-82\:/layout/item_exercise_definition.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_exercise_definition.xml.flat
com.atom.diesnookerapp-mergeDebugResources-82\:/layout/item_exercise_history.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_exercise_history.xml.flat
com.atom.diesnookerapp-mergeDebugResources-82\:/layout/item_history.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_history.xml.flat
com.atom.diesnookerapp-mergeDebugResources-82\:/layout/item_history_detail.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_history_detail.xml.flat
com.atom.diesnookerapp-mergeDebugResources-82\:/layout/item_legend.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_legend.xml.flat
com.atom.diesnookerapp-mergeDebugResources-82\:/layout/item_month_header.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_month_header.xml.flat
com.atom.diesnookerapp-mergeDebugResources-82\:/layout/item_question.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_question.xml.flat
com.atom.diesnookerapp-mergeDebugResources-82\:/layout/item_task.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_task.xml.flat
com.atom.diesnookerapp-mergeDebugResources-82\:/layout/item_task_category.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_task_category.xml.flat
com.atom.diesnookerapp-mergeDebugResources-82\:/layout/item_task_category_header.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_task_category_header.xml.flat
com.atom.diesnookerapp-mergeDebugResources-82\:/layout/item_task_completion.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_task_completion.xml.flat
com.atom.diesnookerapp-mergeDebugResources-82\:/layout/item_task_completion_header.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_task_completion_header.xml.flat
com.atom.diesnookerapp-mergeDebugResources-82\:/layout/item_task_history.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_task_history.xml.flat
com.atom.diesnookerapp-mergeDebugResources-82\:/layout/item_training_assessment.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_training_assessment.xml.flat
com.atom.diesnookerapp-mergeDebugResources-82\:/layout/item_user_connection.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_user_connection.xml.flat
com.atom.diesnookerapp-mergeDebugResources-82\:/layout/marker_view.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_marker_view.xml.flat
com.atom.diesnookerapp-mergeDebugResources-82\:/layout/theme_preview_blue.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_theme_preview_blue.xml.flat
com.atom.diesnookerapp-mergeDebugResources-82\:/layout/theme_preview_crimson.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_theme_preview_crimson.xml.flat
com.atom.diesnookerapp-mergeDebugResources-82\:/layout/theme_preview_dark.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_theme_preview_dark.xml.flat
com.atom.diesnookerapp-mergeDebugResources-82\:/layout/theme_preview_dark_blue.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_theme_preview_dark_blue.xml.flat
com.atom.diesnookerapp-mergeDebugResources-82\:/layout/theme_preview_light.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_theme_preview_light.xml.flat
com.atom.diesnookerapp-mergeDebugResources-82\:/layout/theme_preview_neon.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_theme_preview_neon.xml.flat
com.atom.diesnookerapp-mergeDebugResources-82\:/layout/theme_preview_ocean.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_theme_preview_ocean.xml.flat
com.atom.diesnookerapp-mergeDebugResources-82\:/layout/theme_preview_snooker.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_theme_preview_snooker.xml.flat
com.atom.diesnookerapp-mergeDebugResources-82\:/layout/trainingsplan_history_item.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_trainingsplan_history_item.xml.flat
com.atom.diesnookerapp-mergeDebugResources-82\:/layout/trainingsplan_item.xml=D\:\\Home\\Dokumente\\AndroidStudioProjects\\DieSnookerApp - Jules\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_trainingsplan_item.xml.flat
