package com.atom.diesnookerapp.data.firebase

import android.content.Context
import android.util.Log
import androidx.work.Constraints
import androidx.work.CoroutineWorker
import androidx.work.ExistingPeriodicWorkPolicy
import androidx.work.NetworkType
import androidx.work.PeriodicWorkRequestBuilder
import androidx.work.WorkManager
import androidx.work.WorkerParameters
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.util.concurrent.TimeUnit

/**
 * Manager for data synchronization
 */
class SyncManager(private val context: Context) {

    companion object {
        private const val TAG = "SyncManager"
        private const val SYNC_WORK_NAME = "data_sync_work"
    }

    private val trainingRepository = TrainingRecordRepository(context)
    private val questionRepository = QuestionRecordRepository(context)
    private val exerciseRepository = ExerciseRecordRepository(context)
    private val taskRepository = TaskHistoryRepository(context)
    private val trainingsplanRepository = TrainingsplanRepository(context)
    private val authManager = FirebaseAuthManager()

    /**
     * Schedule periodic sync
     */
    fun scheduleSyncWork() {
        val constraints = Constraints.Builder()
            .setRequiredNetworkType(NetworkType.CONNECTED)
            .build()

        val syncRequest = PeriodicWorkRequestBuilder<SyncWorker>(
            15, TimeUnit.MINUTES
        )
            .setConstraints(constraints)
            .build()

        WorkManager.getInstance(context).enqueueUniquePeriodicWork(
            SYNC_WORK_NAME,
            ExistingPeriodicWorkPolicy.KEEP,
            syncRequest
        )

        Log.d(TAG, "Scheduled periodic sync work")
    }

    /**
     * Cancel scheduled sync
     */
    fun cancelSyncWork() {
        WorkManager.getInstance(context).cancelUniqueWork(SYNC_WORK_NAME)
        Log.d(TAG, "Cancelled sync work")
    }

    /**
     * Clean up duplicate records in Firestore
     */
    suspend fun cleanupDuplicates(): Result<CleanupResult> = withContext(Dispatchers.IO) {
        if (!authManager.isLoggedIn()) {
            return@withContext Result.failure(Exception("User not logged in"))
        }

        try {
            Log.d(TAG, "Cleaning up duplicate records in Firestore...")

            // Initialize cleanup counts
            var trainingCleanupCount = 0
            var questionCleanupCount = 0
            var exerciseCleanupCount = 0
            var taskCleanupCount = 0
            var trainingsplanCleanupCount = 0

            // Clean up task history records (this is the one we're focusing on)
            try {
                val taskCleanupResult = taskRepository.cleanupDuplicates()
                taskCleanupCount = taskCleanupResult.getOrDefault(0)
                Log.d(TAG, "Cleaned up $taskCleanupCount duplicate task history records")
            } catch (e: Exception) {
                Log.e(TAG, "Error cleaning up task history records: ${e.message}")
            }

            // Return the total number of cleaned up records
            val result = CleanupResult(
                trainingRecordsCleaned = trainingCleanupCount,
                questionRecordsCleaned = questionCleanupCount,
                exerciseRecordsCleaned = exerciseCleanupCount,
                taskHistoryCleaned = taskCleanupCount,
                trainingsplanCleaned = trainingsplanCleanupCount
            )

            Log.d(TAG, "Cleanup completed: $result")
            Result.success(result)
        } catch (e: Exception) {
            Log.e(TAG, "Error during cleanup: ${e.message}")
            Result.failure(e)
        }
    }

    /**
     * Perform a full sync (both to and from Firebase)
     */
    suspend fun performFullSync(): Result<SyncResult> = withContext(Dispatchers.IO) {
        if (!authManager.isLoggedIn()) {
            return@withContext Result.failure(Exception("User not logged in"))
        }

        try {
            // First, clean up any duplicates in Firestore (only for task history)
            val cleanupResult = cleanupDuplicates()

            // Sync local data to Firebase
            val trainingToResult = trainingRepository.syncToFirebase()
            val questionToResult = questionRepository.syncToFirebase()
            val exerciseToResult = exerciseRepository.syncToFirebase()
            val taskToResult = taskRepository.syncToFirebase()
            val trainingsplanToResult = trainingsplanRepository.syncToFirebase()

            // Sync Firebase data to local storage
            val trainingFromResult = trainingRepository.syncFromFirebase()
            val questionFromResult = questionRepository.syncFromFirebase()
            val exerciseFromResult = exerciseRepository.syncFromFirebase()
            val taskFromResult = taskRepository.syncFromFirebase()
            val trainingsplanFromResult = trainingsplanRepository.syncFromFirebase()

            // Run cleanup again after syncing to ensure no new duplicates were created (only for task history)
            val finalCleanupResult = cleanupDuplicates()

            // Get cleanup counts
            val initialCleanupCount = cleanupResult.getOrNull()?.totalCleaned ?: 0
            val finalCleanupCount = finalCleanupResult.getOrNull()?.totalCleaned ?: 0

            // Log cleanup results
            if (initialCleanupCount > 0 || finalCleanupCount > 0) {
                Log.d(TAG, "Total cleanup: $initialCleanupCount before sync, $finalCleanupCount after sync")
            }

            // Collect results
            val result = SyncResult(
                trainingRecordsUploaded = trainingToResult.getOrDefault(0),
                questionRecordsUploaded = questionToResult.getOrDefault(0),
                exerciseRecordsUploaded = exerciseToResult.getOrDefault(0),
                taskHistoryUploaded = taskToResult.getOrDefault(0),
                trainingsplanUploaded = trainingsplanToResult.getOrDefault(0),

                trainingRecordsDownloaded = trainingFromResult.getOrDefault(0),
                questionRecordsDownloaded = questionFromResult.getOrDefault(0),
                exerciseRecordsDownloaded = exerciseFromResult.getOrDefault(0),
                taskHistoryDownloaded = taskFromResult.getOrDefault(0),
                trainingsplanDownloaded = trainingsplanFromResult.getOrDefault(0),

                initialCleanupCount = initialCleanupCount,
                finalCleanupCount = finalCleanupCount
            )

            Log.d(TAG, "Sync completed: $result")
            Result.success(result)
        } catch (e: Exception) {
            Log.e(TAG, "Error during sync: ${e.message}")
            Result.failure(e)
        }
    }

    /**
     * Worker class for background synchronization
     */
    class SyncWorker(
        context: Context,
        workerParams: WorkerParameters
    ) : CoroutineWorker(context, workerParams) {

        override suspend fun doWork(): Result {
            val syncManager = SyncManager(applicationContext)
            val authManager = FirebaseAuthManager()

            return if (authManager.isLoggedIn()) {
                try {
                    syncManager.performFullSync()
                    Log.d(TAG, "Background sync completed successfully")
                    Result.success()
                } catch (e: Exception) {
                    Log.e(TAG, "Background sync failed: ${e.message}")
                    Result.retry()
                }
            } else {
                Log.d(TAG, "User not logged in, skipping sync")
                Result.success()
            }
        }
    }

    /**
     * Data class to hold cleanup results
     */
    data class CleanupResult(
        val trainingRecordsCleaned: Int = 0,
        val questionRecordsCleaned: Int = 0,
        val exerciseRecordsCleaned: Int = 0,
        val taskHistoryCleaned: Int = 0,
        val trainingsplanCleaned: Int = 0
    ) {
        val totalCleaned: Int
            get() = trainingRecordsCleaned + questionRecordsCleaned + exerciseRecordsCleaned +
                    taskHistoryCleaned + trainingsplanCleaned

        override fun toString(): String {
            return "CleanupResult(trainingRecordsCleaned=$trainingRecordsCleaned, " +
                   "questionRecordsCleaned=$questionRecordsCleaned, " +
                   "exerciseRecordsCleaned=$exerciseRecordsCleaned, " +
                   "taskHistoryCleaned=$taskHistoryCleaned, " +
                   "trainingsplanCleaned=$trainingsplanCleaned, " +
                   "totalCleaned=$totalCleaned)"
        }
    }

    /**
     * Data class to hold sync results
     */
    data class SyncResult(
        val trainingRecordsUploaded: Int = 0,
        val questionRecordsUploaded: Int = 0,
        val exerciseRecordsUploaded: Int = 0,
        val taskHistoryUploaded: Int = 0,
        val trainingsplanUploaded: Int = 0,

        val trainingRecordsDownloaded: Int = 0,
        val questionRecordsDownloaded: Int = 0,
        val exerciseRecordsDownloaded: Int = 0,
        val taskHistoryDownloaded: Int = 0,
        val trainingsplanDownloaded: Int = 0,

        val initialCleanupCount: Int = 0,
        val finalCleanupCount: Int = 0
    ) {
        val totalUploaded: Int
            get() = trainingRecordsUploaded + questionRecordsUploaded +
                    exerciseRecordsUploaded + taskHistoryUploaded + trainingsplanUploaded

        val totalDownloaded: Int
            get() = trainingRecordsDownloaded + questionRecordsDownloaded +
                    exerciseRecordsDownloaded + taskHistoryDownloaded + trainingsplanDownloaded

        val totalCleanupCount: Int
            get() = initialCleanupCount + finalCleanupCount
    }
}

