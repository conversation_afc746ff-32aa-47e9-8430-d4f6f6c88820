<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="dialog_add_edit_exercise" modulePackage="com.atom.diesnookerapp" filePath="app\src\main\res\layout\dialog_add_edit_exercise.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/dialog_add_edit_exercise_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="110" endOffset="14"/></Target><Target id="@+id/exerciseNameInputLayout" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="8" startOffset="4" endLine="20" endOffset="59"/></Target><Target id="@+id/exerciseNameEditText" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="15" startOffset="8" endLine="19" endOffset="46"/></Target><Target id="@+id/exerciseCategoryInputLayout" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="22" startOffset="4" endLine="35" endOffset="59"/></Target><Target id="@+id/exerciseCategoryAutoCompleteTextView" view="com.google.android.material.textfield.MaterialAutoCompleteTextView"><Expressions/><location startLine="30" startOffset="8" endLine="34" endOffset="38"/></Target><Target id="@+id/exerciseTypeInputLayout" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="37" startOffset="4" endLine="50" endOffset="59"/></Target><Target id="@+id/exerciseTypeAutoCompleteTextView" view="com.google.android.material.textfield.MaterialAutoCompleteTextView"><Expressions/><location startLine="45" startOffset="8" endLine="49" endOffset="38"/></Target><Target id="@+id/newCategoryInputLayout" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="52" startOffset="4" endLine="66" endOffset="59"/></Target><Target id="@+id/newCategoryEditText" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="61" startOffset="8" endLine="65" endOffset="50"/></Target><Target id="@+id/exerciseDescriptionInputLayout" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="68" startOffset="4" endLine="84" endOffset="59"/></Target><Target id="@+id/exerciseDescriptionEditText" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="76" startOffset="8" endLine="83" endOffset="34"/></Target><Target id="@+id/cancelButton" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="92" startOffset="8" endLine="99" endOffset="43"/></Target><Target id="@+id/saveButton" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="101" startOffset="8" endLine="107" endOffset="45"/></Target></Targets></Layout>