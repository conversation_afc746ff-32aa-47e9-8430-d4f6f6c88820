// Update imports at the top of the file
package com.atom.diesnookerapp.ui.aufgaben

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.atom.diesnookerapp.R
import org.threeten.bp.LocalDate  // Use ThreeTenABP instead of java.time

class KonzentrationFragment : Fragment() {
    private lateinit var recyclerView: RecyclerView
    private lateinit var pointsTextView: TextView
    private lateinit var taskAdapter: TaskAdapter
    private lateinit var taskManager: TaskManager
    
    private val konzentrationTasks = listOf(
        Task(
            id = "focus_on_shot",
            title = "Fokus auf Abstoß",
            description = "20 mal blau lang, nur auf Abstoß fokussieren",
            frequency = TaskFrequency.DAILY,
            points = 1,
            category = "concentration"
        ),
        Task(
            id = "all_balls_equal",
            title = "Alle Bälle sind gleich",
            description = "50 Schwarze vom Spot",
            frequency = TaskFrequency.WEEKLY,
            points = 2,
            category = "concentration"
        ),
        Task(
            id = "visualize_cue_ball",
            title = "Ablage Weiß vorvisualisieren",
            description = "10min pro Tag Snooker im Kopf spielen",
            frequency = TaskFrequency.DAILY,
            points = 1,
            category = "concentration"
        ),
        Task(
            id = "focus_on_table",
            title = "Alles um den Tisch herum ausblenden",
            description = "5min auf einen Gegenstand voll fokussieren",
            frequency = TaskFrequency.DAILY,
            points = 1,
            category = "concentration"
        ),
        Task(
            id = "balance_unterberger",
            title = "Gleichgewicht",
            description = "Unterberger",
            frequency = TaskFrequency.WEEKLY,
            points = 2,
            category = "concentration"
        ),
        Task(
            id = "balance",
            title = "Gleichgewicht",
            description = "10min Gleichgewicht trainieren",
            frequency = TaskFrequency.DAILY,
            points = 1,
            category = "concentration"
        ),
        Task(
            id = "general",
            title = "Allgemein",
            description = "15min pro Tag Übungen wie Fingerjogging etc.",
            frequency = TaskFrequency.DAILY,
            points = 1,
            category = "concentration"
        )
    )
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_task_list, container, false)
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        // Initialize TaskManager
        taskManager = TaskManager(requireContext())
        
        // Check if we need to reset tasks for a new week
        taskManager.resetTasksForNewWeek()
        
        // Initialize views
        recyclerView = view.findViewById(R.id.tasksRecyclerView)
        pointsTextView = view.findViewById(R.id.pointsTextView)
        
        // Set up RecyclerView
        recyclerView.layoutManager = LinearLayoutManager(requireContext())
        
        // Initialize tasks if needed
        taskManager.initializeTasksIfNeeded(konzentrationTasks)
        
        // Get tasks from storage
        val tasks = taskManager.getTasksByCategory("concentration")
        
        // Set up adapter
        taskAdapter = TaskAdapter(
            tasks,
            onTaskCompleted = { task ->
                taskManager.markTaskCompleted(task.id, LocalDate.now())
                updatePointsDisplay()
                // No need to call notifyDataSetChanged as we're now handling updates in the adapter
            },
            onTaskUncompleted = { task ->
                taskManager.markTaskUncompleted(task.id, LocalDate.now())
                updatePointsDisplay()
                // No need to call notifyDataSetChanged as we're now handling updates in the adapter
            },
            getFrequencyText = { task ->
                when (task.frequency) {
                    TaskFrequency.DAILY -> "täglich"
                    TaskFrequency.WEEKLY -> when (task.id) {
                        "all_balls_equal" -> "2x pro Woche"
                        "balance_unterberger" -> "3x pro Woche"
                        else -> "wöchentlich"
                    }
                    TaskFrequency.MONTHLY -> "monatlich"
                }
            },
            taskManager = taskManager  // Pass the taskManager instance
        )
        
        recyclerView.adapter = taskAdapter
        
        // Set title
        view.findViewById<TextView>(R.id.categoryTitleTextView).text = "Konzentration"
        
        // Update points display
        updatePointsDisplay()
    }
    
    private fun updatePointsDisplay() {
        val totalPoints = taskManager.getTotalPoints()
        pointsTextView.text = "Gesammelte Punkte: $totalPoints"
    }
}