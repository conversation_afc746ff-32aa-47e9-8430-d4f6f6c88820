package com.atom.diesnookerapp.ui.aufgaben

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.atom.diesnookerapp.R
import org.threeten.bp.LocalDate

class BreakbuildingFragment : Fragment() {
    private lateinit var recyclerView: RecyclerView
    private lateinit var pointsTextView: TextView
    private lateinit var titleTextView: TextView
    private lateinit var taskAdapter: TaskAdapter
    private lateinit var taskManager: TaskManager

    private val breakbuildingTasks = listOf(
        Task(
            id = "zhao_x",
            title = "30min Zhao X.",
            description = "2x die Woche",
            frequency = TaskFrequency.WEEKLY,
            points = 1,
            category = "breakbuilding"
        )
    )

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_task_list, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        // Initialize TaskManager
        taskManager = TaskManager(requireContext())

        // Initialize views
        recyclerView = view.findViewById(R.id.tasksRecyclerView)
        pointsTextView = view.findViewById(R.id.pointsTextView)
        titleTextView = view.findViewById(R.id.categoryTitleTextView)

        // Set up RecyclerView
        recyclerView.layoutManager = LinearLayoutManager(requireContext())

        // Initialize tasks if needed
        taskManager.initializeTasksIfNeeded(breakbuildingTasks)

        // Get tasks from storage
        val tasks = taskManager.getTasksByCategory("breakbuilding")

        // Set up adapter
        taskAdapter = TaskAdapter(
            tasks,
            onTaskCompleted = { task ->
                taskManager.markTaskCompleted(task.id, LocalDate.now())
                updatePointsDisplay()
            },
            onTaskUncompleted = { task ->
                taskManager.markTaskUncompleted(task.id, LocalDate.now())
                updatePointsDisplay()
            },
            getFrequencyText = { task ->
                when (task.frequency) {
                    TaskFrequency.DAILY -> "1x täglich"
                    TaskFrequency.WEEKLY -> when (task.id) {
                        "zhao_x" -> "2x pro Woche"
                        else -> "wöchentlich"
                    }
                    TaskFrequency.MONTHLY -> "monatlich"
                }
            },
            taskManager = taskManager  // Add this parameter
        )

        recyclerView.adapter = taskAdapter

        // Set title
        titleTextView.text = "Breakbuilding"

        // Update points display
        updatePointsDisplay()
    }

    private fun updatePointsDisplay() {
        val totalPoints = taskManager.getTotalPoints()
        pointsTextView.text = "Gesammelte Punkte: $totalPoints"
    }
}