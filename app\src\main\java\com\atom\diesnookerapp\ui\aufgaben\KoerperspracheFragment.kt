package com.atom.diesnookerapp.ui.aufgaben

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.atom.diesnookerapp.R
import org.threeten.bp.LocalDate

class KoerperspracheFragment : Fragment() {
    private lateinit var recyclerView: RecyclerView
    private lateinit var pointsTextView: TextView
    private lateinit var titleTextView: TextView
    private lateinit var taskAdapter: TaskAdapter
    private lateinit var taskManager: TaskManager

    private val koerperspracheTask = listOf(
        Task(
            id = "aeusserliche_ruhe",
            title = "Äußerliche Ruhe",
            description = "Nach einfachen Fehlern im Training für 30-60sec auf Stuhl setzen und Lächeln/positiv denken",
            frequency = TaskFrequency.WEEKLY,
            points = 2,
            category = "mental"
        ),
        Task(
            id = "aufrechte_koerperhaltung",
            title = "Aufrechte Körperhaltung",
            description = "Selbstbewusst um den Tisch laufen",
            frequency = TaskFrequency.DAILY,
            points = 1,
            category = "koerpersprache"
        ),
        Task(
            id = "aergern_nach_wettkampf",
            title = "Ärgern nach Wettkampf",
            description = "Siehe positives Denken",
            frequency = TaskFrequency.WEEKLY,
            points = 1,
            category = "koerpersprache"
        ),
        Task(
            id = "laecheln",
            title = "Lächeln",
            description = "Bei Videocheck auf Mimik achten, 1-5 Punkte",
            frequency = TaskFrequency.WEEKLY,
            points = 1,
            category = "koerpersprache"
        )
    )

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_task_list, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        // Initialize TaskManager
        taskManager = TaskManager(requireContext())

        // Initialize views
        recyclerView = view.findViewById(R.id.tasksRecyclerView)
        pointsTextView = view.findViewById(R.id.pointsTextView)
        titleTextView = view.findViewById(R.id.categoryTitleTextView)

        // Set up RecyclerView
        recyclerView.layoutManager = LinearLayoutManager(requireContext())

        // Initialize tasks if needed
        taskManager.initializeTasksIfNeeded(koerperspracheTask)

        // Get tasks from storage
        val tasks = taskManager.getTasksByCategory("koerpersprache")

        // Set up adapter
        taskAdapter = TaskAdapter(
            tasks,
            onTaskCompleted = { task ->
                taskManager.markTaskCompleted(task.id, LocalDate.now())
                updatePointsDisplay()
            },
            onTaskUncompleted = { task ->
                taskManager.markTaskUncompleted(task.id, LocalDate.now())
                updatePointsDisplay()
            },
            getFrequencyText = { task ->
                when (task.frequency) {
                    TaskFrequency.DAILY -> when (task.id) {
                        "laecheln" -> "1x pro Woche" // Changed from "unbegrenzt oft"
                        else -> "1x täglich"
                    }
                    TaskFrequency.WEEKLY -> "wöchentlich"
                    TaskFrequency.MONTHLY -> "monatlich"
                }
            },
            taskManager = taskManager  // Add this parameter
        )

        // Set up custom points callback
        taskAdapter.setOnTaskCompletedWithCustomPoints { task, points ->
            taskManager.markTaskCompletedWithCustomPoints(task.id, LocalDate.now(), points)
            updatePointsDisplay()
        }

        recyclerView.adapter = taskAdapter

        // Set title
        titleTextView.text = "Körpersprache"

        // Update points display
        updatePointsDisplay()
    }

    private fun updatePointsDisplay() {
        val totalPoints = taskManager.getTotalPoints()
        pointsTextView.text = "Gesammelte Punkte: $totalPoints"
    }
}