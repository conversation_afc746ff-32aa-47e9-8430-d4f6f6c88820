package com.atom.diesnookerapp.ui.aufgaben

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.atom.diesnookerapp.R
import com.google.android.material.card.MaterialCardView

class TaskCategoryAdapter(
    private val categories: List<TaskCategory>,
    private val onCategoryClick: (TaskCategory) -> Unit
) : RecyclerView.Adapter<TaskCategoryAdapter.ViewHolder>() {

    class ViewHolder(view: View) : RecyclerView.ViewHolder(view) {
        val categoryCard: MaterialCardView = view.findViewById(R.id.categoryCard)
        val categoryName: TextView = view.findViewById(R.id.categoryName)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_task_category, parent, false)
        return ViewHolder(view)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val category = categories[position]
        holder.categoryName.text = category.name
        
        holder.categoryCard.setOnClickListener {
            onCategoryClick(category)
        }
    }

    override fun getItemCount() = categories.size
}