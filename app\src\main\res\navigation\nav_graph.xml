<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/nav_graph"
    app:startDestination="@id/navigation_selbsteinschaetzung">

    <fragment
        android:id="@+id/navigation_selbsteinschaetzung"
        android:name="com.atom.diesnookerapp.ui.selbsteinschaetzung.SelbsteinschaetzungFragment"
        android:label="Selbsteinschätzung">

        <action
            android:id="@+id/actionToBeforeTraining"
            app:destination="@id/beforeTrainingFragment" />
        <action
            android:id="@+id/actionToAfterTraining"
            app:destination="@id/afterTrainingFragment" />
        <action
            android:id="@+id/actionToBeforeTournament"
            app:destination="@id/beforeTournamentFragment" />
        <action
            android:id="@+id/actionToAfterTournament"
            app:destination="@id/afterTournamentFragment" />
        <action
            android:id="@+id/actionToQuestions"
            app:destination="@id/questionsFragment" />
        <action
            android:id="@+id/action_selbsteinschaetzungFragment_to_historyFragment"
            app:destination="@id/historyFragment" />
    </fragment>

    <fragment
        android:id="@+id/navigation_ergebniserfassung"
        android:name="com.atom.diesnookerapp.ui.ergebniserfassung.ErgebniserfassungFragment"
        android:label="Ergebniserfassung">

        <action
            android:id="@+id/action_navigation_ergebniserfassung_to_breakbuildingFragment"
            app:destination="@id/breakbuildingFragment" />
        <action
            android:id="@+id/action_navigation_ergebniserfassung_to_pottingFragment"
            app:destination="@id/pottingFragment" />
        <action
            android:id="@+id/action_navigation_ergebniserfassung_to_safetiesFragment"
            app:destination="@id/safetiesFragment" />
        <action
            android:id="@+id/action_navigation_ergebniserfassung_to_splitsFragment"
            app:destination="@id/splitsFragment" />
        <action
            android:id="@+id/action_navigation_ergebniserfassung_to_technikFragment"
            app:destination="@id/technikFragment" />
        <action
            android:id="@+id/action_navigation_ergebniserfassung_to_stellungsspielFragment"
            app:destination="@id/stellungsspielFragment" />
        <action
            android:id="@+id/action_navigation_ergebniserfassung_to_exercise_history"
            app:destination="@id/navigation_exercise_history" />
        <action
            android:id="@+id/action_ergebniserfassung_to_generic_exercise_list"
            app:destination="@id/navigation_generic_exercise_list" />
    </fragment>

    <fragment
        android:id="@+id/navigation_generic_exercise_list"
        android:name="com.atom.diesnookerapp.ui.ergebniserfassung.GenericExerciseListFragment"
        android:label="@string/title_category_exercises_generic"
        tools:layout="@layout/fragment_generic_exercise_list">
        <argument
            android:name="categoryName"
            app:argType="string"
            app:nullable="true" />
        <action
            android:id="@+id/action_genericExerciseListFragment_to_exerciseDetailFragment"
            app:destination="@id/exerciseDetailFragment" />
            <!-- Arguments for exerciseId and exerciseTitle are defined in the destination exerciseDetailFragment -->
    </fragment>

    <fragment
        android:id="@+id/navigation_trainingsplan"
        android:name="com.atom.diesnookerapp.ui.trainingsplan.TrainingsplanFragment"
        android:label="Trainingsplan"
        tools:layout="@layout/fragment_trainingsplan">
        <action
            android:id="@+id/action_navigation_trainingsplan_to_trainingsplanHistoryFragment"
            app:destination="@id/trainingsplanHistoryFragment" />
        <action
            android:id="@+id/action_navigation_trainingsplan_to_exerciseSelectionFragment"
            app:destination="@id/exerciseSelectionFragment" />
        <action
            android:id="@+id/action_navigation_trainingsplan_to_manage_exercises"
            app:destination="@id/navigation_manage_exercises" />
    </fragment>

    <fragment
        android:id="@+id/navigation_manage_exercises"
        android:name="com.atom.diesnookerapp.ui.manageexercises.ManageExercisesFragment"
        android:label="Manage Exercises"
        tools:layout="@layout/fragment_manage_exercises" />

    <fragment
        android:id="@+id/exerciseSelectionFragment"
        android:name="com.atom.diesnookerapp.ui.trainingsplan.ExerciseSelectionFragment"
        android:label="Übung hinzufügen">
        <argument
            android:name="category"
            app:argType="string"
            app:nullable="true"/>
    </fragment>

    <fragment
        android:id="@+id/navigation_aufgaben"
        android:name="com.atom.diesnookerapp.ui.aufgaben.AufgabenFragment"
        android:label="Aufgaben" />

    <fragment
        android:id="@+id/beforeTrainingFragment"
        android:name="com.atom.diesnookerapp.ui.selbsteinschaetzung.BeforeTrainingFragment"
        android:label="Vor Training" />

    <fragment
        android:id="@+id/afterTrainingFragment"
        android:name="com.atom.diesnookerapp.ui.selbsteinschaetzung.AfterTrainingFragment"
        android:label="Nach Training" />

    <fragment
        android:id="@+id/beforeTournamentFragment"
        android:name="com.atom.diesnookerapp.ui.selbsteinschaetzung.BeforeTournamentFragment"
        android:label="Vor Turnier" />

    <fragment
        android:id="@+id/afterTournamentFragment"
        android:name="com.atom.diesnookerapp.ui.selbsteinschaetzung.AfterTournamentFragment"
        android:label="Nach Turnier" />

    <fragment
        android:id="@+id/questionsFragment"
        android:name="com.atom.diesnookerapp.ui.selbsteinschaetzung.QuestionsFragment"
        android:label="Fragen" />

    <fragment
        android:id="@+id/questionsBeforeTrainingFragment"
        android:name="com.atom.diesnookerapp.ui.selbsteinschaetzung.QuestionsBeforeTrainingFragment"
        android:label="Fragen - Vor Training" />

    <fragment
        android:id="@+id/questionsAfterTrainingFragment"
        android:name="com.atom.diesnookerapp.ui.selbsteinschaetzung.QuestionsAfterTrainingFragment"
        android:label="Fragen - Nach Training" />

    <fragment
        android:id="@+id/questionsBeforeTournamentFragment"
        android:name="com.atom.diesnookerapp.ui.selbsteinschaetzung.QuestionsBeforeTournamentFragment"
        android:label="Fragen - Vor Turnier" />

    <fragment
        android:id="@+id/questionsAfterTournamentFragment"
        android:name="com.atom.diesnookerapp.ui.selbsteinschaetzung.QuestionsAfterTournamentFragment"
        android:label="Fragen - Nach Turnier" />

    <fragment
        android:id="@+id/historyFragment"
        android:name="com.atom.diesnookerapp.ui.selbsteinschaetzung.HistoryFragment"
        android:label="Verlauf"
        tools:layout="@layout/fragment_history">

        <action
            android:id="@+id/action_historyFragment_to_historyDetailFragment"
            app:destination="@id/historyDetailFragment" />
        <action
            android:id="@+id/action_historyFragment_to_graphFragment"
            app:destination="@id/graphFragment" />
    </fragment>

    <fragment
        android:id="@+id/historyDetailFragment"
        android:name="com.atom.diesnookerapp.ui.selbsteinschaetzung.HistoryDetailFragment"
        android:label="Details">
        <argument
            android:name="date"
            app:argType="string" />
    </fragment>

    <fragment
        android:id="@+id/graphFragment"
        android:name="com.atom.diesnookerapp.ui.selbsteinschaetzung.GraphFragment"
        android:label="GraphFragment" />

    <fragment
        android:id="@+id/graphDetailFragment"
        android:name="com.atom.diesnookerapp.ui.selbsteinschaetzung.GraphDetailFragment"
        android:label="GraphDetailFragment" />

    <action
        android:id="@+id/action_graphFragment_to_graphDetailFragment"
        app:destination="@id/graphDetailFragment" />

    <fragment
        android:id="@+id/breakbuildingFragment"
        android:name="com.atom.diesnookerapp.ui.ergebniserfassung.BreakbuildingFragment"
        android:label="Breakbuilding">
        <action
            android:id="@+id/action_breakbuildingFragment_to_exerciseDetailFragment"
            app:destination="@id/exerciseDetailFragment" />
    </fragment>

    <fragment
        android:id="@+id/pottingFragment"
        android:name="com.atom.diesnookerapp.ui.ergebniserfassung.PottingFragment"
        android:label="Potting">
        <action
            android:id="@+id/action_pottingFragment_to_exerciseDetailFragment"
            app:destination="@id/exerciseDetailFragment" />
    </fragment>

    <fragment
        android:id="@+id/safetiesFragment"
        android:name="com.atom.diesnookerapp.ui.ergebniserfassung.SafetiesFragment"
        android:label="Safeties">
        <action
            android:id="@+id/action_safetiesFragment_to_exerciseDetailFragment"
            app:destination="@id/exerciseDetailFragment" />
    </fragment>

    <fragment
        android:id="@+id/splitsFragment"
        android:name="com.atom.diesnookerapp.ui.ergebniserfassung.SplitsFragment"
        android:label="Splits">
        <action
            android:id="@+id/action_splitsFragment_to_exerciseDetailFragment"
            app:destination="@id/exerciseDetailFragment" />
    </fragment>

    <fragment
        android:id="@+id/technikFragment"
        android:name="com.atom.diesnookerapp.ui.ergebniserfassung.TechnikFragment"
        android:label="Technik">
        <action
            android:id="@+id/action_technikFragment_to_exerciseDetailFragment"
            app:destination="@id/exerciseDetailFragment" />
    </fragment>

    <fragment
        android:id="@+id/stellungsspielFragment"
        android:name="com.atom.diesnookerapp.ui.ergebniserfassung.StellungsspielFragment"
        android:label="Stellungsspiel">
        <action
            android:id="@+id/action_stellungsspielFragment_to_exerciseDetailFragment"
            app:destination="@id/exerciseDetailFragment" />
    </fragment>

    <fragment
        android:id="@+id/exerciseDetailFragment"
        android:name="com.atom.diesnookerapp.ui.ergebniserfassung.ExerciseDetailFragment"
        android:label="Exercise Detail">
        <argument
            android:name="exerciseId"
            app:argType="string" />
        <argument
            android:name="exerciseTitle"
            app:argType="string" />
        <action
            android:id="@+id/action_exerciseDetailFragment_to_exerciseGraphFragment"
            app:destination="@id/exerciseGraphFragment" />
    </fragment>

    <fragment
        android:id="@+id/navigation_exercise_history"
        android:name="com.atom.diesnookerapp.ui.ergebniserfassung.ExerciseHistoryFragment"
        android:label="Übungshistorie"
        tools:layout="@layout/fragment_exercise_history">
        <action
            android:id="@+id/action_navigation_exercise_history_to_exercise_history_detail"
            app:destination="@id/exerciseHistoryDetailFragment" />
        <action
            android:id="@+id/action_exerciseHistory_to_exerciseGraph"
            app:destination="@id/exerciseGraphFragment" />
    </fragment>

    <fragment
        android:id="@+id/exerciseGraphFragment"
        android:name="com.atom.diesnookerapp.ui.ergebniserfassung.ExerciseGraphFragment"
        android:label="Übungsverlauf">
        <argument
            android:name="category"
            app:argType="string"
            app:nullable="true"/>
        <argument
            android:name="exerciseId"
            app:argType="string"
            app:nullable="true"/>
    </fragment>

    <fragment
        android:id="@+id/exerciseHistoryDetailFragment"
        android:name="com.atom.diesnookerapp.ui.ergebniserfassung.ExerciseHistoryDetailFragment"
        android:label="ExerciseHistoryDetailFragment">
        <argument
            android:name="date"
            app:argType="string" />
    </fragment>
    <!-- Add this fragment to your navigation graph -->
    <fragment
        android:id="@+id/taskHistoryFragment"
        android:name="com.atom.diesnookerapp.ui.aufgaben.TaskHistoryFragment"
        android:label="Aufgaben-Verlauf"
        tools:layout="@layout/fragment_task_history" />

    <!-- Add an action from AufgabenFragment to TaskHistoryFragment -->
    <action
        android:id="@+id/action_navigation_aufgaben_to_taskHistoryFragment"
        app:destination="@id/taskHistoryFragment" />

    <fragment
        android:id="@+id/trainingsplanHistoryFragment"
        android:name="com.atom.diesnookerapp.ui.trainingsplan.TrainingsplanHistoryFragment"
        android:label="Trainingsplan History"
        tools:layout="@layout/fragment_trainingsplan_history" />

    <!-- Add this near the KonzentrationFragment definition -->
    <fragment
        android:id="@+id/aufgabentechnikFragment"
        android:name="com.atom.diesnookerapp.ui.aufgaben.TechnikFragment"
        android:label="Technik"
        tools:layout="@layout/fragment_task_list" />

    <!-- Update the navigation_aufgaben fragment to include the action to TechnikFragment -->
    <fragment
        android:id="@+id/navigation_aufgaben"
        android:name="com.atom.diesnookerapp.ui.aufgaben.AufgabenFragment"
        android:label="Aufgaben"
        tools:layout="@layout/fragment_aufgaben">
        <action
            android:id="@+id/action_navigation_aufgaben_to_konzentrationFragment"
            app:destination="@id/konzentrationFragment" />
        <action
            android:id="@+id/action_navigation_aufgaben_to_technikFragment"
            app:destination="@id/aufgabentechnikFragment" />
        <action
            android:id="@+id/action_navigation_aufgaben_to_matchplayFragment"
            app:destination="@id/matchplayFragment" />
        <action
            android:id="@+id/action_navigation_aufgaben_to_wichtigeBaelleFragment"
            app:destination="@id/wichtigeBaelleFragment" />
        <action
            android:id="@+id/action_navigation_aufgaben_to_safetiesFragment"
            app:destination="@id/aufgabensafetiesFragment" />
        <action
            android:id="@+id/action_navigation_aufgaben_to_mentalFragment"
            app:destination="@id/mentalFragment" />
        <action
            android:id="@+id/action_navigation_aufgaben_to_koerperspracheFragment"
            app:destination="@id/koerperspracheFragment" />
        <action
            android:id="@+id/action_navigation_aufgaben_to_breakbuildingFragment"
            app:destination="@id/aufgabenbreakbuildingFragment" />
        <action
            android:id="@+id/action_navigation_aufgaben_to_fitnessErnaehrungFragment"
            app:destination="@id/fitnessErnaehrungFragment" />
    </fragment>

    <fragment
        android:id="@+id/konzentrationFragment"
        android:name="com.atom.diesnookerapp.ui.aufgaben.KonzentrationFragment"
        android:label="Konzentration"
        tools:layout="@layout/fragment_task_list" />

    <fragment
        android:id="@+id/aufgabentechnikFragment"
        android:name="com.atom.diesnookerapp.ui.aufgaben.TechnikFragment"
        android:label="Technik"
        tools:layout="@layout/fragment_task_list" />

    <fragment
        android:id="@+id/matchplayFragment"
        android:name="com.atom.diesnookerapp.ui.aufgaben.MatchplayFragment"
        android:label="Matchplay"
        tools:layout="@layout/fragment_task_list" />

    <fragment
        android:id="@+id/wichtigeBaelleFragment"
        android:name="com.atom.diesnookerapp.ui.aufgaben.WichtigeBaelleFragment"
        android:label="Wichtige Bälle"
        tools:layout="@layout/fragment_task_list" />

    <fragment
        android:id="@+id/aufgabensafetiesFragment"
        android:name="com.atom.diesnookerapp.ui.aufgaben.SafetiesFragment"
        android:label="Safeties"
        tools:layout="@layout/fragment_task_list" />

    <fragment
        android:id="@+id/mentalFragment"
        android:name="com.atom.diesnookerapp.ui.aufgaben.MentalFragment"
        android:label="Mental"
        tools:layout="@layout/fragment_task_list" />

    <fragment
        android:id="@+id/koerperspracheFragment"
        android:name="com.atom.diesnookerapp.ui.aufgaben.KoerperspracheFragment"
        android:label="Körpersprache"
        tools:layout="@layout/fragment_task_list" />

    <fragment
        android:id="@+id/aufgabenbreakbuildingFragment"
        android:name="com.atom.diesnookerapp.ui.aufgaben.BreakbuildingFragment"
        android:label="Breakbuilding"
        tools:layout="@layout/fragment_task_list" />

    <fragment
        android:id="@+id/aufgabensafetiesFragment"
        android:name="com.atom.diesnookerapp.ui.aufgaben.SafetiesFragment"
        android:label="Safeties"
        tools:layout="@layout/fragment_task_list" />

    <fragment
        android:id="@+id/aufgabentechnikFragment"
        android:name="com.atom.diesnookerapp.ui.aufgaben.TechnikFragment"
        android:label="Technik"
        tools:layout="@layout/fragment_task_list" />

    <fragment
        android:id="@+id/matchplayFragment"
        android:name="com.atom.diesnookerapp.ui.aufgaben.MatchplayFragment"
        android:label="Matchplay"
        tools:layout="@layout/fragment_task_list" />

    <fragment
        android:id="@+id/wichtigeBaelleFragment"
        android:name="com.atom.diesnookerapp.ui.aufgaben.WichtigeBaelleFragment"
        android:label="Wichtige Bälle"
        tools:layout="@layout/fragment_task_list" />

    <fragment
        android:id="@+id/mentalFragment"
        android:name="com.atom.diesnookerapp.ui.aufgaben.MentalFragment"
        android:label="Mental"
        tools:layout="@layout/fragment_task_list" />

    <fragment
        android:id="@+id/koerperspracheFragment"
        android:name="com.atom.diesnookerapp.ui.aufgaben.KoerperspracheFragment"
        android:label="Körpersprache"
        tools:layout="@layout/fragment_task_list" />

    <fragment
        android:id="@+id/fitnessErnaehrungFragment"
        android:name="com.atom.diesnookerapp.ui.aufgaben.FitnessErnaehrungFragment"
        android:label="Fitness und Ernährung"
        tools:layout="@layout/fragment_task_list" />

    <fragment
        android:id="@+id/navigation_settings"
        android:name="com.atom.diesnookerapp.ui.settings.SettingsFragment"
        android:label="Einstellungen"
        tools:layout="@layout/fragment_settings">
        <action
            android:id="@+id/action_settings_to_login"
            app:destination="@id/loginFragment" />
        <action
            android:id="@+id/action_settings_to_userManagement"
            app:destination="@id/userManagementFragment" />
    </fragment>

    <fragment
        android:id="@+id/userManagementFragment"
        android:name="com.atom.diesnookerapp.ui.auth.UserManagementFragment"
        android:label="Trainer-Verbindungen"
        tools:layout="@layout/fragment_user_management" />

    <fragment
        android:id="@+id/loginFragment"
        android:name="com.atom.diesnookerapp.ui.auth.LoginFragment"
        android:label="Login"
        tools:layout="@layout/fragment_login">
        <action
            android:id="@+id/action_loginFragment_to_registerFragment"
            app:destination="@id/registerFragment" />
        <action
            android:id="@+id/action_loginFragment_to_homeFragment"
            app:destination="@id/navigation_selbsteinschaetzung" />
    </fragment>

    <fragment
        android:id="@+id/registerFragment"
        android:name="com.atom.diesnookerapp.ui.auth.RegisterFragment"
        android:label="Register"
        tools:layout="@layout/fragment_register">
        <action
            android:id="@+id/action_registerFragment_to_homeFragment"
            app:destination="@id/navigation_selbsteinschaetzung" />
    </fragment>

</navigation>
