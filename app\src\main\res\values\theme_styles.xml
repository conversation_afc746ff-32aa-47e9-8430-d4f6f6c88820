<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Ocean Theme Styles -->
    <style name="DropdownDialog.Ocean">
        <item name="android:windowBackground">@drawable/dropdown_background_ocean</item>
        <item name="android:textColor">@color/black</item>
        <item name="colorAccent">@color/ocean_medium</item>
        <item name="buttonBarPositiveButtonStyle">@style/OceanPositiveButtonStyle</item>
        <item name="buttonBarNegativeButtonStyle">@style/OceanNegativeButtonStyle</item>
    </style>

    <style name="DefaultAnimationDialog.Ocean">
        <item name="android:windowBackground">@drawable/dropdown_background_ocean</item>
        <item name="android:textColor">@color/black</item>
        <item name="colorAccent">@color/ocean_medium</item>
        <item name="buttonBarPositiveButtonStyle">@style/OceanPositiveButtonStyle</item>
        <item name="buttonBarNegativeButtonStyle">@style/OceanNegativeButtonStyle</item>
    </style>

    <style name="OceanPositiveButtonStyle" parent="Widget.MaterialComponents.Button.TextButton.Dialog">
        <item name="android:textColor">@color/ocean_medium</item>
    </style>

    <style name="OceanNegativeButtonStyle" parent="Widget.MaterialComponents.Button.TextButton.Dialog">
        <item name="android:textColor">@color/ocean_lightest</item>
    </style>

    <!-- PopupMenuStyle_Ocean is defined in popup_styles.xml -->

    <style name="CustomTextInputLayout.Ocean">
        <item name="boxStrokeColor">@color/ocean_medium</item>
        <item name="hintTextColor">@color/ocean_medium</item>
    </style>

    <style name="CustomDropdownTextInputLayout.Ocean">
        <item name="boxStrokeColor">@color/ocean_medium</item>
        <item name="hintTextColor">@color/ocean_medium</item>
        <item name="endIconTint">@color/ocean_medium</item>
    </style>

    <style name="CustomDropdownStyle.Ocean">
        <item name="android:background">@drawable/dropdown_background_solid_ocean</item>
        <item name="android:textColor">@color/black</item>
    </style>

    <!-- Crimson Theme Styles -->
    <style name="DropdownDialog.Crimson">
        <item name="android:windowBackground">@drawable/dropdown_background_crimson</item>
        <item name="android:textColor">@color/black</item>
        <item name="colorAccent">@color/crimson_medium</item>
        <item name="buttonBarPositiveButtonStyle">@style/CrimsonPositiveButtonStyle</item>
        <item name="buttonBarNegativeButtonStyle">@style/CrimsonNegativeButtonStyle</item>
    </style>

    <style name="DefaultAnimationDialog.Crimson">
        <item name="android:windowBackground">@drawable/dropdown_background_crimson</item>
        <item name="android:textColor">@color/black</item>
        <item name="colorAccent">@color/crimson_medium</item>
        <item name="buttonBarPositiveButtonStyle">@style/CrimsonPositiveButtonStyle</item>
        <item name="buttonBarNegativeButtonStyle">@style/CrimsonNegativeButtonStyle</item>
    </style>

    <style name="CrimsonPositiveButtonStyle" parent="Widget.MaterialComponents.Button.TextButton.Dialog">
        <item name="android:textColor">@color/crimson_medium</item>
    </style>

    <style name="CrimsonNegativeButtonStyle" parent="Widget.MaterialComponents.Button.TextButton.Dialog">
        <item name="android:textColor">@color/crimson_lightest</item>
    </style>

    <!-- PopupMenuStyle_Crimson is defined in popup_styles.xml -->

    <style name="CustomTextInputLayout.Crimson">
        <item name="boxStrokeColor">@color/crimson_medium</item>
        <item name="hintTextColor">@color/crimson_medium</item>
    </style>

    <style name="CustomDropdownTextInputLayout.Crimson">
        <item name="boxStrokeColor">@color/crimson_medium</item>
        <item name="hintTextColor">@color/crimson_medium</item>
        <item name="endIconTint">@color/crimson_medium</item>
    </style>

    <style name="CustomDropdownStyle.Crimson">
        <item name="android:background">@drawable/dropdown_background_solid_crimson</item>
        <item name="android:textColor">@color/black</item>
    </style>

    <!-- Neon Theme Styles -->
    <style name="DropdownDialog.Neon">
        <item name="android:windowBackground">@drawable/dropdown_background_neon</item>
        <item name="android:textColor">@color/black</item>
        <item name="colorAccent">@color/neon_purple_3</item>
        <item name="buttonBarPositiveButtonStyle">@style/NeonPositiveButtonStyle</item>
        <item name="buttonBarNegativeButtonStyle">@style/NeonNegativeButtonStyle</item>
    </style>

    <style name="DefaultAnimationDialog.Neon">
        <item name="android:windowBackground">@drawable/dropdown_background_neon</item>
        <item name="android:textColor">@color/black</item>
        <item name="colorAccent">@color/neon_purple_3</item>
        <item name="buttonBarPositiveButtonStyle">@style/NeonPositiveButtonStyle</item>
        <item name="buttonBarNegativeButtonStyle">@style/NeonNegativeButtonStyle</item>
    </style>

    <style name="NeonPositiveButtonStyle" parent="Widget.MaterialComponents.Button.TextButton.Dialog">
        <item name="android:textColor">@color/neon_purple_3</item>
    </style>

    <style name="NeonNegativeButtonStyle" parent="Widget.MaterialComponents.Button.TextButton.Dialog">
        <item name="android:textColor">@color/neon_pink_2</item>
    </style>

    <!-- PopupMenuStyle_Neon is defined in popup_styles.xml -->

    <style name="CustomTextInputLayout.Neon">
        <item name="boxStrokeColor">@color/neon_purple_3</item>
        <item name="hintTextColor">@color/neon_purple_3</item>
    </style>

    <style name="CustomDropdownTextInputLayout.Neon">
        <item name="boxStrokeColor">@color/neon_purple_3</item>
        <item name="hintTextColor">@color/neon_purple_3</item>
        <item name="endIconTint">@color/neon_purple_3</item>
    </style>

    <style name="CustomDropdownStyle.Neon">
        <item name="android:background">@drawable/dropdown_background_solid_neon</item>
        <item name="android:textColor">@color/black</item>
    </style>
</resources>
