<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res"><file name="slide_down" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\anim\slide_down.xml" qualifiers="" type="anim"/><file name="slide_up" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\anim\slide_up.xml" qualifiers="" type="anim"/><file name="baseline_attractions_24" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\drawable\baseline_attractions_24.xml" qualifiers="" type="drawable"/><file name="dropdown_background" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\drawable\dropdown_background.xml" qualifiers="" type="drawable"/><file name="dropdown_background_blue" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\drawable\dropdown_background_blue.xml" qualifiers="" type="drawable"/><file name="dropdown_background_blue_dark" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\drawable\dropdown_background_blue_dark.xml" qualifiers="" type="drawable"/><file name="dropdown_background_crimson" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\drawable\dropdown_background_crimson.xml" qualifiers="" type="drawable"/><file name="dropdown_background_crimson_dark" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\drawable\dropdown_background_crimson_dark.xml" qualifiers="" type="drawable"/><file name="dropdown_background_dark" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\drawable\dropdown_background_dark.xml" qualifiers="" type="drawable"/><file name="dropdown_background_dark_blue" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\drawable\dropdown_background_dark_blue.xml" qualifiers="" type="drawable"/><file name="dropdown_background_dark_blue_dark" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\drawable\dropdown_background_dark_blue_dark.xml" qualifiers="" type="drawable"/><file name="dropdown_background_neon" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\drawable\dropdown_background_neon.xml" qualifiers="" type="drawable"/><file name="dropdown_background_neon_dark" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\drawable\dropdown_background_neon_dark.xml" qualifiers="" type="drawable"/><file name="dropdown_background_ocean" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\drawable\dropdown_background_ocean.xml" qualifiers="" type="drawable"/><file name="dropdown_background_ocean_dark" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\drawable\dropdown_background_ocean_dark.xml" qualifiers="" type="drawable"/><file name="dropdown_background_snooker" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\drawable\dropdown_background_snooker.xml" qualifiers="" type="drawable"/><file name="dropdown_background_snooker_dark" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\drawable\dropdown_background_snooker_dark.xml" qualifiers="" type="drawable"/><file name="dropdown_background_solid_blue" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\drawable\dropdown_background_solid_blue.xml" qualifiers="" type="drawable"/><file name="dropdown_background_solid_crimson" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\drawable\dropdown_background_solid_crimson.xml" qualifiers="" type="drawable"/><file name="dropdown_background_solid_dark" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\drawable\dropdown_background_solid_dark.xml" qualifiers="" type="drawable"/><file name="dropdown_background_solid_dark_blue" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\drawable\dropdown_background_solid_dark_blue.xml" qualifiers="" type="drawable"/><file name="dropdown_background_solid_default" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\drawable\dropdown_background_solid_default.xml" qualifiers="" type="drawable"/><file name="dropdown_background_solid_neon" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\drawable\dropdown_background_solid_neon.xml" qualifiers="" type="drawable"/><file name="dropdown_background_solid_ocean" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\drawable\dropdown_background_solid_ocean.xml" qualifiers="" type="drawable"/><file name="dropdown_background_solid_snooker" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\drawable\dropdown_background_solid_snooker.xml" qualifiers="" type="drawable"/><file name="ic_add" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\drawable\ic_add.xml" qualifiers="" type="drawable"/><file name="ic_add_exercise_trainingsplan" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\drawable\ic_add_exercise_trainingsplan.xml" qualifiers="" type="drawable"/><file name="ic_arrow_forward" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\drawable\ic_arrow_forward.xml" qualifiers="" type="drawable"/><file name="ic_assessment" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\drawable\ic_assessment.xml" qualifiers="" type="drawable"/><file name="ic_assessment_new" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\drawable\ic_assessment_new.xml" qualifiers="" type="drawable"/><file name="ic_calendar" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\drawable\ic_calendar.xml" qualifiers="" type="drawable"/><file name="ic_delete" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\drawable\ic_delete.xml" qualifiers="" type="drawable"/><file name="ic_fitness" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\drawable\ic_fitness.xml" qualifiers="" type="drawable"/><file name="ic_graph" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\drawable\ic_graph.xml" qualifiers="" type="drawable"/><file name="ic_history" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\drawable\ic_history.xml" qualifiers="" type="drawable"/><file name="ic_launcher_background" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="ic_notification" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\drawable\ic_notification.xml" qualifiers="" type="drawable"/><file name="ic_remove_exercise_trainingsplan" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\drawable\ic_remove_exercise_trainingsplan.xml" qualifiers="" type="drawable"/><file name="ic_results" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\drawable\ic_results.xml" qualifiers="" type="drawable"/><file name="ic_results_new" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\drawable\ic_results_new.xml" qualifiers="" type="drawable"/><file name="ic_save" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\drawable\ic_save.xml" qualifiers="" type="drawable"/><file name="ic_settings" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\drawable\ic_settings.xml" qualifiers="" type="drawable"/><file name="ic_tasks" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\drawable\ic_tasks.xml" qualifiers="" type="drawable"/><file name="ic_tasks_new" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\drawable\ic_tasks_new.xml" qualifiers="" type="drawable"/><file name="ic_timer" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\drawable\ic_timer.xml" qualifiers="" type="drawable"/><file name="ic_training_new" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\drawable\ic_training_new.xml" qualifiers="" type="drawable"/><file name="marker_background" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\drawable\marker_background.xml" qualifiers="" type="drawable"/><file name="activity_main" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="dialog_add_edit_exercise" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\layout\dialog_add_edit_exercise.xml" qualifiers="" type="layout"/><file name="dialog_add_exercise" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\layout\dialog_add_exercise.xml" qualifiers="" type="layout"/><file name="dialog_custom_exercise" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\layout\dialog_custom_exercise.xml" qualifiers="" type="layout"/><file name="dialog_custom_timeframe" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\layout\dialog_custom_timeframe.xml" qualifiers="" type="layout"/><file name="dialog_edit_connection" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\layout\dialog_edit_connection.xml" qualifiers="" type="layout"/><file name="dialog_exercise_selection" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\layout\dialog_exercise_selection.xml" qualifiers="" type="layout"/><file name="dialog_legend" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\layout\dialog_legend.xml" qualifiers="" type="layout"/><file name="dialog_select_points" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\layout\dialog_select_points.xml" qualifiers="" type="layout"/><file name="dropdown_item" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\layout\dropdown_item.xml" qualifiers="" type="layout"/><file name="exercise_selection_item" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\layout\exercise_selection_item.xml" qualifiers="" type="layout"/><file name="fragment_after_tournament" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\layout\fragment_after_tournament.xml" qualifiers="" type="layout"/><file name="fragment_after_training" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\layout\fragment_after_training.xml" qualifiers="" type="layout"/><file name="fragment_aufgaben" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\layout\fragment_aufgaben.xml" qualifiers="" type="layout"/><file name="fragment_before_tournament" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\layout\fragment_before_tournament.xml" qualifiers="" type="layout"/><file name="fragment_before_training" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\layout\fragment_before_training.xml" qualifiers="" type="layout"/><file name="fragment_breakbuilding" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\layout\fragment_breakbuilding.xml" qualifiers="" type="layout"/><file name="fragment_ergebniserfassung" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\layout\fragment_ergebniserfassung.xml" qualifiers="" type="layout"/><file name="fragment_exercise_detail" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\layout\fragment_exercise_detail.xml" qualifiers="" type="layout"/><file name="fragment_exercise_detail_splits" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\layout\fragment_exercise_detail_splits.xml" qualifiers="" type="layout"/><file name="fragment_exercise_detail_stellungsspiel" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\layout\fragment_exercise_detail_stellungsspiel.xml" qualifiers="" type="layout"/><file name="fragment_exercise_detail_timeonly" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\layout\fragment_exercise_detail_timeonly.xml" qualifiers="" type="layout"/><file name="fragment_exercise_graph" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\layout\fragment_exercise_graph.xml" qualifiers="" type="layout"/><file name="fragment_exercise_history" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\layout\fragment_exercise_history.xml" qualifiers="" type="layout"/><file name="fragment_exercise_history_detail" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\layout\fragment_exercise_history_detail.xml" qualifiers="" type="layout"/><file name="fragment_exercise_selection" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\layout\fragment_exercise_selection.xml" qualifiers="" type="layout"/><file name="fragment_generic_exercise_list" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\layout\fragment_generic_exercise_list.xml" qualifiers="" type="layout"/><file name="fragment_graph" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\layout\fragment_graph.xml" qualifiers="" type="layout"/><file name="fragment_graph_detail" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\layout\fragment_graph_detail.xml" qualifiers="" type="layout"/><file name="fragment_history" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\layout\fragment_history.xml" qualifiers="" type="layout"/><file name="fragment_history_detail" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\layout\fragment_history_detail.xml" qualifiers="" type="layout"/><file name="fragment_login" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\layout\fragment_login.xml" qualifiers="" type="layout"/><file name="fragment_manage_exercises" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\layout\fragment_manage_exercises.xml" qualifiers="" type="layout"/><file name="fragment_potting" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\layout\fragment_potting.xml" qualifiers="" type="layout"/><file name="fragment_profile" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\layout\fragment_profile.xml" qualifiers="" type="layout"/><file name="fragment_questions" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\layout\fragment_questions.xml" qualifiers="" type="layout"/><file name="fragment_questions_after_tournament" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\layout\fragment_questions_after_tournament.xml" qualifiers="" type="layout"/><file name="fragment_questions_after_training" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\layout\fragment_questions_after_training.xml" qualifiers="" type="layout"/><file name="fragment_questions_before_tournament" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\layout\fragment_questions_before_tournament.xml" qualifiers="" type="layout"/><file name="fragment_questions_before_training" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\layout\fragment_questions_before_training.xml" qualifiers="" type="layout"/><file name="fragment_register" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\layout\fragment_register.xml" qualifiers="" type="layout"/><file name="fragment_safeties" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\layout\fragment_safeties.xml" qualifiers="" type="layout"/><file name="fragment_selbsteinschaetzung" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\layout\fragment_selbsteinschaetzung.xml" qualifiers="" type="layout"/><file name="fragment_settings" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\layout\fragment_settings.xml" qualifiers="" type="layout"/><file name="fragment_splits" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\layout\fragment_splits.xml" qualifiers="" type="layout"/><file name="fragment_stellungsspiel" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\layout\fragment_stellungsspiel.xml" qualifiers="" type="layout"/><file name="fragment_task_history" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\layout\fragment_task_history.xml" qualifiers="" type="layout"/><file name="fragment_task_list" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\layout\fragment_task_list.xml" qualifiers="" type="layout"/><file name="fragment_technik" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\layout\fragment_technik.xml" qualifiers="" type="layout"/><file name="fragment_trainingsplan" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\layout\fragment_trainingsplan.xml" qualifiers="" type="layout"/><file name="fragment_trainingsplan_history" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\layout\fragment_trainingsplan_history.xml" qualifiers="" type="layout"/><file name="fragment_user_management" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\layout\fragment_user_management.xml" qualifiers="" type="layout"/><file name="item_assessment" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\layout\item_assessment.xml" qualifiers="" type="layout"/><file name="item_breakbuilding" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\layout\item_breakbuilding.xml" qualifiers="" type="layout"/><file name="item_date" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\layout\item_date.xml" qualifiers="" type="layout"/><file name="item_exercise" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\layout\item_exercise.xml" qualifiers="" type="layout"/><file name="item_exercise_category" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\layout\item_exercise_category.xml" qualifiers="" type="layout"/><file name="item_exercise_definition" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\layout\item_exercise_definition.xml" qualifiers="" type="layout"/><file name="item_exercise_history" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\layout\item_exercise_history.xml" qualifiers="" type="layout"/><file name="item_history" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\layout\item_history.xml" qualifiers="" type="layout"/><file name="item_history_detail" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\layout\item_history_detail.xml" qualifiers="" type="layout"/><file name="item_legend" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\layout\item_legend.xml" qualifiers="" type="layout"/><file name="item_month_header" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\layout\item_month_header.xml" qualifiers="" type="layout"/><file name="item_question" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\layout\item_question.xml" qualifiers="" type="layout"/><file name="item_task" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\layout\item_task.xml" qualifiers="" type="layout"/><file name="item_task_category" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\layout\item_task_category.xml" qualifiers="" type="layout"/><file name="item_task_category_header" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\layout\item_task_category_header.xml" qualifiers="" type="layout"/><file name="item_task_completion" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\layout\item_task_completion.xml" qualifiers="" type="layout"/><file name="item_task_completion_header" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\layout\item_task_completion_header.xml" qualifiers="" type="layout"/><file name="item_task_history" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\layout\item_task_history.xml" qualifiers="" type="layout"/><file name="item_training_assessment" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\layout\item_training_assessment.xml" qualifiers="" type="layout"/><file name="item_user_connection" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\layout\item_user_connection.xml" qualifiers="" type="layout"/><file name="marker_view" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\layout\marker_view.xml" qualifiers="" type="layout"/><file name="theme_preview_blue" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\layout\theme_preview_blue.xml" qualifiers="" type="layout"/><file name="theme_preview_crimson" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\layout\theme_preview_crimson.xml" qualifiers="" type="layout"/><file name="theme_preview_dark" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\layout\theme_preview_dark.xml" qualifiers="" type="layout"/><file name="theme_preview_dark_blue" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\layout\theme_preview_dark_blue.xml" qualifiers="" type="layout"/><file name="theme_preview_light" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\layout\theme_preview_light.xml" qualifiers="" type="layout"/><file name="theme_preview_neon" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\layout\theme_preview_neon.xml" qualifiers="" type="layout"/><file name="theme_preview_ocean" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\layout\theme_preview_ocean.xml" qualifiers="" type="layout"/><file name="theme_preview_snooker" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\layout\theme_preview_snooker.xml" qualifiers="" type="layout"/><file name="trainingsplan_history_item" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\layout\trainingsplan_history_item.xml" qualifiers="" type="layout"/><file name="trainingsplan_item" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\layout\trainingsplan_item.xml" qualifiers="" type="layout"/><file name="bottom_nav_menu" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\menu\bottom_nav_menu.xml" qualifiers="" type="menu"/><file name="menu_history" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\menu\menu_history.xml" qualifiers="" type="menu"/><file name="menu_timeframe" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\menu\menu_timeframe.xml" qualifiers="" type="menu"/><file name="menu_timeframe_simple" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\menu\menu_timeframe_simple.xml" qualifiers="" type="menu"/><file name="ic_launcher" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\mipmap-hdpi\ic_launcher.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_background" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\mipmap-hdpi\ic_launcher_background.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\mipmap-hdpi\ic_launcher_foreground.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\mipmap-hdpi\ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\mipmap-mdpi\ic_launcher.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_background" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\mipmap-mdpi\ic_launcher_background.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\mipmap-mdpi\ic_launcher_foreground.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\mipmap-mdpi\ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\mipmap-xhdpi\ic_launcher.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_background" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\mipmap-xhdpi\ic_launcher_background.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\mipmap-xhdpi\ic_launcher_foreground.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\mipmap-xhdpi\ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\mipmap-xxhdpi\ic_launcher.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_background" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\mipmap-xxhdpi\ic_launcher_background.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\mipmap-xxhdpi\ic_launcher_foreground.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\mipmap-xxxhdpi\ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_background" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\mipmap-xxxhdpi\ic_launcher_background.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\mipmap-xxxhdpi\ic_launcher_foreground.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="auth_nav_graph" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\navigation\auth_nav_graph.xml" qualifiers="" type="navigation"/><file name="mobile_navigation" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\navigation\mobile_navigation.xml" qualifiers="" type="navigation"/><file name="nav_graph" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\navigation\nav_graph.xml" qualifiers="" type="navigation"/><file path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\values\colors.xml" qualifiers=""><color name="selected_color">#E0E0E0</color><color name="purple_200">#FFBB86FC</color><color name="purple_500">#FF6200EE</color><color name="purple_700">#FF3700B3</color><color name="teal_200">#FF03DAC5</color><color name="teal_700">#FF018786</color><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color><color name="dark_gray">#303030</color><color name="light_gray">#E0E0E0</color><color name="snooker_green_200">#81C784</color><color name="snooker_green_500">#0B6623</color><color name="snooker_green_700">#094D1B</color><color name="snooker_red_200">#EF9A9A</color><color name="snooker_red_500">#D32F2F</color><color name="snooker_red_700">#B71C1C</color><color name="snooker_brown">#8B4513</color><color name="snooker_blue">#1565C0</color><color name="snooker_pink">#E91E63</color><color name="snooker_black">#212121</color><color name="snooker_yellow">#FBC02D</color><color name="snooker_card_background_light">#C8E6C9</color><color name="snooker_card_background_dark">#1B5E20</color><color name="blue_200">#90CAF9</color><color name="blue_500">#2196F3</color><color name="blue_700">#1976D2</color><color name="orange_200">#FFCC80</color><color name="orange_500">#FF9800</color><color name="orange_700">#F57C00</color><color name="blue_card_background_light">#E3F2FD</color><color name="blue_card_background_dark">#0D47A1</color><color name="dark_blue_200">#64B5F6</color><color name="dark_blue_500">#0D47A1</color><color name="dark_blue_700">#0A3880</color><color name="cyan_200">#80DEEA</color><color name="cyan_500">#00BCD4</color><color name="cyan_700">#0097A7</color><color name="dark_blue_card_background_light">#E1F5FE</color><color name="dark_blue_card_background_dark">#01579B</color><color name="ocean_darkest">#0B132B</color><color name="ocean_dark">#1C2541</color><color name="ocean_medium">#3A506B</color><color name="ocean_light">#5BC0BE</color><color name="ocean_lightest">#6FFFE9</color><color name="ocean_card_background_light">#DAFFFE</color><color name="ocean_card_background_dark">#1C2541</color><color name="crimson_darkest">#250902</color><color name="crimson_dark">#38040E</color><color name="crimson_medium">#640D14</color><color name="crimson_light">#800E13</color><color name="crimson_lightest">#AD2831</color><color name="crimson_card_background_light">#FFEBEE</color><color name="crimson_card_background_dark">#38040E</color><color name="neon_purple_1">#2D00F7</color><color name="neon_purple_2">#6A00F4</color><color name="neon_purple_3">#8900F2</color><color name="neon_purple_4">#A100F2</color><color name="neon_purple_5">#B100E8</color><color name="neon_purple_6">#BC00DD</color><color name="neon_purple_7">#D100D1</color><color name="neon_pink_1">#DB00B6</color><color name="neon_pink_2">#E500A4</color><color name="neon_pink_3">#F20089</color><color name="neon_card_background_light">#F3E5F5</color><color name="neon_card_background_dark">#6A00F4</color></file><file path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\values\default_dialog_styles.xml" qualifiers=""><style name="DefaultAnimationDialog" parent="ThemeOverlay.MaterialComponents.Dialog.Alert">
        
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowMinWidthMajor">0dp</item>
        <item name="android:windowMinWidthMinor">0dp</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:windowContentOverlay">@null</item>
    </style><style name="DefaultAnimationDialog.Snooker">
        <item name="android:windowBackground">@drawable/dropdown_background_snooker</item>
        <item name="android:textColor">@color/black</item>
        <item name="colorAccent">@color/snooker_red_500</item>
        <item name="buttonBarPositiveButtonStyle">@style/SnookerPositiveButtonStyle</item>
        <item name="buttonBarNegativeButtonStyle">@style/SnookerNegativeButtonStyle</item>
    </style><style name="DefaultAnimationDialog.Blue">
        <item name="android:windowBackground">@drawable/dropdown_background_blue</item>
        <item name="android:textColor">@color/black</item>
        <item name="colorAccent">@color/blue_500</item>
        <item name="buttonBarPositiveButtonStyle">@style/BluePositiveButtonStyle</item>
        <item name="buttonBarNegativeButtonStyle">@style/BlueNegativeButtonStyle</item>
    </style><style name="DefaultAnimationDialog.DarkBlue">
        <item name="android:windowBackground">@drawable/dropdown_background_dark_blue</item>
        <item name="android:textColor">@color/black</item>
        <item name="colorAccent">@color/dark_blue_500</item>
        <item name="buttonBarPositiveButtonStyle">@style/DarkBluePositiveButtonStyle</item>
        <item name="buttonBarNegativeButtonStyle">@style/DarkBlueNegativeButtonStyle</item>
    </style><style name="DefaultAnimationDialog.Dark">
        <item name="android:windowBackground">@drawable/dropdown_background_dark</item>
        <item name="android:textColor">@color/white</item>
        <item name="colorAccent">@color/purple_200</item>
        <item name="buttonBarPositiveButtonStyle">@style/DarkPositiveButtonStyle</item>
        <item name="buttonBarNegativeButtonStyle">@style/DarkNegativeButtonStyle</item>
    </style></file><file path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\values\dimens.xml" qualifiers=""><dimen name="dialog_input_min_height">150dp</dimen><dimen name="legend_dialog_width">240dp</dimen></file><file path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\values\dropdown_styles.xml" qualifiers=""><style name="CustomDropdownStyle" parent="Widget.AppCompat.DropDownItem.Spinner">
        <item name="android:background">@drawable/dropdown_background_solid_default</item>
        <item name="android:textColor">@color/black</item>
        <item name="android:paddingStart">16dp</item>
        <item name="android:paddingEnd">16dp</item>
        <item name="android:paddingTop">12dp</item>
        <item name="android:paddingBottom">12dp</item>
    </style><style name="CustomDropdownStyle.Snooker">
        <item name="android:background">@drawable/dropdown_background_solid_snooker</item>
        <item name="android:textColor">@color/black</item>
    </style><style name="CustomDropdownStyle.Blue">
        <item name="android:background">@drawable/dropdown_background_solid_blue</item>
        <item name="android:textColor">@color/black</item>
    </style><style name="CustomDropdownStyle.DarkBlue">
        <item name="android:background">@drawable/dropdown_background_solid_dark_blue</item>
        <item name="android:textColor">@color/black</item>
    </style><style name="CustomDropdownStyle.Dark">
        <item name="android:background">@drawable/dropdown_background_solid_dark</item>
        <item name="android:textColor">@color/white</item>
    </style></file><file path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\values\ic_launcher_background.xml" qualifiers=""><color name="ic_launcher_background">#0E6C1C</color></file><file path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\values\popup_menu_item_styles.xml" qualifiers=""><style name="PopupMenuItemStyle" parent="TextAppearance.AppCompat.Widget.PopupMenu.Small">
        <item name="android:textColor">@color/black</item>
    </style><style name="PopupMenuItemStyle_Snooker" parent="PopupMenuItemStyle">
        <item name="android:textColor">@color/black</item>
        <item name="android:background">@drawable/dropdown_background_snooker</item>
    </style><style name="PopupMenuItemStyle_Blue" parent="PopupMenuItemStyle">
        <item name="android:textColor">@color/black</item>
        <item name="android:background">@drawable/dropdown_background_blue</item>
    </style><style name="PopupMenuItemStyle_DarkBlue" parent="PopupMenuItemStyle">
        <item name="android:textColor">@color/black</item>
        <item name="android:background">@drawable/dropdown_background_dark_blue</item>
    </style><style name="PopupMenuItemStyle_Dark" parent="PopupMenuItemStyle">
        <item name="android:textColor">@color/white</item>
        <item name="android:background">@drawable/dropdown_background_dark</item>
    </style></file><file path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\values\popup_styles.xml" qualifiers=""><style name="PopupMenuStyle" parent="Widget.AppCompat.PopupMenu">
        <item name="android:popupBackground">@drawable/dropdown_background</item>
        <item name="android:textColor">@color/black</item>
        <item name="android:itemBackground">@drawable/dropdown_background</item>
        <item name="android:dropDownListViewStyle">@style/PopupMenuListViewStyle</item>
    </style><style name="PopupMenuStyle_Snooker" parent="PopupMenuStyle">
        <item name="android:popupBackground">@drawable/dropdown_background_snooker</item>
        <item name="android:textColor">@color/black</item>
        <item name="android:itemBackground">@drawable/dropdown_background_snooker</item>
        <item name="android:dropDownListViewStyle">@style/PopupMenuListViewStyle_Snooker</item>
    </style><style name="PopupMenuStyle_Blue" parent="PopupMenuStyle">
        <item name="android:popupBackground">@drawable/dropdown_background_blue</item>
        <item name="android:textColor">@color/black</item>
        <item name="android:itemBackground">@drawable/dropdown_background_blue</item>
        <item name="android:dropDownListViewStyle">@style/PopupMenuListViewStyle_Blue</item>
    </style><style name="PopupMenuStyle_DarkBlue" parent="PopupMenuStyle">
        <item name="android:popupBackground">@drawable/dropdown_background_dark_blue</item>
        <item name="android:textColor">@color/black</item>
        <item name="android:itemBackground">@drawable/dropdown_background_dark_blue</item>
        <item name="android:dropDownListViewStyle">@style/PopupMenuListViewStyle_DarkBlue</item>
    </style><style name="PopupMenuStyle_Dark" parent="PopupMenuStyle">
        <item name="android:popupBackground">@drawable/dropdown_background_dark</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:itemBackground">@drawable/dropdown_background_dark</item>
        <item name="android:dropDownListViewStyle">@style/PopupMenuListViewStyle_Dark</item>
    </style><style name="PopupMenuListViewStyle" parent="Widget.AppCompat.ListView.DropDown">
        <item name="android:divider">@android:color/transparent</item>
        <item name="android:dividerHeight">0dp</item>
        <item name="android:background">@drawable/dropdown_background</item>
    </style><style name="PopupMenuListViewStyle_Snooker" parent="PopupMenuListViewStyle">
        <item name="android:background">@drawable/dropdown_background_snooker</item>
    </style><style name="PopupMenuListViewStyle_Blue" parent="PopupMenuListViewStyle">
        <item name="android:background">@drawable/dropdown_background_blue</item>
    </style><style name="PopupMenuListViewStyle_DarkBlue" parent="PopupMenuListViewStyle">
        <item name="android:background">@drawable/dropdown_background_dark_blue</item>
    </style><style name="PopupMenuListViewStyle_Dark" parent="PopupMenuListViewStyle">
        <item name="android:background">@drawable/dropdown_background_dark</item>
    </style><style name="PopupMenuStyle_Ocean" parent="PopupMenuStyle">
        <item name="android:popupBackground">@drawable/dropdown_background_ocean</item>
        <item name="android:textColor">@color/black</item>
        <item name="android:itemBackground">@drawable/dropdown_background_ocean</item>
        <item name="android:dropDownListViewStyle">@style/PopupMenuListViewStyle_Ocean</item>
    </style><style name="PopupMenuListViewStyle_Ocean" parent="PopupMenuListViewStyle">
        <item name="android:background">@drawable/dropdown_background_ocean</item>
    </style><style name="PopupMenuStyle_Crimson" parent="PopupMenuStyle">
        <item name="android:popupBackground">@drawable/dropdown_background_crimson</item>
        <item name="android:textColor">@color/black</item>
        <item name="android:itemBackground">@drawable/dropdown_background_crimson</item>
        <item name="android:dropDownListViewStyle">@style/PopupMenuListViewStyle_Crimson</item>
    </style><style name="PopupMenuListViewStyle_Crimson" parent="PopupMenuListViewStyle">
        <item name="android:background">@drawable/dropdown_background_crimson</item>
    </style><style name="PopupMenuStyle_Neon" parent="PopupMenuStyle">
        <item name="android:popupBackground">@drawable/dropdown_background_neon</item>
        <item name="android:textColor">@color/black</item>
        <item name="android:itemBackground">@drawable/dropdown_background_neon</item>
        <item name="android:dropDownListViewStyle">@style/PopupMenuListViewStyle_Neon</item>
    </style><style name="PopupMenuListViewStyle_Neon" parent="PopupMenuListViewStyle">
        <item name="android:background">@drawable/dropdown_background_neon</item>
    </style><style name="DatePickerDialogTheme" parent="ThemeOverlay.MaterialComponents.Dialog">
        <item name="colorAccent">?attr/colorPrimary</item>
        <item name="android:buttonBarPositiveButtonStyle">@style/DatePickerButtonStyle</item>
        <item name="android:buttonBarNegativeButtonStyle">@style/DatePickerButtonStyle</item>
    </style><style name="DatePickerButtonStyle" parent="Widget.MaterialComponents.Button.TextButton.Dialog">
        <item name="android:textColor">?attr/colorPrimary</item>
    </style></file><file path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">Die Snooker App</string><string name="save_button">Save</string><string name="cancel_button">Cancel</string><string name="edit_button">Edit</string><string name="delete_button">Delete</string><string name="manage_my_exercises_button">Manage My Exercises</string><string name="add_exercise_button_label">Add Exercise</string><string name="dialog_title_add_exercise">Add Exercise</string><string name="dialog_title_edit_exercise">Edit Exercise</string><string name="fragment_label_manage_exercises">Manage Exercises</string><string name="dialog_title_confirmation">Confirmation</string><string name="label_name">Name</string><string name="label_category">Category</string><string name="label_description">Description</string><string name="toast_no_specific_view_for_category">No specific view for category: %s</string><string name="default_exercises_added_toast">Default exercises added to your library.</string><string name="exercise_saved_toast">Exercise saved.</string><string name="exercise_deleted_toast">Exercise deleted.</string><string name="error_saving_exercise_toast">Error saving exercise.</string><string name="error_deleting_exercise_toast">Error deleting exercise.</string><string name="error_loading_exercises_toast">Error loading exercises.</string><string name="no_exercises_found_toast">No exercises found.</string><string name="please_log_in_to_manage_exercises_toast">Please log in to manage exercises.</string><string name="please_log_in_to_load_categories_toast">Please log in to load categories.</string><string name="no_categories_found_toast">No categories found. Add exercises to create categories.</string><string name="please_log_in_to_load_exercises_toast">Please log in to load exercises.</string><string name="no_exercises_in_category_toast">No exercises found for %s.</string><string name="confirm_delete_exercise_message">Delete exercise \'%s\'?</string><string name="input_error_name_required">Name is required.</string><string name="input_error_category_required">Category is required.</string><string name="label_new_category">New Category Name</string><string name="yes">Yes</string><string name="no">No</string><string name="action_create_new_category">+ Create New Category</string><string name="error_user_id_not_found">Error: User ID not found</string><string name="error_user_id_not_found_info">Could not retrieve user information.</string><string name="default_exercises_partial_success_toast">Some default exercises added, some failed.</string><string name="default_exercises_failed_toast">Failed to add default exercises.</string><string name="error_loading_categories_toast">Failed to load categories.</string><string name="exercise_not_deletable_toast">is not deletable.</string><string name="dialog_title_delete_exercise">Delete Exercise</string><string name="no_exercises_in_category_generic">No exercises found in this category.</string><string name="unknown_category">Unknown Category</string><string name="category_not_specified_toast">Category not specified.</string><string name="label_empty_exercise_list">No custom exercises yet. Tap \'+\' to add one.</string><string name="toast_please_select_exercise">Please select at least one exercise.</string><string name="confirm_remove_exercise_from_plan_message">Remove exercise \'%s\' from the training plan?</string><string name="debug_menu_title">Debug Menu</string><string-array name="debug_menu_options">
        <item>Test Notification (Normal)</item>
        <item>Force Notification (Add Test Exercise)</item>
        <item>Reschedule Weekly Reminder</item>
    </string-array><string name="toast_plan_reset_archived">Training plan was reset and archived.</string><string name="toast_notification_sent_success">Notification sent successfully!</string><string name="toast_notification_sent_none">No notification sent - no incomplete exercises.</string><string name="toast_notification_forced">Forced notification with test exercise.</string><string name="toast_reminder_rescheduled">Weekly reminder rescheduled.</string><string name="title_category_exercises_generic">Category Exercises</string><string name="exercise_type_normal">Normal</string><string name="exercise_type_splits">Splits</string><string name="label_exercise_type">Exercise Type</string><string name="exercise_type_timeonly">Time Only</string><string name="exercise_type_stellungsspiel">Positional Play</string></file><file path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\values\styles.xml" qualifiers=""><style name="DropdownDialog" parent="ThemeOverlay.MaterialComponents.Dialog.Alert">
        <item name="android:windowBackground">@drawable/dropdown_background</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowAnimationStyle">@style/DropdownAnimation</item>
        <item name="android:windowMinWidthMajor">0dp</item>
        <item name="android:windowMinWidthMinor">0dp</item>
        <item name="android:backgroundDimEnabled">false</item>
        <item name="android:windowContentOverlay">@null</item>
    </style><style name="DropdownDialog.Snooker">
        <item name="android:windowBackground">@drawable/dropdown_background_snooker</item>
        <item name="android:textColor">@color/black</item>
        <item name="colorAccent">@color/snooker_red_500</item>
        <item name="buttonBarPositiveButtonStyle">@style/SnookerPositiveButtonStyle</item>
        <item name="buttonBarNegativeButtonStyle">@style/SnookerNegativeButtonStyle</item>
    </style><style name="SnookerPositiveButtonStyle" parent="Widget.MaterialComponents.Button.TextButton.Dialog">
        <item name="android:textColor">@color/snooker_red_500</item>
    </style><style name="SnookerNegativeButtonStyle" parent="Widget.MaterialComponents.Button.TextButton.Dialog">
        <item name="android:textColor">@color/snooker_green_500</item>
    </style><style name="DropdownDialog.Blue">
        <item name="android:windowBackground">@drawable/dropdown_background_blue</item>
        <item name="android:textColor">@color/black</item>
        <item name="colorAccent">@color/blue_500</item>
        <item name="buttonBarPositiveButtonStyle">@style/BluePositiveButtonStyle</item>
        <item name="buttonBarNegativeButtonStyle">@style/BlueNegativeButtonStyle</item>
    </style><style name="BluePositiveButtonStyle" parent="Widget.MaterialComponents.Button.TextButton.Dialog">
        <item name="android:textColor">@color/blue_500</item>
    </style><style name="BlueNegativeButtonStyle" parent="Widget.MaterialComponents.Button.TextButton.Dialog">
        <item name="android:textColor">@color/orange_500</item>
    </style><style name="DropdownDialog.DarkBlue">
        <item name="android:windowBackground">@drawable/dropdown_background_dark_blue</item>
        <item name="android:textColor">@color/black</item>
        <item name="colorAccent">@color/dark_blue_500</item>
        <item name="buttonBarPositiveButtonStyle">@style/DarkBluePositiveButtonStyle</item>
        <item name="buttonBarNegativeButtonStyle">@style/DarkBlueNegativeButtonStyle</item>
    </style><style name="DarkBluePositiveButtonStyle" parent="Widget.MaterialComponents.Button.TextButton.Dialog">
        <item name="android:textColor">@color/dark_blue_500</item>
    </style><style name="DarkBlueNegativeButtonStyle" parent="Widget.MaterialComponents.Button.TextButton.Dialog">
        <item name="android:textColor">@color/cyan_500</item>
    </style><style name="DropdownDialog.Dark">
        <item name="android:windowBackground">@drawable/dropdown_background_dark</item>
        <item name="android:textColor">@color/white</item>
        <item name="colorAccent">@color/purple_200</item>
        <item name="buttonBarPositiveButtonStyle">@style/DarkPositiveButtonStyle</item>
        <item name="buttonBarNegativeButtonStyle">@style/DarkNegativeButtonStyle</item>
    </style><style name="DarkPositiveButtonStyle" parent="Widget.MaterialComponents.Button.TextButton.Dialog">
        <item name="android:textColor">@color/purple_200</item>
    </style><style name="DarkNegativeButtonStyle" parent="Widget.MaterialComponents.Button.TextButton.Dialog">
        <item name="android:textColor">@color/teal_200</item>
    </style><style name="DropdownAnimation">
        <item name="android:windowEnterAnimation">@anim/slide_up</item>
        <item name="android:windowExitAnimation">@anim/slide_down</item>
    </style></file><file path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\values\tab_styles.xml" qualifiers=""><style name="CustomTabLayout" parent="Widget.MaterialComponents.TabLayout">
        <item name="tabBackground">@color/white</item>
        <item name="tabIndicatorColor">?attr/colorPrimary</item>
        <item name="tabSelectedTextColor">?attr/colorPrimary</item>
        <item name="tabTextColor">@color/black</item>
    </style><style name="CustomTabLayout.Snooker">
        <item name="tabBackground">@color/snooker_card_background_light</item>
        <item name="tabIndicatorColor">@color/snooker_red_500</item>
        <item name="tabSelectedTextColor">@color/snooker_red_500</item>
        <item name="tabTextColor">@color/black</item>
    </style><style name="CustomTabLayout.Blue">
        <item name="tabBackground">@color/blue_card_background_light</item>
        <item name="tabIndicatorColor">@color/blue_500</item>
        <item name="tabSelectedTextColor">@color/blue_500</item>
        <item name="tabTextColor">@color/black</item>
    </style><style name="CustomTabLayout.DarkBlue">
        <item name="tabBackground">@color/dark_blue_card_background_light</item>
        <item name="tabIndicatorColor">@color/dark_blue_500</item>
        <item name="tabSelectedTextColor">@color/dark_blue_500</item>
        <item name="tabTextColor">@color/black</item>
    </style><style name="CustomTabLayout.Dark">
        <item name="tabBackground">@color/dark_gray</item>
        <item name="tabIndicatorColor">@color/purple_200</item>
        <item name="tabSelectedTextColor">@color/purple_200</item>
        <item name="tabTextColor">@color/white</item>
    </style><style name="CustomTabLayout.Ocean">
        <item name="tabBackground">@color/ocean_card_background_light</item>
        <item name="tabIndicatorColor">@color/ocean_medium</item>
        <item name="tabSelectedTextColor">@color/ocean_medium</item>
        <item name="tabTextColor">@color/black</item>
    </style><style name="CustomTabLayout.Crimson">
        <item name="tabBackground">@color/crimson_card_background_light</item>
        <item name="tabIndicatorColor">@color/crimson_medium</item>
        <item name="tabSelectedTextColor">@color/crimson_medium</item>
        <item name="tabTextColor">@color/black</item>
    </style><style name="CustomTabLayout.Neon">
        <item name="tabBackground">@color/neon_card_background_light</item>
        <item name="tabIndicatorColor">@color/neon_purple_3</item>
        <item name="tabSelectedTextColor">@color/neon_purple_3</item>
        <item name="tabTextColor">@color/black</item>
    </style></file><file path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\values\text_input_styles.xml" qualifiers=""><style name="CustomTextInputLayout" parent="Widget.MaterialComponents.TextInputLayout.OutlinedBox">
        <item name="boxStrokeColor">?attr/colorPrimary</item>
        <item name="hintTextColor">?attr/colorPrimary</item>
    </style><style name="CustomTextInputLayout.Snooker">
        <item name="boxStrokeColor">@color/snooker_red_500</item>
        <item name="hintTextColor">@color/snooker_red_500</item>
        <item name="boxBackgroundColor">@color/snooker_card_background_light</item>
    </style><style name="CustomTextInputLayout.Blue">
        <item name="boxStrokeColor">@color/blue_500</item>
        <item name="hintTextColor">@color/blue_500</item>
        <item name="boxBackgroundColor">@color/blue_card_background_light</item>
    </style><style name="CustomTextInputLayout.DarkBlue">
        <item name="boxStrokeColor">@color/dark_blue_500</item>
        <item name="hintTextColor">@color/dark_blue_500</item>
        <item name="boxBackgroundColor">@color/dark_blue_card_background_light</item>
    </style><style name="CustomTextInputLayout.Dark">
        <item name="boxStrokeColor">@color/purple_200</item>
        <item name="hintTextColor">@color/purple_200</item>
        <item name="boxBackgroundColor">@color/dark_gray</item>
    </style><style name="CustomDropdownTextInputLayout" parent="Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu">
        <item name="boxStrokeColor">?attr/colorPrimary</item>
        <item name="hintTextColor">?attr/colorPrimary</item>
    </style><style name="CustomDropdownTextInputLayout.Snooker">
        <item name="boxStrokeColor">@color/snooker_red_500</item>
        <item name="hintTextColor">@color/snooker_red_500</item>
        <item name="boxBackgroundColor">@color/snooker_card_background_light</item>
        <item name="endIconTint">@color/snooker_red_500</item>
    </style><style name="CustomDropdownTextInputLayout.Blue">
        <item name="boxStrokeColor">@color/blue_500</item>
        <item name="hintTextColor">@color/blue_500</item>
        <item name="boxBackgroundColor">@color/blue_card_background_light</item>
        <item name="endIconTint">@color/blue_500</item>
    </style><style name="CustomDropdownTextInputLayout.DarkBlue">
        <item name="boxStrokeColor">@color/dark_blue_500</item>
        <item name="hintTextColor">@color/dark_blue_500</item>
        <item name="boxBackgroundColor">@color/dark_blue_card_background_light</item>
        <item name="endIconTint">@color/dark_blue_500</item>
    </style><style name="CustomDropdownTextInputLayout.Dark">
        <item name="boxStrokeColor">@color/purple_200</item>
        <item name="hintTextColor">@color/purple_200</item>
        <item name="boxBackgroundColor">@color/dark_gray</item>
        <item name="endIconTint">@color/purple_200</item>
    </style></file><file path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\values\themes.xml" qualifiers=""><style name="Theme.DieSnookerApp" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        <item name="colorPrimary">@color/purple_500</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/white</item>
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>

        
        <item name="android:popupMenuStyle">@style/PopupMenuStyle</item>
        <item name="popupMenuStyle">@style/PopupMenuStyle</item>
        <item name="android:itemTextAppearance">@style/PopupMenuItemStyle</item>
    </style><style name="Theme.DieSnookerApp.Snooker" parent="Theme.MaterialComponents.Light.NoActionBar">
        <item name="colorPrimary">@color/snooker_red_500</item>
        <item name="colorPrimaryVariant">@color/snooker_red_700</item>
        <item name="colorOnPrimary">@color/white</item>
        <item name="colorSecondary">@color/snooker_green_500</item>
        <item name="colorSecondaryVariant">@color/snooker_green_700</item>
        <item name="colorOnSecondary">@color/white</item>
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        <item name="android:windowBackground">@color/snooker_green_200</item>
        <item name="android:textColorPrimary">@color/black</item>
        <item name="colorSurface">@color/snooker_green_200</item>
        <item name="colorOnSurface">@color/black</item>

        
        <item name="cardBackgroundColor">@color/snooker_card_background_light</item>
        <item name="materialCardViewStyle">@style/SnookerCardStyle</item>

        
        <item name="android:popupMenuStyle">@style/PopupMenuStyle_Snooker</item>
        <item name="popupMenuStyle">@style/PopupMenuStyle_Snooker</item>
        <item name="android:itemTextAppearance">@style/PopupMenuItemStyle_Snooker</item>
    </style><style name="SnookerCardStyle" parent="Widget.MaterialComponents.CardView">
        <item name="cardBackgroundColor">@color/snooker_card_background_light</item>
        <item name="cardElevation">4dp</item>
        <item name="cardCornerRadius">8dp</item>
    </style><style name="Theme.DieSnookerApp.Blue" parent="Theme.MaterialComponents.Light.NoActionBar">
        <item name="colorPrimary">@color/blue_500</item>
        <item name="colorPrimaryVariant">@color/blue_700</item>
        <item name="colorOnPrimary">@color/white</item>
        <item name="colorSecondary">@color/orange_500</item>
        <item name="colorSecondaryVariant">@color/orange_700</item>
        <item name="colorOnSecondary">@color/black</item>
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        <item name="android:windowBackground">@color/white</item>
        <item name="android:textColorPrimary">@color/black</item>
        <item name="colorSurface">@color/white</item>
        <item name="colorOnSurface">@color/black</item>

        
        <item name="cardBackgroundColor">@color/blue_card_background_light</item>
        <item name="materialCardViewStyle">@style/BlueCardStyle</item>

        
        <item name="android:popupMenuStyle">@style/PopupMenuStyle_Blue</item>
        <item name="popupMenuStyle">@style/PopupMenuStyle_Blue</item>
        <item name="android:itemTextAppearance">@style/PopupMenuItemStyle_Blue</item>
    </style><style name="BlueCardStyle" parent="Widget.MaterialComponents.CardView">
        <item name="cardBackgroundColor">@color/blue_card_background_light</item>
        <item name="cardElevation">4dp</item>
        <item name="cardCornerRadius">8dp</item>
    </style><style name="Theme.DieSnookerApp.DarkBlue" parent="Theme.MaterialComponents.Light.NoActionBar">
        <item name="colorPrimary">@color/dark_blue_500</item>
        <item name="colorPrimaryVariant">@color/dark_blue_700</item>
        <item name="colorOnPrimary">@color/white</item>
        <item name="colorSecondary">@color/cyan_500</item>
        <item name="colorSecondaryVariant">@color/cyan_700</item>
        <item name="colorOnSecondary">@color/black</item>
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        <item name="android:windowBackground">@color/white</item>
        <item name="android:textColorPrimary">@color/black</item>
        <item name="colorSurface">@color/white</item>
        <item name="colorOnSurface">@color/black</item>

        
        <item name="cardBackgroundColor">@color/dark_blue_card_background_light</item>
        <item name="materialCardViewStyle">@style/DarkBlueCardStyle</item>

        
        <item name="android:popupMenuStyle">@style/PopupMenuStyle_DarkBlue</item>
        <item name="popupMenuStyle">@style/PopupMenuStyle_DarkBlue</item>
        <item name="android:itemTextAppearance">@style/PopupMenuItemStyle_DarkBlue</item>
    </style><style name="DarkBlueCardStyle" parent="Widget.MaterialComponents.CardView">
        <item name="cardBackgroundColor">@color/dark_blue_card_background_light</item>
        <item name="cardElevation">4dp</item>
        <item name="cardCornerRadius">8dp</item>
    </style><style name="Theme.DieSnookerApp.Ocean" parent="Theme.MaterialComponents.Light.NoActionBar">
        <item name="colorPrimary">@color/ocean_medium</item>
        <item name="colorPrimaryVariant">@color/ocean_dark</item>
        <item name="colorOnPrimary">@color/white</item>
        <item name="colorSecondary">@color/ocean_light</item>
        <item name="colorSecondaryVariant">@color/ocean_lightest</item>
        <item name="colorOnSecondary">@color/black</item>
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        <item name="android:windowBackground">@color/ocean_lightest</item>
        <item name="android:textColorPrimary">@color/black</item>
        <item name="colorSurface">@color/ocean_lightest</item>
        <item name="colorOnSurface">@color/black</item>

        
        <item name="cardBackgroundColor">@color/ocean_card_background_light</item>
        <item name="materialCardViewStyle">@style/OceanCardStyle</item>

        
        <item name="android:popupMenuStyle">@style/PopupMenuStyle_Ocean</item>
        <item name="popupMenuStyle">@style/PopupMenuStyle_Ocean</item>
        <item name="android:itemTextAppearance">@style/PopupMenuItemStyle</item>
    </style><style name="OceanCardStyle" parent="Widget.MaterialComponents.CardView">
        <item name="cardBackgroundColor">@color/ocean_card_background_light</item>
        <item name="cardElevation">4dp</item>
        <item name="cardCornerRadius">8dp</item>
    </style><style name="Theme.DieSnookerApp.Crimson" parent="Theme.MaterialComponents.Light.NoActionBar">
        <item name="colorPrimary">@color/crimson_medium</item>
        <item name="colorPrimaryVariant">@color/crimson_dark</item>
        <item name="colorOnPrimary">@color/white</item>
        <item name="colorSecondary">@color/crimson_light</item>
        <item name="colorSecondaryVariant">@color/crimson_lightest</item>
        <item name="colorOnSecondary">@color/white</item>
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        <item name="android:windowBackground">@color/crimson_card_background_light</item>
        <item name="android:textColorPrimary">@color/black</item>
        <item name="colorSurface">@color/crimson_card_background_light</item>
        <item name="colorOnSurface">@color/black</item>

        
        <item name="cardBackgroundColor">@color/crimson_card_background_light</item>
        <item name="materialCardViewStyle">@style/CrimsonCardStyle</item>

        
        <item name="android:popupMenuStyle">@style/PopupMenuStyle_Crimson</item>
        <item name="popupMenuStyle">@style/PopupMenuStyle_Crimson</item>
        <item name="android:itemTextAppearance">@style/PopupMenuItemStyle</item>
    </style><style name="CrimsonCardStyle" parent="Widget.MaterialComponents.CardView">
        <item name="cardBackgroundColor">@color/crimson_card_background_light</item>
        <item name="cardElevation">4dp</item>
        <item name="cardCornerRadius">8dp</item>
    </style><style name="Theme.DieSnookerApp.Neon" parent="Theme.MaterialComponents.Light.NoActionBar">
        <item name="colorPrimary">@color/neon_purple_3</item>
        <item name="colorPrimaryVariant">@color/neon_purple_1</item>
        <item name="colorOnPrimary">@color/white</item>
        <item name="colorSecondary">@color/neon_pink_2</item>
        <item name="colorSecondaryVariant">@color/neon_pink_3</item>
        <item name="colorOnSecondary">@color/white</item>
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        <item name="android:windowBackground">@color/neon_card_background_light</item>
        <item name="android:textColorPrimary">@color/black</item>
        <item name="colorSurface">@color/neon_card_background_light</item>
        <item name="colorOnSurface">@color/black</item>

        
        <item name="cardBackgroundColor">@color/neon_card_background_light</item>
        <item name="materialCardViewStyle">@style/NeonCardStyle</item>

        
        <item name="android:popupMenuStyle">@style/PopupMenuStyle_Neon</item>
        <item name="popupMenuStyle">@style/PopupMenuStyle_Neon</item>
        <item name="android:itemTextAppearance">@style/PopupMenuItemStyle</item>
    </style><style name="NeonCardStyle" parent="Widget.MaterialComponents.CardView">
        <item name="cardBackgroundColor">@color/neon_card_background_light</item>
        <item name="cardElevation">4dp</item>
        <item name="cardCornerRadius">8dp</item>
    </style></file><file path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\values\theme_styles.xml" qualifiers=""><style name="DropdownDialog.Ocean">
        <item name="android:windowBackground">@drawable/dropdown_background_ocean</item>
        <item name="android:textColor">@color/black</item>
        <item name="colorAccent">@color/ocean_medium</item>
        <item name="buttonBarPositiveButtonStyle">@style/OceanPositiveButtonStyle</item>
        <item name="buttonBarNegativeButtonStyle">@style/OceanNegativeButtonStyle</item>
    </style><style name="DefaultAnimationDialog.Ocean">
        <item name="android:windowBackground">@drawable/dropdown_background_ocean</item>
        <item name="android:textColor">@color/black</item>
        <item name="colorAccent">@color/ocean_medium</item>
        <item name="buttonBarPositiveButtonStyle">@style/OceanPositiveButtonStyle</item>
        <item name="buttonBarNegativeButtonStyle">@style/OceanNegativeButtonStyle</item>
    </style><style name="OceanPositiveButtonStyle" parent="Widget.MaterialComponents.Button.TextButton.Dialog">
        <item name="android:textColor">@color/ocean_medium</item>
    </style><style name="OceanNegativeButtonStyle" parent="Widget.MaterialComponents.Button.TextButton.Dialog">
        <item name="android:textColor">@color/ocean_lightest</item>
    </style><style name="CustomTextInputLayout.Ocean">
        <item name="boxStrokeColor">@color/ocean_medium</item>
        <item name="hintTextColor">@color/ocean_medium</item>
        <item name="boxBackgroundColor">@color/ocean_card_background_light</item>
    </style><style name="CustomDropdownTextInputLayout.Ocean">
        <item name="boxStrokeColor">@color/ocean_medium</item>
        <item name="hintTextColor">@color/ocean_medium</item>
        <item name="endIconTint">@color/ocean_medium</item>
        <item name="boxBackgroundColor">@color/ocean_card_background_light</item>
    </style><style name="CustomDropdownStyle.Ocean">
        <item name="android:background">@drawable/dropdown_background_solid_ocean</item>
        <item name="android:textColor">@color/black</item>
    </style><style name="DropdownDialog.Crimson">
        <item name="android:windowBackground">@drawable/dropdown_background_crimson</item>
        <item name="android:textColor">@color/black</item>
        <item name="colorAccent">@color/crimson_medium</item>
        <item name="buttonBarPositiveButtonStyle">@style/CrimsonPositiveButtonStyle</item>
        <item name="buttonBarNegativeButtonStyle">@style/CrimsonNegativeButtonStyle</item>
    </style><style name="DefaultAnimationDialog.Crimson">
        <item name="android:windowBackground">@drawable/dropdown_background_crimson</item>
        <item name="android:textColor">@color/black</item>
        <item name="colorAccent">@color/crimson_medium</item>
        <item name="buttonBarPositiveButtonStyle">@style/CrimsonPositiveButtonStyle</item>
        <item name="buttonBarNegativeButtonStyle">@style/CrimsonNegativeButtonStyle</item>
    </style><style name="CrimsonPositiveButtonStyle" parent="Widget.MaterialComponents.Button.TextButton.Dialog">
        <item name="android:textColor">@color/crimson_medium</item>
    </style><style name="CrimsonNegativeButtonStyle" parent="Widget.MaterialComponents.Button.TextButton.Dialog">
        <item name="android:textColor">@color/crimson_lightest</item>
    </style><style name="CustomTextInputLayout.Crimson">
        <item name="boxStrokeColor">@color/crimson_medium</item>
        <item name="hintTextColor">@color/crimson_medium</item>
        <item name="boxBackgroundColor">@color/crimson_card_background_light</item>
    </style><style name="CustomDropdownTextInputLayout.Crimson">
        <item name="boxStrokeColor">@color/crimson_medium</item>
        <item name="hintTextColor">@color/crimson_medium</item>
        <item name="endIconTint">@color/crimson_medium</item>
        <item name="boxBackgroundColor">@color/crimson_card_background_light</item>
    </style><style name="CustomDropdownStyle.Crimson">
        <item name="android:background">@drawable/dropdown_background_solid_crimson</item>
        <item name="android:textColor">@color/black</item>
    </style><style name="DropdownDialog.Neon">
        <item name="android:windowBackground">@drawable/dropdown_background_neon</item>
        <item name="android:textColor">@color/black</item>
        <item name="colorAccent">@color/neon_purple_3</item>
        <item name="buttonBarPositiveButtonStyle">@style/NeonPositiveButtonStyle</item>
        <item name="buttonBarNegativeButtonStyle">@style/NeonNegativeButtonStyle</item>
    </style><style name="DefaultAnimationDialog.Neon">
        <item name="android:windowBackground">@drawable/dropdown_background_neon</item>
        <item name="android:textColor">@color/black</item>
        <item name="colorAccent">@color/neon_purple_3</item>
        <item name="buttonBarPositiveButtonStyle">@style/NeonPositiveButtonStyle</item>
        <item name="buttonBarNegativeButtonStyle">@style/NeonNegativeButtonStyle</item>
    </style><style name="NeonPositiveButtonStyle" parent="Widget.MaterialComponents.Button.TextButton.Dialog">
        <item name="android:textColor">@color/neon_purple_3</item>
    </style><style name="NeonNegativeButtonStyle" parent="Widget.MaterialComponents.Button.TextButton.Dialog">
        <item name="android:textColor">@color/neon_pink_2</item>
    </style><style name="CustomTextInputLayout.Neon">
        <item name="boxStrokeColor">@color/neon_purple_3</item>
        <item name="hintTextColor">@color/neon_purple_3</item>
        <item name="boxBackgroundColor">@color/neon_card_background_light</item>
    </style><style name="CustomDropdownTextInputLayout.Neon">
        <item name="boxStrokeColor">@color/neon_purple_3</item>
        <item name="hintTextColor">@color/neon_purple_3</item>
        <item name="endIconTint">@color/neon_purple_3</item>
        <item name="boxBackgroundColor">@color/neon_card_background_light</item>
    </style><style name="CustomDropdownStyle.Neon">
        <item name="android:background">@drawable/dropdown_background_solid_neon</item>
        <item name="android:textColor">@color/black</item>
    </style></file><file path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\values-de\strings.xml" qualifiers="de"><string name="app_name">Die Snooker App</string><string name="save_button">Speichern</string><string name="cancel_button">Abbrechen</string><string name="edit_button">Bearbeiten</string><string name="delete_button">Löschen</string><string name="manage_my_exercises_button">Meine Übungen verwalten</string><string name="add_exercise_button_label">Übung hinzufügen</string><string name="dialog_title_add_exercise">Übung hinzufügen</string><string name="dialog_title_edit_exercise">Übung bearbeiten</string><string name="fragment_label_manage_exercises">Übungen verwalten</string><string name="dialog_title_confirmation">Bestätigung</string><string name="label_name">Name</string><string name="label_category">Kategorie</string><string name="label_description">Beschreibung</string><string name="toast_no_specific_view_for_category">Keine spezifische Ansicht für Kategorie: %s</string><string name="default_exercises_added_toast">Standardübungen wurden Ihrer Bibliothek hinzugefügt.</string><string name="exercise_saved_toast">Übung gespeichert.</string><string name="exercise_deleted_toast">Übung gelöscht.</string><string name="error_saving_exercise_toast">Fehler beim Speichern der Übung.</string><string name="error_deleting_exercise_toast">Fehler beim Löschen der Übung.</string><string name="error_loading_exercises_toast">Fehler beim Laden der Übungen.</string><string name="no_exercises_found_toast">Keine Übungen gefunden.</string><string name="please_log_in_to_manage_exercises_toast">Bitte anmelden, um Übungen zu verwalten.</string><string name="please_log_in_to_load_categories_toast">Bitte anmelden, um Kategorien zu laden.</string><string name="no_categories_found_toast">Keine Kategorien gefunden. Fügen Sie Übungen hinzu, um Kategorien zu erstellen.</string><string name="please_log_in_to_load_exercises_toast">Bitte anmelden, um Übungen zu laden.</string><string name="no_exercises_in_category_toast">Keine Übungen für %s gefunden.</string><string name="confirm_delete_exercise_message">Übung \'%s\' löschen?</string><string name="input_error_name_required">Name ist erforderlich.</string><string name="input_error_category_required">Kategorie ist erforderlich.</string><string name="label_new_category">Neue Kategorie</string><string name="yes">Ja</string><string name="no">Nein</string><string name="action_create_new_category">+ Neue Kategorie erstellen</string><string name="error_user_id_not_found">Fehler: Benutzer-ID nicht gefunden</string><string name="error_user_id_not_found_info">Benutzerinformationen konnten nicht abgerufen werden.</string><string name="default_exercises_partial_success_toast">Einige Standardübungen hinzugefügt, einige fehlgeschlagen.</string><string name="default_exercises_failed_toast">Standardübungen konnten nicht hinzugefügt werden.</string><string name="error_loading_categories_toast">Kategorien konnten nicht geladen werden.</string><string name="exercise_not_deletable_toast">ist nicht löschbar.</string><string name="dialog_title_delete_exercise">Übung löschen</string><string name="no_exercises_in_category_generic">Keine Übungen in dieser Kategorie gefunden.</string><string name="unknown_category">Unbekannte Kategorie</string><string name="category_not_specified_toast">Kategorie nicht angegeben.</string><string name="label_empty_exercise_list">Noch keine eigenen Übungen. Tippe auf \'+\', um eine hinzuzufügen.</string><string name="toast_please_select_exercise">Bitte wähle mindestens eine Übung aus.</string><string name="confirm_remove_exercise_from_plan_message">Übung \'%s\' wirklich aus dem Trainingsplan entfernen?</string><string name="debug_menu_title">Debug-Menü</string><string-array name="debug_menu_options">
        <item>Testbenachrichtigung (Normal)</item>
        <item>Benachrichtigung erzwingen (Testübung hinzufügen)</item>
        <item>Wöchentliche Erinnerung neu planen</item>
    </string-array><string name="toast_plan_reset_archived">Trainingsplan wurde zurückgesetzt und archiviert.</string><string name="toast_notification_sent_success">Benachrichtigung erfolgreich gesendet!</string><string name="toast_notification_sent_none">Keine Benachrichtigung gesendet - keine unerledigten Übungen.</string><string name="toast_notification_forced">Erzwungene Benachrichtigung mit Testübung.</string><string name="toast_reminder_rescheduled">Wöchentliche Erinnerung neu geplant.</string><string name="title_category_exercises_generic">Kategorie Übungen</string><string name="label_exercise_type">Übungstyp</string><string name="exercise_type_timeonly">Nur Zeit</string><string name="exercise_type_normal">Normal</string><string name="exercise_type_splits">Splits</string><string name="exercise_type_stellungsspiel">Stellungsspiel</string></file><file path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\values-night\default_dialog_styles.xml" qualifiers="night-v8"><style name="DefaultAnimationDialog.Snooker">
        <item name="android:windowBackground">@drawable/dropdown_background_snooker_dark</item>
        <item name="android:textColor">@color/white</item>
        <item name="colorAccent">@color/snooker_red_200</item>
        <item name="buttonBarPositiveButtonStyle">@style/SnookerPositiveButtonStyle.Night</item>
        <item name="buttonBarNegativeButtonStyle">@style/SnookerNegativeButtonStyle.Night</item>
    </style><style name="DefaultAnimationDialog.Blue">
        <item name="android:windowBackground">@drawable/dropdown_background_blue_dark</item>
        <item name="android:textColor">@color/white</item>
        <item name="colorAccent">@color/blue_200</item>
        <item name="buttonBarPositiveButtonStyle">@style/BluePositiveButtonStyle.Night</item>
        <item name="buttonBarNegativeButtonStyle">@style/BlueNegativeButtonStyle.Night</item>
    </style><style name="DefaultAnimationDialog.DarkBlue">
        <item name="android:windowBackground">@drawable/dropdown_background_dark_blue_dark</item>
        <item name="android:textColor">@color/white</item>
        <item name="colorAccent">@color/dark_blue_200</item>
        <item name="buttonBarPositiveButtonStyle">@style/DarkBluePositiveButtonStyle.Night</item>
        <item name="buttonBarNegativeButtonStyle">@style/DarkBlueNegativeButtonStyle.Night</item>
    </style><style name="DefaultAnimationDialog.Dark">
        <item name="android:windowBackground">@drawable/dropdown_background_dark</item>
        <item name="android:textColor">@color/white</item>
        <item name="colorAccent">@color/purple_200</item>
        <item name="buttonBarPositiveButtonStyle">@style/DarkPositiveButtonStyle</item>
        <item name="buttonBarNegativeButtonStyle">@style/DarkNegativeButtonStyle</item>
    </style></file><file path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\values-night\popup_menu_item_styles.xml" qualifiers="night-v8"><style name="PopupMenuItemStyle" parent="TextAppearance.AppCompat.Widget.PopupMenu.Small">
        <item name="android:textColor">@color/white</item>
    </style><style name="PopupMenuItemStyle_Snooker" parent="PopupMenuItemStyle">
        <item name="android:textColor">@color/white</item>
        <item name="android:background">@drawable/dropdown_background_snooker_dark</item>
    </style><style name="PopupMenuItemStyle_Blue" parent="PopupMenuItemStyle">
        <item name="android:textColor">@color/white</item>
        <item name="android:background">@drawable/dropdown_background_blue_dark</item>
    </style><style name="PopupMenuItemStyle_DarkBlue" parent="PopupMenuItemStyle">
        <item name="android:textColor">@color/white</item>
        <item name="android:background">@drawable/dropdown_background_dark_blue_dark</item>
    </style><style name="PopupMenuItemStyle_Dark" parent="PopupMenuItemStyle">
        <item name="android:textColor">@color/white</item>
        <item name="android:background">@drawable/dropdown_background_dark</item>
    </style></file><file path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\values-night\popup_styles.xml" qualifiers="night-v8"><style name="PopupMenuStyle" parent="Widget.AppCompat.PopupMenu">
        <item name="android:popupBackground">@drawable/dropdown_background_dark</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:itemBackground">@drawable/dropdown_background_dark</item>
        <item name="android:dropDownListViewStyle">@style/PopupMenuListViewStyle</item>
    </style><style name="PopupMenuStyle_Snooker" parent="PopupMenuStyle">
        <item name="android:popupBackground">@drawable/dropdown_background_snooker_dark</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:itemBackground">@drawable/dropdown_background_snooker_dark</item>
        <item name="android:dropDownListViewStyle">@style/PopupMenuListViewStyle_Snooker</item>
    </style><style name="PopupMenuStyle_Blue" parent="PopupMenuStyle">
        <item name="android:popupBackground">@drawable/dropdown_background_blue_dark</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:itemBackground">@drawable/dropdown_background_blue_dark</item>
        <item name="android:dropDownListViewStyle">@style/PopupMenuListViewStyle_Blue</item>
    </style><style name="PopupMenuStyle_DarkBlue" parent="PopupMenuStyle">
        <item name="android:popupBackground">@drawable/dropdown_background_dark_blue_dark</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:itemBackground">@drawable/dropdown_background_dark_blue_dark</item>
        <item name="android:dropDownListViewStyle">@style/PopupMenuListViewStyle_DarkBlue</item>
    </style><style name="PopupMenuStyle_Dark" parent="PopupMenuStyle">
        <item name="android:popupBackground">@drawable/dropdown_background_dark</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:itemBackground">@drawable/dropdown_background_dark</item>
        <item name="android:dropDownListViewStyle">@style/PopupMenuListViewStyle_Dark</item>
    </style><style name="PopupMenuListViewStyle" parent="Widget.AppCompat.ListView.DropDown">
        <item name="android:divider">@android:color/transparent</item>
        <item name="android:dividerHeight">0dp</item>
        <item name="android:background">@drawable/dropdown_background_dark</item>
    </style><style name="PopupMenuListViewStyle_Snooker" parent="PopupMenuListViewStyle">
        <item name="android:background">@drawable/dropdown_background_snooker_dark</item>
    </style><style name="PopupMenuListViewStyle_Blue" parent="PopupMenuListViewStyle">
        <item name="android:background">@drawable/dropdown_background_blue_dark</item>
    </style><style name="PopupMenuListViewStyle_DarkBlue" parent="PopupMenuListViewStyle">
        <item name="android:background">@drawable/dropdown_background_dark_blue_dark</item>
    </style><style name="PopupMenuListViewStyle_Dark" parent="PopupMenuListViewStyle">
        <item name="android:background">@drawable/dropdown_background_dark</item>
    </style><style name="DatePickerDialogTheme" parent="ThemeOverlay.MaterialComponents.Dialog">
        <item name="colorAccent">?attr/colorPrimary</item>
        <item name="android:buttonBarPositiveButtonStyle">@style/DatePickerButtonStyle</item>
        <item name="android:buttonBarNegativeButtonStyle">@style/DatePickerButtonStyle</item>
    </style></file><file path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\values-night\styles.xml" qualifiers="night-v8"><style name="DropdownDialog.Snooker">
        <item name="android:windowBackground">@drawable/dropdown_background_snooker_dark</item>
        <item name="android:textColor">@color/white</item>
        <item name="colorAccent">@color/snooker_red_200</item>
        <item name="buttonBarPositiveButtonStyle">@style/SnookerPositiveButtonStyle.Night</item>
        <item name="buttonBarNegativeButtonStyle">@style/SnookerNegativeButtonStyle.Night</item>
    </style><style name="SnookerPositiveButtonStyle.Night">
        <item name="android:textColor">@color/snooker_red_200</item>
    </style><style name="SnookerNegativeButtonStyle.Night">
        <item name="android:textColor">@color/snooker_green_200</item>
    </style><style name="DropdownDialog.Blue">
        <item name="android:windowBackground">@drawable/dropdown_background_blue_dark</item>
        <item name="android:textColor">@color/white</item>
        <item name="colorAccent">@color/blue_200</item>
        <item name="buttonBarPositiveButtonStyle">@style/BluePositiveButtonStyle.Night</item>
        <item name="buttonBarNegativeButtonStyle">@style/BlueNegativeButtonStyle.Night</item>
    </style><style name="BluePositiveButtonStyle.Night">
        <item name="android:textColor">@color/blue_200</item>
    </style><style name="BlueNegativeButtonStyle.Night">
        <item name="android:textColor">@color/orange_200</item>
    </style><style name="DropdownDialog.DarkBlue">
        <item name="android:windowBackground">@drawable/dropdown_background_dark_blue_dark</item>
        <item name="android:textColor">@color/white</item>
        <item name="colorAccent">@color/dark_blue_200</item>
        <item name="buttonBarPositiveButtonStyle">@style/DarkBluePositiveButtonStyle.Night</item>
        <item name="buttonBarNegativeButtonStyle">@style/DarkBlueNegativeButtonStyle.Night</item>
    </style><style name="DarkBluePositiveButtonStyle.Night">
        <item name="android:textColor">@color/dark_blue_200</item>
    </style><style name="DarkBlueNegativeButtonStyle.Night">
        <item name="android:textColor">@color/cyan_200</item>
    </style></file><file path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\values-night\tab_styles.xml" qualifiers="night-v8"><style name="CustomTabLayout" parent="Widget.MaterialComponents.TabLayout">
        <item name="tabBackground">@color/dark_gray</item>
        <item name="tabIndicatorColor">@color/purple_200</item>
        <item name="tabSelectedTextColor">@color/purple_200</item>
        <item name="tabTextColor">@color/white</item>
    </style><style name="CustomTabLayout.Snooker">
        <item name="tabBackground">@color/snooker_card_background_dark</item>
        <item name="tabIndicatorColor">@color/snooker_red_200</item>
        <item name="tabSelectedTextColor">@color/snooker_red_200</item>
        <item name="tabTextColor">@color/white</item>
    </style><style name="CustomTabLayout.Blue">
        <item name="tabBackground">@color/blue_card_background_dark</item>
        <item name="tabIndicatorColor">@color/blue_200</item>
        <item name="tabSelectedTextColor">@color/blue_200</item>
        <item name="tabTextColor">@color/white</item>
    </style><style name="CustomTabLayout.DarkBlue">
        <item name="tabBackground">@color/dark_blue_card_background_dark</item>
        <item name="tabIndicatorColor">@color/dark_blue_200</item>
        <item name="tabSelectedTextColor">@color/dark_blue_200</item>
        <item name="tabTextColor">@color/white</item>
    </style><style name="CustomTabLayout.Dark">
        <item name="tabBackground">@color/dark_gray</item>
        <item name="tabIndicatorColor">@color/purple_200</item>
        <item name="tabSelectedTextColor">@color/purple_200</item>
        <item name="tabTextColor">@color/white</item>
    </style><style name="CustomTabLayout.Ocean">
        <item name="tabBackground">@color/ocean_card_background_dark</item>
        <item name="tabIndicatorColor">@color/ocean_lightest</item>
        <item name="tabSelectedTextColor">@color/ocean_lightest</item>
        <item name="tabTextColor">@color/white</item>
    </style><style name="CustomTabLayout.Crimson">
        <item name="tabBackground">@color/crimson_card_background_dark</item>
        <item name="tabIndicatorColor">@color/crimson_lightest</item>
        <item name="tabSelectedTextColor">@color/crimson_lightest</item>
        <item name="tabTextColor">@color/white</item>
    </style><style name="CustomTabLayout.Neon">
        <item name="tabBackground">@color/neon_card_background_dark</item>
        <item name="tabIndicatorColor">@color/neon_purple_2</item>
        <item name="tabSelectedTextColor">@color/neon_purple_2</item>
        <item name="tabTextColor">@color/white</item>
    </style></file><file path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\values-night\text_input_styles.xml" qualifiers="night-v8"><style name="CustomTextInputLayout" parent="Widget.MaterialComponents.TextInputLayout.OutlinedBox">
        <item name="boxStrokeColor">?attr/colorPrimary</item>
        <item name="hintTextColor">?attr/colorPrimary</item>
        <item name="android:textColorHint">@color/white</item>
    </style><style name="CustomTextInputLayout.Snooker">
        <item name="boxStrokeColor">@color/snooker_red_200</item>
        <item name="hintTextColor">@color/snooker_red_200</item>
        <item name="boxBackgroundColor">@color/snooker_card_background_dark</item>
        <item name="android:textColorHint">@color/white</item>
    </style><style name="CustomTextInputLayout.Blue">
        <item name="boxStrokeColor">@color/blue_200</item>
        <item name="hintTextColor">@color/blue_200</item>
        <item name="boxBackgroundColor">@color/blue_card_background_dark</item>
        <item name="android:textColorHint">@color/white</item>
    </style><style name="CustomTextInputLayout.DarkBlue">
        <item name="boxStrokeColor">@color/dark_blue_200</item>
        <item name="hintTextColor">@color/dark_blue_200</item>
        <item name="boxBackgroundColor">@color/dark_blue_card_background_dark</item>
        <item name="android:textColorHint">@color/white</item>
    </style><style name="CustomTextInputLayout.Dark">
        <item name="boxStrokeColor">@color/purple_200</item>
        <item name="hintTextColor">@color/purple_200</item>
        <item name="boxBackgroundColor">@color/dark_gray</item>
        <item name="android:textColorHint">@color/white</item>
    </style><style name="CustomDropdownTextInputLayout" parent="Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu">
        <item name="boxStrokeColor">?attr/colorPrimary</item>
        <item name="hintTextColor">?attr/colorPrimary</item>
        <item name="android:textColorHint">@color/white</item>
    </style><style name="CustomDropdownTextInputLayout.Snooker">
        <item name="boxStrokeColor">@color/snooker_red_200</item>
        <item name="hintTextColor">@color/snooker_red_200</item>
        <item name="boxBackgroundColor">@color/snooker_card_background_dark</item>
        <item name="endIconTint">@color/snooker_red_200</item>
        <item name="android:textColorHint">@color/white</item>
    </style><style name="CustomDropdownTextInputLayout.Blue">
        <item name="boxStrokeColor">@color/blue_200</item>
        <item name="hintTextColor">@color/blue_200</item>
        <item name="boxBackgroundColor">@color/blue_card_background_dark</item>
        <item name="endIconTint">@color/blue_200</item>
        <item name="android:textColorHint">@color/white</item>
    </style><style name="CustomDropdownTextInputLayout.DarkBlue">
        <item name="boxStrokeColor">@color/dark_blue_200</item>
        <item name="hintTextColor">@color/dark_blue_200</item>
        <item name="boxBackgroundColor">@color/dark_blue_card_background_dark</item>
        <item name="endIconTint">@color/dark_blue_200</item>
        <item name="android:textColorHint">@color/white</item>
    </style><style name="CustomDropdownTextInputLayout.Dark">
        <item name="boxStrokeColor">@color/purple_200</item>
        <item name="hintTextColor">@color/purple_200</item>
        <item name="boxBackgroundColor">@color/dark_gray</item>
        <item name="endIconTint">@color/purple_200</item>
        <item name="android:textColorHint">@color/white</item>
    </style><style name="CustomDropdownTextInputLayout.Ocean">
        <item name="boxStrokeColor">@color/ocean_lightest</item>
        <item name="hintTextColor">@color/ocean_lightest</item>
        <item name="boxBackgroundColor">@color/ocean_card_background_dark</item>
        <item name="endIconTint">@color/ocean_lightest</item>
        <item name="android:textColorHint">@color/white</item>
    </style><style name="CustomDropdownTextInputLayout.Crimson">
        <item name="boxStrokeColor">@color/crimson_lightest</item>
        <item name="hintTextColor">@color/crimson_lightest</item>
        <item name="boxBackgroundColor">@color/crimson_card_background_dark</item>
        <item name="endIconTint">@color/crimson_lightest</item>
        <item name="android:textColorHint">@color/white</item>
    </style><style name="CustomTextInputLayout.Neon">
        <item name="boxStrokeColor">@color/neon_purple_2</item>
        <item name="hintTextColor">@color/neon_purple_2</item>
        <item name="boxBackgroundColor">@color/neon_card_background_dark</item>
        <item name="android:textColorHint">@color/white</item>
    </style><style name="CustomTextInputLayout.Ocean">
        <item name="boxStrokeColor">@color/ocean_lightest</item>
        <item name="hintTextColor">@color/ocean_lightest</item>
        <item name="boxBackgroundColor">@color/ocean_card_background_dark</item>
        <item name="android:textColorHint">@color/white</item>
    </style><style name="CustomTextInputLayout.Crimson">
        <item name="boxStrokeColor">@color/crimson_lightest</item>
        <item name="hintTextColor">@color/crimson_lightest</item>
        <item name="boxBackgroundColor">@color/crimson_card_background_dark</item>
        <item name="android:textColorHint">@color/white</item>
    </style><style name="CustomDropdownTextInputLayout.Neon">
        <item name="boxStrokeColor">@color/neon_purple_2</item>
        <item name="hintTextColor">@color/neon_purple_2</item>
        <item name="boxBackgroundColor">@color/neon_card_background_dark</item>
        <item name="endIconTint">@color/neon_purple_2</item>
        <item name="android:textColorHint">@color/white</item>
    </style></file><file path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\values-night\themes.xml" qualifiers="night-v8"><style name="Theme.DieSnookerApp" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        <item name="colorPrimary">@color/purple_200</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/black</item>
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_200</item>
        <item name="colorOnSecondary">@color/black</item>
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>

        
        <item name="android:popupMenuStyle">@style/PopupMenuStyle</item>
        <item name="popupMenuStyle">@style/PopupMenuStyle</item>
        <item name="android:itemTextAppearance">@style/PopupMenuItemStyle</item>
    </style><style name="Theme.DieSnookerApp.Snooker" parent="Theme.MaterialComponents.NoActionBar">
        <item name="colorPrimary">@color/snooker_red_200</item>
        <item name="colorPrimaryVariant">@color/snooker_red_500</item>
        <item name="colorOnPrimary">@color/black</item>
        <item name="colorSecondary">@color/snooker_green_200</item>
        <item name="colorSecondaryVariant">@color/snooker_green_500</item>
        <item name="colorOnSecondary">@color/black</item>
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        <item name="android:windowBackground">@color/snooker_green_700</item>
        <item name="android:textColorPrimary">@color/white</item>
        <item name="colorSurface">@color/snooker_green_700</item>
        <item name="colorOnSurface">@color/white</item>

        
        <item name="cardBackgroundColor">@color/snooker_card_background_dark</item>
        <item name="materialCardViewStyle">@style/SnookerCardStyleDark</item>

        
        <item name="android:popupMenuStyle">@style/PopupMenuStyle_Snooker</item>
        <item name="popupMenuStyle">@style/PopupMenuStyle_Snooker</item>
        <item name="android:itemTextAppearance">@style/PopupMenuItemStyle_Snooker</item>
    </style><style name="SnookerCardStyleDark" parent="Widget.MaterialComponents.CardView">
        <item name="cardBackgroundColor">@color/snooker_card_background_dark</item>
        <item name="cardElevation">4dp</item>
        <item name="cardCornerRadius">8dp</item>
        <item name="android:textColor">@color/white</item>
    </style><style name="Theme.DieSnookerApp.Blue" parent="Theme.MaterialComponents.NoActionBar">
        <item name="colorPrimary">@color/blue_200</item>
        <item name="colorPrimaryVariant">@color/blue_500</item>
        <item name="colorOnPrimary">@color/black</item>
        <item name="colorSecondary">@color/orange_200</item>
        <item name="colorSecondaryVariant">@color/orange_500</item>
        <item name="colorOnSecondary">@color/black</item>
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        <item name="android:windowBackground">@color/black</item>
        <item name="android:textColorPrimary">@color/white</item>
        <item name="colorSurface">@color/black</item>
        <item name="colorOnSurface">@color/white</item>

        
        <item name="cardBackgroundColor">@color/blue_card_background_dark</item>
        <item name="materialCardViewStyle">@style/BlueCardStyleDark</item>

        
        <item name="android:popupMenuStyle">@style/PopupMenuStyle_Blue</item>
        <item name="popupMenuStyle">@style/PopupMenuStyle_Blue</item>
        <item name="android:itemTextAppearance">@style/PopupMenuItemStyle_Blue</item>
    </style><style name="BlueCardStyleDark" parent="Widget.MaterialComponents.CardView">
        <item name="cardBackgroundColor">@color/blue_card_background_dark</item>
        <item name="cardElevation">4dp</item>
        <item name="cardCornerRadius">8dp</item>
        <item name="android:textColor">@color/white</item>
    </style><style name="Theme.DieSnookerApp.DarkBlue" parent="Theme.MaterialComponents.NoActionBar">
        <item name="colorPrimary">@color/dark_blue_200</item>
        <item name="colorPrimaryVariant">@color/dark_blue_500</item>
        <item name="colorOnPrimary">@color/black</item>
        <item name="colorSecondary">@color/cyan_200</item>
        <item name="colorSecondaryVariant">@color/cyan_500</item>
        <item name="colorOnSecondary">@color/black</item>
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        <item name="android:windowBackground">@color/black</item>
        <item name="android:textColorPrimary">@color/white</item>
        <item name="colorSurface">@color/black</item>
        <item name="colorOnSurface">@color/white</item>

        
        <item name="cardBackgroundColor">@color/dark_blue_card_background_dark</item>
        <item name="materialCardViewStyle">@style/DarkBlueCardStyleDark</item>

        
        <item name="android:popupMenuStyle">@style/PopupMenuStyle_DarkBlue</item>
        <item name="popupMenuStyle">@style/PopupMenuStyle_DarkBlue</item>
        <item name="android:itemTextAppearance">@style/PopupMenuItemStyle_DarkBlue</item>
    </style><style name="DarkBlueCardStyleDark" parent="Widget.MaterialComponents.CardView">
        <item name="cardBackgroundColor">@color/dark_blue_card_background_dark</item>
        <item name="cardElevation">4dp</item>
        <item name="cardCornerRadius">8dp</item>
        <item name="android:textColor">@color/white</item>
    </style><style name="Theme.DieSnookerApp.Ocean" parent="Theme.MaterialComponents.NoActionBar">
        <item name="colorPrimary">@color/ocean_lightest</item>
        <item name="colorPrimaryVariant">@color/ocean_light</item>
        <item name="colorOnPrimary">@color/black</item>
        <item name="colorSecondary">@color/ocean_medium</item>
        <item name="colorSecondaryVariant">@color/ocean_dark</item>
        <item name="colorOnSecondary">@color/white</item>
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        <item name="android:windowBackground">@color/ocean_dark</item>
        <item name="android:textColorPrimary">@color/white</item>
        <item name="colorSurface">@color/ocean_dark</item>
        <item name="colorOnSurface">@color/white</item>

        
        <item name="cardBackgroundColor">@color/ocean_card_background_dark</item>
        <item name="materialCardViewStyle">@style/OceanCardStyleDark</item>

        
        <item name="android:popupMenuStyle">@style/PopupMenuStyle_Ocean</item>
        <item name="popupMenuStyle">@style/PopupMenuStyle_Ocean</item>
        <item name="android:itemTextAppearance">@style/PopupMenuItemStyle</item>
    </style><style name="OceanCardStyleDark" parent="Widget.MaterialComponents.CardView">
        <item name="cardBackgroundColor">@color/ocean_card_background_dark</item>
        <item name="cardElevation">4dp</item>
        <item name="cardCornerRadius">8dp</item>
        <item name="android:textColor">@color/white</item>
    </style><style name="Theme.DieSnookerApp.Crimson" parent="Theme.MaterialComponents.NoActionBar">
        <item name="colorPrimary">@color/crimson_lightest</item>
        <item name="colorPrimaryVariant">@color/crimson_light</item>
        <item name="colorOnPrimary">@color/black</item>
        <item name="colorSecondary">@color/crimson_medium</item>
        <item name="colorSecondaryVariant">@color/crimson_dark</item>
        <item name="colorOnSecondary">@color/white</item>
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        <item name="android:windowBackground">@color/crimson_dark</item>
        <item name="android:textColorPrimary">@color/white</item>
        <item name="colorSurface">@color/crimson_dark</item>
        <item name="colorOnSurface">@color/white</item>

        
        <item name="cardBackgroundColor">@color/crimson_card_background_dark</item>
        <item name="materialCardViewStyle">@style/CrimsonCardStyleDark</item>

        
        <item name="android:popupMenuStyle">@style/PopupMenuStyle_Crimson</item>
        <item name="popupMenuStyle">@style/PopupMenuStyle_Crimson</item>
        <item name="android:itemTextAppearance">@style/PopupMenuItemStyle</item>
    </style><style name="CrimsonCardStyleDark" parent="Widget.MaterialComponents.CardView">
        <item name="cardBackgroundColor">@color/crimson_card_background_dark</item>
        <item name="cardElevation">4dp</item>
        <item name="cardCornerRadius">8dp</item>
        <item name="android:textColor">@color/white</item>
    </style><style name="Theme.DieSnookerApp.Neon" parent="Theme.MaterialComponents.NoActionBar">
        <item name="colorPrimary">@color/neon_purple_2</item>
        <item name="colorPrimaryVariant">@color/neon_purple_1</item>
        <item name="colorOnPrimary">@color/black</item>
        <item name="colorSecondary">@color/neon_pink_1</item>
        <item name="colorSecondaryVariant">@color/neon_pink_3</item>
        <item name="colorOnSecondary">@color/white</item>
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        <item name="android:windowBackground">@color/neon_purple_1</item>
        <item name="android:textColorPrimary">@color/white</item>
        <item name="colorSurface">@color/neon_purple_1</item>
        <item name="colorOnSurface">@color/white</item>

        
        <item name="cardBackgroundColor">@color/neon_card_background_dark</item>
        <item name="materialCardViewStyle">@style/NeonCardStyleDark</item>

        
        <item name="android:popupMenuStyle">@style/PopupMenuStyle_Neon</item>
        <item name="popupMenuStyle">@style/PopupMenuStyle_Neon</item>
        <item name="android:itemTextAppearance">@style/PopupMenuItemStyle</item>
    </style><style name="NeonCardStyleDark" parent="Widget.MaterialComponents.CardView">
        <item name="cardBackgroundColor">@color/neon_card_background_dark</item>
        <item name="cardElevation">4dp</item>
        <item name="cardCornerRadius">8dp</item>
        <item name="android:textColor">@color/white</item>
    </style></file><file path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\values-night\theme_styles.xml" qualifiers="night-v8"><style name="DropdownDialog.Ocean">
        <item name="android:windowBackground">@drawable/dropdown_background_ocean_dark</item>
        <item name="android:textColor">@color/white</item>
        <item name="colorAccent">@color/ocean_lightest</item>
        <item name="buttonBarPositiveButtonStyle">@style/OceanPositiveButtonStyle.Night</item>
        <item name="buttonBarNegativeButtonStyle">@style/OceanNegativeButtonStyle.Night</item>
    </style><style name="DefaultAnimationDialog.Ocean">
        <item name="android:windowBackground">@drawable/dropdown_background_ocean_dark</item>
        <item name="android:textColor">@color/white</item>
        <item name="colorAccent">@color/ocean_lightest</item>
        <item name="buttonBarPositiveButtonStyle">@style/OceanPositiveButtonStyle.Night</item>
        <item name="buttonBarNegativeButtonStyle">@style/OceanNegativeButtonStyle.Night</item>
    </style><style name="OceanPositiveButtonStyle.Night">
        <item name="android:textColor">@color/ocean_lightest</item>
    </style><style name="OceanNegativeButtonStyle.Night">
        <item name="android:textColor">@color/ocean_light</item>
    </style><style name="DropdownDialog.Crimson">
        <item name="android:windowBackground">@drawable/dropdown_background_crimson_dark</item>
        <item name="android:textColor">@color/white</item>
        <item name="colorAccent">@color/crimson_lightest</item>
        <item name="buttonBarPositiveButtonStyle">@style/CrimsonPositiveButtonStyle.Night</item>
        <item name="buttonBarNegativeButtonStyle">@style/CrimsonNegativeButtonStyle.Night</item>
    </style><style name="DefaultAnimationDialog.Crimson">
        <item name="android:windowBackground">@drawable/dropdown_background_crimson_dark</item>
        <item name="android:textColor">@color/white</item>
        <item name="colorAccent">@color/crimson_lightest</item>
        <item name="buttonBarPositiveButtonStyle">@style/CrimsonPositiveButtonStyle.Night</item>
        <item name="buttonBarNegativeButtonStyle">@style/CrimsonNegativeButtonStyle.Night</item>
    </style><style name="CrimsonPositiveButtonStyle.Night">
        <item name="android:textColor">@color/crimson_lightest</item>
    </style><style name="CrimsonNegativeButtonStyle.Night">
        <item name="android:textColor">@color/crimson_light</item>
    </style><style name="DropdownDialog.Neon">
        <item name="android:windowBackground">@drawable/dropdown_background_neon_dark</item>
        <item name="android:textColor">@color/white</item>
        <item name="colorAccent">@color/neon_purple_2</item>
        <item name="buttonBarPositiveButtonStyle">@style/NeonPositiveButtonStyle.Night</item>
        <item name="buttonBarNegativeButtonStyle">@style/NeonNegativeButtonStyle.Night</item>
    </style><style name="DefaultAnimationDialog.Neon">
        <item name="android:windowBackground">@drawable/dropdown_background_neon_dark</item>
        <item name="android:textColor">@color/white</item>
        <item name="colorAccent">@color/neon_purple_2</item>
        <item name="buttonBarPositiveButtonStyle">@style/NeonPositiveButtonStyle.Night</item>
        <item name="buttonBarNegativeButtonStyle">@style/NeonNegativeButtonStyle.Night</item>
    </style><style name="NeonPositiveButtonStyle.Night">
        <item name="android:textColor">@color/neon_purple_2</item>
    </style><style name="NeonNegativeButtonStyle.Night">
        <item name="android:textColor">@color/neon_pink_1</item>
    </style></file><file name="backup_rules" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\build\generated\res\resValues\debug"/><source path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\build\generated\res\processDebugGoogleServices"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\build\generated\res\resValues\debug"/><source path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\build\generated\res\processDebugGoogleServices"><file path="D:\Home\Dokumente\AndroidStudioProjects\DieSnookerApp - Jules\app\build\generated\res\processDebugGoogleServices\values\values.xml" qualifiers=""><string name="gcm_defaultSenderId" translatable="false">547283642216</string><string name="google_api_key" translatable="false">AIzaSyBYUMN3Xe-YUqM2rWka7TPakht8YpKWyPI</string><string name="google_app_id" translatable="false">1:547283642216:android:f0d4e7958787128030d8dd</string><string name="google_crash_reporting_api_key" translatable="false">AIzaSyBYUMN3Xe-YUqM2rWka7TPakht8YpKWyPI</string><string name="google_storage_bucket" translatable="false">die-snooker-app.firebasestorage.app</string><string name="project_id" translatable="false">die-snooker-app</string></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="res-processDebugGoogleServices$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="res-processDebugGoogleServices" generated-set="res-processDebugGoogleServices$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><mergedItems/></merger>