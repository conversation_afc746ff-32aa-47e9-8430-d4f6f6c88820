package com.atom.diesnookerapp.ui.trainingsplan

import android.app.AlertDialog
import android.content.res.ColorStateList
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ArrayAdapter
import android.widget.AutoCompleteTextView
import android.widget.LinearLayout
import android.widget.TextView
import android.widget.Toast
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.atom.diesnookerapp.R
import kotlinx.coroutines.launch
import com.google.android.material.button.MaterialButton
import com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton
import com.google.android.material.floatingactionbutton.FloatingActionButton
import com.google.android.material.tabs.TabLayout
import com.google.android.material.textfield.TextInputEditText
import com.google.android.material.textfield.TextInputLayout
import com.atom.diesnookerapp.ui.settings.ThemeHelper
import com.atom.diesnookerapp.ui.settings.ThemePreferences
import com.atom.diesnookerapp.ui.utils.ThemedArrayAdapter
import com.atom.diesnookerapp.ui.utils.setStyle
import java.util.UUID

class TrainingsplanFragment : Fragment() {
    private lateinit var trainingsplanManager: TrainingsplanManager
    private lateinit var trainingsplanAdapter: TrainingsplanAdapter
    private lateinit var trainingsplanResetService: TrainingsplanResetService
    private lateinit var recyclerView: RecyclerView
    private lateinit var emptyStateText: TextView

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_trainingsplan, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        trainingsplanManager = TrainingsplanManager(requireContext())
        trainingsplanResetService = TrainingsplanResetService(requireContext())

        // Check if the training plan needs to be reset
        val wasReset = trainingsplanResetService.checkAndResetTrainingsplan()
        if (wasReset) {
            Toast.makeText(
                requireContext(),
                getString(R.string.toast_plan_reset_archived),
                Toast.LENGTH_SHORT
            ).show()
        }

        // Initialize views
        recyclerView = view.findViewById(R.id.trainingsplanRecyclerView)
        emptyStateText = view.findViewById(R.id.emptyStateText)

        // Set up RecyclerView
        recyclerView.layoutManager = LinearLayoutManager(requireContext())

        trainingsplanAdapter = TrainingsplanAdapter(
            emptyList(),
            onItemCheckedChange = { item, isChecked ->
                trainingsplanManager.toggleItemChecked(item.id, isChecked)
            },
            onItemDelete = { item ->
                showDeleteConfirmationDialog(item)
            }
        )

        recyclerView.adapter = trainingsplanAdapter

        // Set up buttons
        view.findViewById<FloatingActionButton>(R.id.addExerciseButton).setOnClickListener {
            showAddExerciseDialog()
        }

        // Set up history button with normal click and long press for debug
        val historyButton = view.findViewById<ExtendedFloatingActionButton>(R.id.historyButton)
        historyButton.setOnClickListener {
            findNavController().navigate(R.id.action_navigation_trainingsplan_to_trainingsplanHistoryFragment)
        }

        // Add long press listener for debug functionality (5 seconds)
        historyButton.setOnLongClickListener {
            // Use a handler to detect if the button is held for 5 seconds
            val handler = android.os.Handler(android.os.Looper.getMainLooper())
            var longPressActive = true

            // Schedule the debug menu to appear after 5 seconds
            handler.postDelayed({
                if (longPressActive) {
                    showDebugMenu()
                }
            }, 5000) // 5 seconds

            // Set up a touch listener to cancel if released before 5 seconds
            historyButton.setOnTouchListener { _, event ->
                if (event.action == android.view.MotionEvent.ACTION_UP) {
                    longPressActive = false
                    historyButton.setOnTouchListener(null)
                    return@setOnTouchListener false
                }
                return@setOnTouchListener false
            }

            return@setOnLongClickListener true
        }

        // Hide the debug button but keep it in the layout for reference
        view.findViewById<MaterialButton>(R.id.debugButton).visibility = View.GONE

        // Set up Manage Exercises button
        view.findViewById<MaterialButton>(R.id.manageExercisesButton).setOnClickListener {
            findNavController().navigate(R.id.action_navigation_trainingsplan_to_manage_exercises)
        }

        // Update UI with current data
        updateUI()
    }

    override fun onResume() {
        super.onResume()
        // Check for reset on resume as well, in case the app was in background over Monday
        trainingsplanResetService.checkAndResetTrainingsplan()
        updateUI()
    }

    private fun updateUI() {
        val currentPlan = trainingsplanManager.getCurrentTrainingsplan()

        // Make sure we're working with the latest data
        val updatedItems = currentPlan.items.map { item ->
            // Ensure isChecked is consistent with completionCount
            item.isChecked = item.completionCount >= item.targetCount
            item
        }

        // Update the adapter with the latest data
        trainingsplanAdapter.updateData(updatedItems)

        // Show empty state if needed
        if (currentPlan.items.isEmpty()) {
            emptyStateText.visibility = View.VISIBLE
            recyclerView.visibility = View.GONE
        } else {
            emptyStateText.visibility = View.GONE
            recyclerView.visibility = View.VISIBLE
        }
    }

    private fun showAddExerciseDialog() {
        val dialogView = LayoutInflater.from(requireContext())
            .inflate(R.layout.dialog_add_exercise, null)

        // Use ThemeHelper to create a themed AlertDialog with default animations
        val dialog = ThemeHelper.createDefaultAnimationAlertDialogBuilder(requireContext())
            .setView(dialogView)
            .create()

        // Get references to views
        val categoryTabs = dialogView.findViewById<TabLayout>(R.id.categoryTabs)
        val exercisesRecyclerView = dialogView.findViewById<RecyclerView>(R.id.exercisesRecyclerView)
        val customExerciseLayout = dialogView.findViewById<ConstraintLayout>(R.id.customExerciseLayout)
        val exerciseNameInput = dialogView.findViewById<TextInputEditText>(R.id.exerciseNameInput)
        val exerciseTypeDropdown = dialogView.findViewById<AutoCompleteTextView>(R.id.exerciseTypeDropdown)
        val cancelButton = dialogView.findViewById<MaterialButton>(R.id.cancelButton)
        val addButton = dialogView.findViewById<MaterialButton>(R.id.addButton)

        // The SnookerTabLayout will automatically apply the appropriate theme

        // Hide the target count picker as we're replacing it with in-card controls
        dialogView.findViewById<LinearLayout>(R.id.targetCountLayout)?.visibility = View.GONE

        // Set up tabs for categories
        val selectedExercises = mutableListOf<ExerciseItem>()
        val exerciseAdapter = ExerciseAdapter(
            emptyList(),
            onItemClick = { /* Selection handled in adapter */ },
            selectedExercisesList = selectedExercises
        )
        exercisesRecyclerView.layoutManager = LinearLayoutManager(requireContext())
        exercisesRecyclerView.adapter = exerciseAdapter

        var categoriesGlobal = mutableListOf<String>()
        var dropdownCategoriesGlobal = listOf<String>()


        viewLifecycleOwner.lifecycleScope.launch {
            try {
                val fetchedCategories = trainingsplanManager.getExerciseCategories().toMutableList()
                categoriesGlobal = fetchedCategories
                categoriesGlobal.add("Eigene Übung") // Add "Eigene Übung" as the last tab

                categoriesGlobal.forEach { categoryName ->
                    categoryTabs.addTab(categoryTabs.newTab().setText(categoryName))
                }

                // Load initial exercises for first category (if it exists and is not "Eigene Übung")
                if (categoriesGlobal.isNotEmpty() && categoriesGlobal[0] != "Eigene Übung") {
                    val firstCategory = categoriesGlobal[0]
                    val exercises = trainingsplanManager.getExercisesForCategory(firstCategory)
                    exerciseAdapter.updateData(exercises)
                    customExerciseLayout.visibility = View.GONE
                    exercisesRecyclerView.visibility = View.VISIBLE
                } else if (categoriesGlobal.isNotEmpty() && categoriesGlobal[0] == "Eigene Übung") {
                    // Handle case where "Eigene Übung" is the only or first tab
                    customExerciseLayout.visibility = View.VISIBLE
                    exercisesRecyclerView.visibility = View.GONE
                }


                // Setup dropdown for custom exercise after categories are loaded
                dropdownCategoriesGlobal = categoriesGlobal.filter { it != "Eigene Übung" }
                val dropdownAdapter = ThemedArrayAdapter(requireContext(), R.layout.dropdown_item, dropdownCategoriesGlobal)
                exerciseTypeDropdown.setAdapter(dropdownAdapter)


            } catch (e: Exception) {
                Toast.makeText(requireContext(), "Failed to load exercise categories: ${e.message}", Toast.LENGTH_LONG).show()
                // Potentially disable tabs or show an error state in the dialog
            }
        }


        // Handle category tab changes
        categoryTabs.addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
            override fun onTabSelected(tab: TabLayout.Tab?) {
                tab?.let {
                    val category = it.text.toString()
                    if (category == "Eigene Übung") {
                        customExerciseLayout.visibility = View.VISIBLE
                        exercisesRecyclerView.visibility = View.GONE
                    } else {
                        customExerciseLayout.visibility = View.GONE
                        exercisesRecyclerView.visibility = View.VISIBLE
                        viewLifecycleOwner.lifecycleScope.launch {
                            try {
                                val exercises = trainingsplanManager.getExercisesForCategory(category)
                                exerciseAdapter.updateData(exercises)
                            } catch (e: Exception) {
                                Toast.makeText(requireContext(), "Failed to load exercises for $category: ${e.message}", Toast.LENGTH_LONG).show()
                                exerciseAdapter.updateData(emptyList()) // Clear list on error
                            }
                        }
                    }
                }
            }

            override fun onTabUnselected(tab: TabLayout.Tab?) {}
            override fun onTabReselected(tab: TabLayout.Tab?) {}
        })

        // Set up category dropdown for custom exercise
        // Note: The adapter is now set after categories are fetched asynchronously.
        // Remove "Eigene Übung" from the dropdown options - This is handled when dropdownCategoriesGlobal is populated
        // val dropdownCategories = categories.filter { it != "Eigene Übung" }

        // Apply theme to the TextInputLayouts
        val textInputLayoutStyle = ThemeHelper.getTextInputLayoutStyleForCurrentTheme(requireContext())
        val dropdownTextInputLayoutStyle = ThemeHelper.getDropdownTextInputLayoutStyleForCurrentTheme(requireContext())

        // Apply styles to the TextInputLayouts
        val exerciseNameLayout = dialogView.findViewById<TextInputLayout>(R.id.exerciseNameLayout)
        val exerciseTypeLayout = dialogView.findViewById<TextInputLayout>(R.id.exerciseTypeLayout)

        // Apply the appropriate theme to the TextInputLayouts
        val themePreferences = ThemePreferences(requireContext())
        val currentTheme = themePreferences.getThemeMode()

        when (currentTheme) {
            ThemePreferences.THEME_SNOOKER -> {
                exerciseNameLayout.boxBackgroundColor = resources.getColor(R.color.snooker_card_background_light, null)
                exerciseNameLayout.setBoxStrokeColorStateList(ColorStateList.valueOf(resources.getColor(R.color.snooker_red_500, null)))
                exerciseNameLayout.hintTextColor = ColorStateList.valueOf(resources.getColor(R.color.snooker_red_500, null))

                exerciseTypeLayout.boxBackgroundColor = resources.getColor(R.color.snooker_card_background_light, null)
                exerciseTypeLayout.setBoxStrokeColorStateList(ColorStateList.valueOf(resources.getColor(R.color.snooker_red_500, null)))
                exerciseTypeLayout.hintTextColor = ColorStateList.valueOf(resources.getColor(R.color.snooker_red_500, null))
                exerciseTypeLayout.setEndIconTintList(ColorStateList.valueOf(resources.getColor(R.color.snooker_red_500, null)))
            }
            ThemePreferences.THEME_BLUE -> {
                exerciseNameLayout.boxBackgroundColor = resources.getColor(R.color.blue_card_background_light, null)
                exerciseNameLayout.setBoxStrokeColorStateList(ColorStateList.valueOf(resources.getColor(R.color.blue_500, null)))
                exerciseNameLayout.hintTextColor = ColorStateList.valueOf(resources.getColor(R.color.blue_500, null))

                exerciseTypeLayout.boxBackgroundColor = resources.getColor(R.color.blue_card_background_light, null)
                exerciseTypeLayout.setBoxStrokeColorStateList(ColorStateList.valueOf(resources.getColor(R.color.blue_500, null)))
                exerciseTypeLayout.hintTextColor = ColorStateList.valueOf(resources.getColor(R.color.blue_500, null))
                exerciseTypeLayout.setEndIconTintList(ColorStateList.valueOf(resources.getColor(R.color.blue_500, null)))
            }
            ThemePreferences.THEME_DARK_BLUE -> {
                exerciseNameLayout.boxBackgroundColor = resources.getColor(R.color.dark_blue_card_background_light, null)
                exerciseNameLayout.setBoxStrokeColorStateList(ColorStateList.valueOf(resources.getColor(R.color.dark_blue_500, null)))
                exerciseNameLayout.hintTextColor = ColorStateList.valueOf(resources.getColor(R.color.dark_blue_500, null))

                exerciseTypeLayout.boxBackgroundColor = resources.getColor(R.color.dark_blue_card_background_light, null)
                exerciseTypeLayout.setBoxStrokeColorStateList(ColorStateList.valueOf(resources.getColor(R.color.dark_blue_500, null)))
                exerciseTypeLayout.hintTextColor = ColorStateList.valueOf(resources.getColor(R.color.dark_blue_500, null))
                exerciseTypeLayout.setEndIconTintList(ColorStateList.valueOf(resources.getColor(R.color.dark_blue_500, null)))
            }
            ThemePreferences.THEME_DARK -> {
                exerciseNameLayout.boxBackgroundColor = resources.getColor(R.color.dark_gray, null)
                exerciseNameLayout.setBoxStrokeColorStateList(ColorStateList.valueOf(resources.getColor(R.color.purple_200, null)))
                exerciseNameLayout.hintTextColor = ColorStateList.valueOf(resources.getColor(R.color.purple_200, null))

                exerciseTypeLayout.boxBackgroundColor = resources.getColor(R.color.dark_gray, null)
                exerciseTypeLayout.setBoxStrokeColorStateList(ColorStateList.valueOf(resources.getColor(R.color.purple_200, null)))
                exerciseTypeLayout.hintTextColor = ColorStateList.valueOf(resources.getColor(R.color.purple_200, null))
                exerciseTypeLayout.setEndIconTintList(ColorStateList.valueOf(resources.getColor(R.color.purple_200, null)))
            }
            else -> {
                // Default theme
                exerciseNameLayout.boxBackgroundColor = resources.getColor(R.color.white, null)
                exerciseNameLayout.setBoxStrokeColorStateList(ColorStateList.valueOf(resources.getColor(R.color.purple_500, null)))
                exerciseNameLayout.hintTextColor = ColorStateList.valueOf(resources.getColor(R.color.purple_500, null))

                exerciseTypeLayout.boxBackgroundColor = resources.getColor(R.color.white, null)
                exerciseTypeLayout.setBoxStrokeColorStateList(ColorStateList.valueOf(resources.getColor(R.color.purple_500, null)))
                exerciseTypeLayout.hintTextColor = ColorStateList.valueOf(resources.getColor(R.color.purple_500, null))
                exerciseTypeLayout.setEndIconTintList(ColorStateList.valueOf(resources.getColor(R.color.purple_500, null)))
            }
        }

        // Use our custom ThemedArrayAdapter instead of the default ArrayAdapter
        // Adapter is set inside the lifecycleScope.launch block after categoriesGlobal is populated
        // val adapter = ThemedArrayAdapter(requireContext(), R.layout.dropdown_item, dropdownCategoriesGlobal)
        // exerciseTypeDropdown.setAdapter(adapter)

        // Set the dropdown popup background to match the theme
        val dropdownBackground = ThemeHelper.getDropdownBackgroundForCurrentTheme(requireContext())
        exerciseTypeDropdown.setDropDownBackgroundResource(dropdownBackground)

        // Set the popup elevation to 0 to remove any shadows that might cause white lines
        exerciseTypeDropdown.dropDownVerticalOffset = 0

        // Handle button clicks
        cancelButton.setOnClickListener {
            dialog.dismiss()
        }

        addButton.setOnClickListener {
            // Check which tab is selected
            val selectedCategory = categoryTabs.getTabAt(categoryTabs.selectedTabPosition)?.text.toString()

            if (selectedCategory == "Eigene Übung") {
                // Custom exercise tab
                val exerciseName = exerciseNameInput.text.toString().trim()
                val exerciseType = exerciseTypeDropdown.text.toString().trim()

                if (exerciseName.isNotEmpty()) {
                    trainingsplanManager.addCustomExerciseToPlan(
                        exerciseName,
                        if (exerciseType.isNotEmpty() && dropdownCategoriesGlobal.contains(exerciseType)) exerciseType else null,
                        1 // Default to 1 for custom exercises
                    )
                    updateUI()
                    dialog.dismiss()
                } else {
                    exerciseNameInput.error = getString(R.string.input_error_name_required)
                }
            } else {
                // Existing exercises tab
                val selectedItems = exerciseAdapter.getSelectedExercisesWithCounts()
                if (selectedItems.isNotEmpty()) {
                    // Add each exercise with its count
                    selectedItems.forEach { (exercise, count) ->
                        trainingsplanManager.addExercisesToPlan(listOf(exercise), count)
                    }
                    updateUI()
                    dialog.dismiss()
                } else {
                    Toast.makeText(requireContext(), getString(R.string.toast_please_select_exercise), Toast.LENGTH_SHORT).show()
                }
            }
        }

        // Show the dialog first, then force a layout pass to ensure proper rendering
        dialog.show()

        // Set the dialog width to match the screen width with some padding
        val window = dialog.window
        window?.let { win ->
            val displayMetrics = resources.displayMetrics
            val width = (displayMetrics.widthPixels * 0.9).toInt() // 90% of screen width
            win.setLayout(width, win.attributes.height)
        }

        // Force a layout pass after the dialog is shown to ensure proper rendering
        // This specific post block might be redundant or need adjustment
        // as initial data loading is now handled within the coroutine.
        // If issues arise with initial rendering, this might be revisited.
        // For now, let's assume the coroutine handles initial load properly.
        /*
        dialog.window?.decorView?.post {
            // Force adapter to refresh
            if (categoriesGlobal.isNotEmpty() && categoriesGlobal[0] != "Eigene Übung") {
                val firstCategory = categoriesGlobal[0]
                // This would need to be a coroutine call too if kept
                // viewLifecycleOwner.lifecycleScope.launch {
                //     exerciseAdapter.updateData(trainingsplanManager.getExercisesForCategory(firstCategory))
                //     exercisesRecyclerView.post {
                //         exercisesRecyclerView.invalidate()
                //     }
                // }
            }
        }
        */
    }

    private fun showDeleteConfirmationDialog(item: TrainingsplanItem) {
        // Use ThemeHelper to create a themed AlertDialog
        ThemeHelper.createThemedAlertDialogBuilder(requireContext())
            .setTitle(getString(R.string.dialog_title_delete_exercise))
            .setMessage(getString(R.string.confirm_remove_exercise_from_plan_message, item.name))
            .setPositiveButton(getString(R.string.yes)) { _, _ ->
                trainingsplanManager.removeItem(item.id)
                updateUI()
            }
            .setNegativeButton(getString(R.string.no), null)
            .show()
    }

    /**
     * Shows the debug menu with notification testing options
     */
    private fun showDebugMenu() {
        // Show testing options dialog
        val options = arrayOf(
            "Test Notification (Normal)",
            "Force Notification (Add Test Exercise)",
            "Reschedule Weekly Reminder"
        )

        // Use ThemeHelper to create a themed AlertDialog
        val debugOptions = resources.getStringArray(R.array.debug_menu_options)
        ThemeHelper.createThemedAlertDialogBuilder(requireContext())
            .setTitle(getString(R.string.debug_menu_title))
            .setItems(debugOptions) { _, which ->
                when (which) {
                    0 -> {
                        // Normal notification test
                        val notificationSent = TestNotificationHelper.triggerNotificationTest(requireContext())
                        Toast.makeText(
                            requireContext(),
                            if (notificationSent) getString(R.string.toast_notification_sent_success)
                            else getString(R.string.toast_notification_sent_none),
                            Toast.LENGTH_SHORT
                        ).show()
                    }
                    1 -> {
                        // Force notification with test exercise
                        TestNotificationHelper.forceReminderWorkerRun(requireContext())
                        Toast.makeText(
                            requireContext(),
                            getString(R.string.toast_notification_forced),
                            Toast.LENGTH_SHORT
                        ).show()
                        // Refresh UI to show the new test exercise
                        updateUI()
                    }
                    2 -> {
                        // Reschedule reminder
                        val scheduler = TrainingsplanReminderScheduler(requireContext())
                        scheduler.cancelReminder()
                        scheduler.scheduleWeeklyReminder()
                        Toast.makeText(
                            requireContext(),
                            getString(R.string.toast_reminder_rescheduled),
                            Toast.LENGTH_SHORT
                        ).show()
                    }
                }
            }
            .setNegativeButton(getString(R.string.cancel_button), null)
            .show()
    }
}
