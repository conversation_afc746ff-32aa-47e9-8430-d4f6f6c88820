package com.atom.diesnookerapp.ui.manageexercises

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.atom.diesnookerapp.R
import com.atom.diesnookerapp.data.firebase.UserExerciseDefinition
import com.atom.diesnookerapp.databinding.ItemExerciseDefinitionBinding

class ManageExercisesAdapter(
    private val onEditClick: (UserExerciseDefinition) -> Unit,
    private val onDeleteClick: (UserExerciseDefinition) -> Unit
) : ListAdapter<UserExerciseDefinition, ManageExercisesAdapter.ExerciseViewHolder>(DiffCallback) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ExerciseViewHolder {
        val binding = ItemExerciseDefinitionBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return ExerciseViewHolder(binding)
    }

    override fun onBindViewHolder(holder: ExerciseViewHolder, position: Int) {
        val currentExercise = getItem(position)
        holder.bind(currentExercise, onEditClick, onDeleteClick)
    }

    inner class ExerciseViewHolder(private val binding: ItemExerciseDefinitionBinding) :
        RecyclerView.ViewHolder(binding.root) {

        fun bind(
            exercise: UserExerciseDefinition,
            onEditClick: (UserExerciseDefinition) -> Unit,
            onDeleteClick: (UserExerciseDefinition) -> Unit
        ) {
            binding.exerciseNameTextView.text = exercise.name
            binding.exerciseCategoryTextView.text = exercise.category

            binding.editExerciseButton.setOnClickListener {
                onEditClick(exercise)
            }

            if (exercise.isDeletable) {
                binding.deleteExerciseButton.visibility = View.VISIBLE
                binding.deleteExerciseButton.setOnClickListener {
                    onDeleteClick(exercise)
                }
            } else {
                binding.deleteExerciseButton.visibility = View.GONE
            }
        }
    }

    companion object {
        private val DiffCallback = object : DiffUtil.ItemCallback<UserExerciseDefinition>() {
            override fun areItemsTheSame(oldItem: UserExerciseDefinition, newItem: UserExerciseDefinition): Boolean {
                return oldItem.id == newItem.id
            }

            override fun areContentsTheSame(oldItem: UserExerciseDefinition, newItem: UserExerciseDefinition): Boolean {
                return oldItem == newItem
            }
        }
    }
}
