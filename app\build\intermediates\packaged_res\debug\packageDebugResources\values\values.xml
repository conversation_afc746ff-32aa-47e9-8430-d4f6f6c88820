<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string-array name="debug_menu_options">
        <item>Test Notification (Normal)</item>
        <item>Force Notification (Add Test Exercise)</item>
        <item>Reschedule Weekly Reminder</item>
    </string-array>
    <color name="black">#FF000000</color>
    <color name="blue_200">#90CAF9</color>
    <color name="blue_500">#2196F3</color>
    <color name="blue_700">#1976D2</color>
    <color name="blue_card_background_dark">#0D47A1</color>
    <color name="blue_card_background_light">#E3F2FD</color>
    <color name="crimson_card_background_dark">#38040E</color>
    <color name="crimson_card_background_light">#FFEBEE</color>
    <color name="crimson_dark">#38040E</color>
    <color name="crimson_darkest">#250902</color>
    <color name="crimson_light">#800E13</color>
    <color name="crimson_lightest">#AD2831</color>
    <color name="crimson_medium">#640D14</color>
    <color name="cyan_200">#80DEEA</color>
    <color name="cyan_500">#00BCD4</color>
    <color name="cyan_700">#0097A7</color>
    <color name="dark_blue_200">#64B5F6</color>
    <color name="dark_blue_500">#0D47A1</color>
    <color name="dark_blue_700">#0A3880</color>
    <color name="dark_blue_card_background_dark">#01579B</color>
    <color name="dark_blue_card_background_light">#E1F5FE</color>
    <color name="dark_gray">#303030</color>
    <color name="ic_launcher_background">#0E6C1C</color>
    <color name="light_gray">#E0E0E0</color>
    <color name="neon_card_background_dark">#6A00F4</color>
    <color name="neon_card_background_light">#F3E5F5</color>
    <color name="neon_pink_1">#DB00B6</color>
    <color name="neon_pink_2">#E500A4</color>
    <color name="neon_pink_3">#F20089</color>
    <color name="neon_purple_1">#2D00F7</color>
    <color name="neon_purple_2">#6A00F4</color>
    <color name="neon_purple_3">#8900F2</color>
    <color name="neon_purple_4">#A100F2</color>
    <color name="neon_purple_5">#B100E8</color>
    <color name="neon_purple_6">#BC00DD</color>
    <color name="neon_purple_7">#D100D1</color>
    <color name="ocean_card_background_dark">#1C2541</color>
    <color name="ocean_card_background_light">#DAFFFE</color>
    <color name="ocean_dark">#1C2541</color>
    <color name="ocean_darkest">#0B132B</color>
    <color name="ocean_light">#5BC0BE</color>
    <color name="ocean_lightest">#6FFFE9</color>
    <color name="ocean_medium">#3A506B</color>
    <color name="orange_200">#FFCC80</color>
    <color name="orange_500">#FF9800</color>
    <color name="orange_700">#F57C00</color>
    <color name="purple_200">#FFBB86FC</color>
    <color name="purple_500">#FF6200EE</color>
    <color name="purple_700">#FF3700B3</color>
    <color name="selected_color">#E0E0E0</color>
    <color name="snooker_black">#212121</color>
    <color name="snooker_blue">#1565C0</color>
    <color name="snooker_brown">#8B4513</color>
    <color name="snooker_card_background_dark">#1B5E20</color>
    <color name="snooker_card_background_light">#C8E6C9</color>
    <color name="snooker_green_200">#81C784</color>
    <color name="snooker_green_500">#0B6623</color>
    <color name="snooker_green_700">#094D1B</color>
    <color name="snooker_pink">#E91E63</color>
    <color name="snooker_red_200">#EF9A9A</color>
    <color name="snooker_red_500">#D32F2F</color>
    <color name="snooker_red_700">#B71C1C</color>
    <color name="snooker_yellow">#FBC02D</color>
    <color name="teal_200">#FF03DAC5</color>
    <color name="teal_700">#FF018786</color>
    <color name="white">#FFFFFFFF</color>
    <dimen name="dialog_input_min_height">150dp</dimen>
    <dimen name="legend_dialog_width">240dp</dimen>
    <string name="action_create_new_category">+ Create New Category</string>
    <string name="add_exercise_button_label">Add Exercise</string>
    <string name="app_name">Die Snooker App</string>
    <string name="cancel_button">Cancel</string>
    <string name="category_not_specified_toast">Category not specified.</string>
    <string name="confirm_delete_exercise_message">Delete exercise \'%s\'?</string>
    <string name="confirm_remove_exercise_from_plan_message">Remove exercise \'%s\' from the training plan?</string>
    <string name="debug_menu_title">Debug Menu</string>
    <string name="default_exercises_added_toast">Default exercises added to your library.</string>
    <string name="default_exercises_failed_toast">Failed to add default exercises.</string>
    <string name="default_exercises_partial_success_toast">Some default exercises added, some failed.</string>
    <string name="delete_button">Delete</string>
    <string name="dialog_title_add_exercise">Add Exercise</string>
    <string name="dialog_title_confirmation">Confirmation</string>
    <string name="dialog_title_delete_exercise">Delete Exercise</string>
    <string name="dialog_title_edit_exercise">Edit Exercise</string>
    <string name="edit_button">Edit</string>
    <string name="error_deleting_exercise_toast">Error deleting exercise.</string>
    <string name="error_loading_categories_toast">Failed to load categories.</string>
    <string name="error_loading_exercises_toast">Error loading exercises.</string>
    <string name="error_saving_exercise_toast">Error saving exercise.</string>
    <string name="error_user_id_not_found">Error: User ID not found</string>
    <string name="error_user_id_not_found_info">Could not retrieve user information.</string>
    <string name="exercise_deleted_toast">Exercise deleted.</string>
    <string name="exercise_not_deletable_toast">is not deletable.</string>
    <string name="exercise_saved_toast">Exercise saved.</string>
    <string name="fragment_label_manage_exercises">Manage Exercises</string>
    <string name="gcm_defaultSenderId" translatable="false">547283642216</string>
    <string name="google_api_key" translatable="false">AIzaSyBYUMN3Xe-YUqM2rWka7TPakht8YpKWyPI</string>
    <string name="google_app_id" translatable="false">1:547283642216:android:f0d4e7958787128030d8dd</string>
    <string name="google_crash_reporting_api_key" translatable="false">AIzaSyBYUMN3Xe-YUqM2rWka7TPakht8YpKWyPI</string>
    <string name="google_storage_bucket" translatable="false">die-snooker-app.firebasestorage.app</string>
    <string name="input_error_category_required">Category is required.</string>
    <string name="input_error_name_required">Name is required.</string>
    <string name="label_category">Category</string>
    <string name="label_description">Description</string>
    <string name="label_empty_exercise_list">No custom exercises yet. Tap \'+\' to add one.</string>
    <string name="label_name">Name</string>
    <string name="label_new_category">New Category Name</string>
    <string name="manage_my_exercises_button">Manage My Exercises</string>
    <string name="no">No</string>
    <string name="no_categories_found_toast">No categories found. Add exercises to create categories.</string>
    <string name="no_exercises_found_toast">No exercises found.</string>
    <string name="no_exercises_in_category_generic">No exercises found in this category.</string>
    <string name="no_exercises_in_category_toast">No exercises found for %s.</string>
    <string name="please_log_in_to_load_categories_toast">Please log in to load categories.</string>
    <string name="please_log_in_to_load_exercises_toast">Please log in to load exercises.</string>
    <string name="please_log_in_to_manage_exercises_toast">Please log in to manage exercises.</string>
    <string name="project_id" translatable="false">die-snooker-app</string>
    <string name="save_button">Save</string>
    <string name="title_category_exercises_generic">Category Exercises</string>
    <string name="toast_no_specific_view_for_category">No specific view for category: %s</string>
    <string name="toast_notification_forced">Forced notification with test exercise.</string>
    <string name="toast_notification_sent_none">No notification sent - no incomplete exercises.</string>
    <string name="toast_notification_sent_success">Notification sent successfully!</string>
    <string name="toast_plan_reset_archived">Training plan was reset and archived.</string>
    <string name="toast_please_select_exercise">Please select at least one exercise.</string>
    <string name="toast_reminder_rescheduled">Weekly reminder rescheduled.</string>
    <string name="unknown_category">Unknown Category</string>
    <string name="yes">Yes</string>
    <style name="BlueCardStyle" parent="Widget.MaterialComponents.CardView">
        <item name="cardBackgroundColor">@color/blue_card_background_light</item>
        <item name="cardElevation">4dp</item>
        <item name="cardCornerRadius">8dp</item>
    </style>
    <style name="BlueNegativeButtonStyle" parent="Widget.MaterialComponents.Button.TextButton.Dialog">
        <item name="android:textColor">@color/orange_500</item>
    </style>
    <style name="BluePositiveButtonStyle" parent="Widget.MaterialComponents.Button.TextButton.Dialog">
        <item name="android:textColor">@color/blue_500</item>
    </style>
    <style name="CrimsonCardStyle" parent="Widget.MaterialComponents.CardView">
        <item name="cardBackgroundColor">@color/crimson_card_background_light</item>
        <item name="cardElevation">4dp</item>
        <item name="cardCornerRadius">8dp</item>
    </style>
    <style name="CrimsonNegativeButtonStyle" parent="Widget.MaterialComponents.Button.TextButton.Dialog">
        <item name="android:textColor">@color/crimson_lightest</item>
    </style>
    <style name="CrimsonPositiveButtonStyle" parent="Widget.MaterialComponents.Button.TextButton.Dialog">
        <item name="android:textColor">@color/crimson_medium</item>
    </style>
    <style name="CustomDropdownStyle" parent="Widget.AppCompat.DropDownItem.Spinner">
        <item name="android:background">@drawable/dropdown_background_solid_default</item>
        <item name="android:textColor">@color/black</item>
        <item name="android:paddingStart">16dp</item>
        <item name="android:paddingEnd">16dp</item>
        <item name="android:paddingTop">12dp</item>
        <item name="android:paddingBottom">12dp</item>
    </style>
    <style name="CustomDropdownStyle.Blue">
        <item name="android:background">@drawable/dropdown_background_solid_blue</item>
        <item name="android:textColor">@color/black</item>
    </style>
    <style name="CustomDropdownStyle.Crimson">
        <item name="android:background">@drawable/dropdown_background_solid_crimson</item>
        <item name="android:textColor">@color/black</item>
    </style>
    <style name="CustomDropdownStyle.Dark">
        <item name="android:background">@drawable/dropdown_background_solid_dark</item>
        <item name="android:textColor">@color/white</item>
    </style>
    <style name="CustomDropdownStyle.DarkBlue">
        <item name="android:background">@drawable/dropdown_background_solid_dark_blue</item>
        <item name="android:textColor">@color/black</item>
    </style>
    <style name="CustomDropdownStyle.Neon">
        <item name="android:background">@drawable/dropdown_background_solid_neon</item>
        <item name="android:textColor">@color/black</item>
    </style>
    <style name="CustomDropdownStyle.Ocean">
        <item name="android:background">@drawable/dropdown_background_solid_ocean</item>
        <item name="android:textColor">@color/black</item>
    </style>
    <style name="CustomDropdownStyle.Snooker">
        <item name="android:background">@drawable/dropdown_background_solid_snooker</item>
        <item name="android:textColor">@color/black</item>
    </style>
    <style name="CustomDropdownTextInputLayout" parent="Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu">
        <item name="boxStrokeColor">?attr/colorPrimary</item>
        <item name="hintTextColor">?attr/colorPrimary</item>
    </style>
    <style name="CustomDropdownTextInputLayout.Blue">
        <item name="boxStrokeColor">@color/blue_500</item>
        <item name="hintTextColor">@color/blue_500</item>
        <item name="boxBackgroundColor">@color/blue_card_background_light</item>
        <item name="endIconTint">@color/blue_500</item>
    </style>
    <style name="CustomDropdownTextInputLayout.Crimson">
        <item name="boxStrokeColor">@color/crimson_medium</item>
        <item name="hintTextColor">@color/crimson_medium</item>
        <item name="endIconTint">@color/crimson_medium</item>
        <item name="boxBackgroundColor">@color/crimson_card_background_light</item>
    </style>
    <style name="CustomDropdownTextInputLayout.Dark">
        <item name="boxStrokeColor">@color/purple_200</item>
        <item name="hintTextColor">@color/purple_200</item>
        <item name="boxBackgroundColor">@color/dark_gray</item>
        <item name="endIconTint">@color/purple_200</item>
    </style>
    <style name="CustomDropdownTextInputLayout.DarkBlue">
        <item name="boxStrokeColor">@color/dark_blue_500</item>
        <item name="hintTextColor">@color/dark_blue_500</item>
        <item name="boxBackgroundColor">@color/dark_blue_card_background_light</item>
        <item name="endIconTint">@color/dark_blue_500</item>
    </style>
    <style name="CustomDropdownTextInputLayout.Neon">
        <item name="boxStrokeColor">@color/neon_purple_3</item>
        <item name="hintTextColor">@color/neon_purple_3</item>
        <item name="endIconTint">@color/neon_purple_3</item>
        <item name="boxBackgroundColor">@color/neon_card_background_light</item>
    </style>
    <style name="CustomDropdownTextInputLayout.Ocean">
        <item name="boxStrokeColor">@color/ocean_medium</item>
        <item name="hintTextColor">@color/ocean_medium</item>
        <item name="endIconTint">@color/ocean_medium</item>
        <item name="boxBackgroundColor">@color/ocean_card_background_light</item>
    </style>
    <style name="CustomDropdownTextInputLayout.Snooker">
        <item name="boxStrokeColor">@color/snooker_red_500</item>
        <item name="hintTextColor">@color/snooker_red_500</item>
        <item name="boxBackgroundColor">@color/snooker_card_background_light</item>
        <item name="endIconTint">@color/snooker_red_500</item>
    </style>
    <style name="CustomTabLayout" parent="Widget.MaterialComponents.TabLayout">
        <item name="tabBackground">@color/white</item>
        <item name="tabIndicatorColor">?attr/colorPrimary</item>
        <item name="tabSelectedTextColor">?attr/colorPrimary</item>
        <item name="tabTextColor">@color/black</item>
    </style>
    <style name="CustomTabLayout.Blue">
        <item name="tabBackground">@color/blue_card_background_light</item>
        <item name="tabIndicatorColor">@color/blue_500</item>
        <item name="tabSelectedTextColor">@color/blue_500</item>
        <item name="tabTextColor">@color/black</item>
    </style>
    <style name="CustomTabLayout.Crimson">
        <item name="tabBackground">@color/crimson_card_background_light</item>
        <item name="tabIndicatorColor">@color/crimson_medium</item>
        <item name="tabSelectedTextColor">@color/crimson_medium</item>
        <item name="tabTextColor">@color/black</item>
    </style>
    <style name="CustomTabLayout.Dark">
        <item name="tabBackground">@color/dark_gray</item>
        <item name="tabIndicatorColor">@color/purple_200</item>
        <item name="tabSelectedTextColor">@color/purple_200</item>
        <item name="tabTextColor">@color/white</item>
    </style>
    <style name="CustomTabLayout.DarkBlue">
        <item name="tabBackground">@color/dark_blue_card_background_light</item>
        <item name="tabIndicatorColor">@color/dark_blue_500</item>
        <item name="tabSelectedTextColor">@color/dark_blue_500</item>
        <item name="tabTextColor">@color/black</item>
    </style>
    <style name="CustomTabLayout.Neon">
        <item name="tabBackground">@color/neon_card_background_light</item>
        <item name="tabIndicatorColor">@color/neon_purple_3</item>
        <item name="tabSelectedTextColor">@color/neon_purple_3</item>
        <item name="tabTextColor">@color/black</item>
    </style>
    <style name="CustomTabLayout.Ocean">
        <item name="tabBackground">@color/ocean_card_background_light</item>
        <item name="tabIndicatorColor">@color/ocean_medium</item>
        <item name="tabSelectedTextColor">@color/ocean_medium</item>
        <item name="tabTextColor">@color/black</item>
    </style>
    <style name="CustomTabLayout.Snooker">
        <item name="tabBackground">@color/snooker_card_background_light</item>
        <item name="tabIndicatorColor">@color/snooker_red_500</item>
        <item name="tabSelectedTextColor">@color/snooker_red_500</item>
        <item name="tabTextColor">@color/black</item>
    </style>
    <style name="CustomTextInputLayout" parent="Widget.MaterialComponents.TextInputLayout.OutlinedBox">
        <item name="boxStrokeColor">?attr/colorPrimary</item>
        <item name="hintTextColor">?attr/colorPrimary</item>
    </style>
    <style name="CustomTextInputLayout.Blue">
        <item name="boxStrokeColor">@color/blue_500</item>
        <item name="hintTextColor">@color/blue_500</item>
        <item name="boxBackgroundColor">@color/blue_card_background_light</item>
    </style>
    <style name="CustomTextInputLayout.Crimson">
        <item name="boxStrokeColor">@color/crimson_medium</item>
        <item name="hintTextColor">@color/crimson_medium</item>
        <item name="boxBackgroundColor">@color/crimson_card_background_light</item>
    </style>
    <style name="CustomTextInputLayout.Dark">
        <item name="boxStrokeColor">@color/purple_200</item>
        <item name="hintTextColor">@color/purple_200</item>
        <item name="boxBackgroundColor">@color/dark_gray</item>
    </style>
    <style name="CustomTextInputLayout.DarkBlue">
        <item name="boxStrokeColor">@color/dark_blue_500</item>
        <item name="hintTextColor">@color/dark_blue_500</item>
        <item name="boxBackgroundColor">@color/dark_blue_card_background_light</item>
    </style>
    <style name="CustomTextInputLayout.Neon">
        <item name="boxStrokeColor">@color/neon_purple_3</item>
        <item name="hintTextColor">@color/neon_purple_3</item>
        <item name="boxBackgroundColor">@color/neon_card_background_light</item>
    </style>
    <style name="CustomTextInputLayout.Ocean">
        <item name="boxStrokeColor">@color/ocean_medium</item>
        <item name="hintTextColor">@color/ocean_medium</item>
        <item name="boxBackgroundColor">@color/ocean_card_background_light</item>
    </style>
    <style name="CustomTextInputLayout.Snooker">
        <item name="boxStrokeColor">@color/snooker_red_500</item>
        <item name="hintTextColor">@color/snooker_red_500</item>
        <item name="boxBackgroundColor">@color/snooker_card_background_light</item>
    </style>
    <style name="DarkBlueCardStyle" parent="Widget.MaterialComponents.CardView">
        <item name="cardBackgroundColor">@color/dark_blue_card_background_light</item>
        <item name="cardElevation">4dp</item>
        <item name="cardCornerRadius">8dp</item>
    </style>
    <style name="DarkBlueNegativeButtonStyle" parent="Widget.MaterialComponents.Button.TextButton.Dialog">
        <item name="android:textColor">@color/cyan_500</item>
    </style>
    <style name="DarkBluePositiveButtonStyle" parent="Widget.MaterialComponents.Button.TextButton.Dialog">
        <item name="android:textColor">@color/dark_blue_500</item>
    </style>
    <style name="DarkNegativeButtonStyle" parent="Widget.MaterialComponents.Button.TextButton.Dialog">
        <item name="android:textColor">@color/teal_200</item>
    </style>
    <style name="DarkPositiveButtonStyle" parent="Widget.MaterialComponents.Button.TextButton.Dialog">
        <item name="android:textColor">@color/purple_200</item>
    </style>
    <style name="DatePickerButtonStyle" parent="Widget.MaterialComponents.Button.TextButton.Dialog">
        <item name="android:textColor">?attr/colorPrimary</item>
    </style>
    <style name="DatePickerDialogTheme" parent="ThemeOverlay.MaterialComponents.Dialog">
        <item name="colorAccent">?attr/colorPrimary</item>
        <item name="android:buttonBarPositiveButtonStyle">@style/DatePickerButtonStyle</item>
        <item name="android:buttonBarNegativeButtonStyle">@style/DatePickerButtonStyle</item>
    </style>
    <style name="DefaultAnimationDialog" parent="ThemeOverlay.MaterialComponents.Dialog.Alert">
        
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowMinWidthMajor">0dp</item>
        <item name="android:windowMinWidthMinor">0dp</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:windowContentOverlay">@null</item>
    </style>
    <style name="DefaultAnimationDialog.Blue">
        <item name="android:windowBackground">@drawable/dropdown_background_blue</item>
        <item name="android:textColor">@color/black</item>
        <item name="colorAccent">@color/blue_500</item>
        <item name="buttonBarPositiveButtonStyle">@style/BluePositiveButtonStyle</item>
        <item name="buttonBarNegativeButtonStyle">@style/BlueNegativeButtonStyle</item>
    </style>
    <style name="DefaultAnimationDialog.Crimson">
        <item name="android:windowBackground">@drawable/dropdown_background_crimson</item>
        <item name="android:textColor">@color/black</item>
        <item name="colorAccent">@color/crimson_medium</item>
        <item name="buttonBarPositiveButtonStyle">@style/CrimsonPositiveButtonStyle</item>
        <item name="buttonBarNegativeButtonStyle">@style/CrimsonNegativeButtonStyle</item>
    </style>
    <style name="DefaultAnimationDialog.Dark">
        <item name="android:windowBackground">@drawable/dropdown_background_dark</item>
        <item name="android:textColor">@color/white</item>
        <item name="colorAccent">@color/purple_200</item>
        <item name="buttonBarPositiveButtonStyle">@style/DarkPositiveButtonStyle</item>
        <item name="buttonBarNegativeButtonStyle">@style/DarkNegativeButtonStyle</item>
    </style>
    <style name="DefaultAnimationDialog.DarkBlue">
        <item name="android:windowBackground">@drawable/dropdown_background_dark_blue</item>
        <item name="android:textColor">@color/black</item>
        <item name="colorAccent">@color/dark_blue_500</item>
        <item name="buttonBarPositiveButtonStyle">@style/DarkBluePositiveButtonStyle</item>
        <item name="buttonBarNegativeButtonStyle">@style/DarkBlueNegativeButtonStyle</item>
    </style>
    <style name="DefaultAnimationDialog.Neon">
        <item name="android:windowBackground">@drawable/dropdown_background_neon</item>
        <item name="android:textColor">@color/black</item>
        <item name="colorAccent">@color/neon_purple_3</item>
        <item name="buttonBarPositiveButtonStyle">@style/NeonPositiveButtonStyle</item>
        <item name="buttonBarNegativeButtonStyle">@style/NeonNegativeButtonStyle</item>
    </style>
    <style name="DefaultAnimationDialog.Ocean">
        <item name="android:windowBackground">@drawable/dropdown_background_ocean</item>
        <item name="android:textColor">@color/black</item>
        <item name="colorAccent">@color/ocean_medium</item>
        <item name="buttonBarPositiveButtonStyle">@style/OceanPositiveButtonStyle</item>
        <item name="buttonBarNegativeButtonStyle">@style/OceanNegativeButtonStyle</item>
    </style>
    <style name="DefaultAnimationDialog.Snooker">
        <item name="android:windowBackground">@drawable/dropdown_background_snooker</item>
        <item name="android:textColor">@color/black</item>
        <item name="colorAccent">@color/snooker_red_500</item>
        <item name="buttonBarPositiveButtonStyle">@style/SnookerPositiveButtonStyle</item>
        <item name="buttonBarNegativeButtonStyle">@style/SnookerNegativeButtonStyle</item>
    </style>
    <style name="DropdownAnimation">
        <item name="android:windowEnterAnimation">@anim/slide_up</item>
        <item name="android:windowExitAnimation">@anim/slide_down</item>
    </style>
    <style name="DropdownDialog" parent="ThemeOverlay.MaterialComponents.Dialog.Alert">
        <item name="android:windowBackground">@drawable/dropdown_background</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowAnimationStyle">@style/DropdownAnimation</item>
        <item name="android:windowMinWidthMajor">0dp</item>
        <item name="android:windowMinWidthMinor">0dp</item>
        <item name="android:backgroundDimEnabled">false</item>
        <item name="android:windowContentOverlay">@null</item>
    </style>
    <style name="DropdownDialog.Blue">
        <item name="android:windowBackground">@drawable/dropdown_background_blue</item>
        <item name="android:textColor">@color/black</item>
        <item name="colorAccent">@color/blue_500</item>
        <item name="buttonBarPositiveButtonStyle">@style/BluePositiveButtonStyle</item>
        <item name="buttonBarNegativeButtonStyle">@style/BlueNegativeButtonStyle</item>
    </style>
    <style name="DropdownDialog.Crimson">
        <item name="android:windowBackground">@drawable/dropdown_background_crimson</item>
        <item name="android:textColor">@color/black</item>
        <item name="colorAccent">@color/crimson_medium</item>
        <item name="buttonBarPositiveButtonStyle">@style/CrimsonPositiveButtonStyle</item>
        <item name="buttonBarNegativeButtonStyle">@style/CrimsonNegativeButtonStyle</item>
    </style>
    <style name="DropdownDialog.Dark">
        <item name="android:windowBackground">@drawable/dropdown_background_dark</item>
        <item name="android:textColor">@color/white</item>
        <item name="colorAccent">@color/purple_200</item>
        <item name="buttonBarPositiveButtonStyle">@style/DarkPositiveButtonStyle</item>
        <item name="buttonBarNegativeButtonStyle">@style/DarkNegativeButtonStyle</item>
    </style>
    <style name="DropdownDialog.DarkBlue">
        <item name="android:windowBackground">@drawable/dropdown_background_dark_blue</item>
        <item name="android:textColor">@color/black</item>
        <item name="colorAccent">@color/dark_blue_500</item>
        <item name="buttonBarPositiveButtonStyle">@style/DarkBluePositiveButtonStyle</item>
        <item name="buttonBarNegativeButtonStyle">@style/DarkBlueNegativeButtonStyle</item>
    </style>
    <style name="DropdownDialog.Neon">
        <item name="android:windowBackground">@drawable/dropdown_background_neon</item>
        <item name="android:textColor">@color/black</item>
        <item name="colorAccent">@color/neon_purple_3</item>
        <item name="buttonBarPositiveButtonStyle">@style/NeonPositiveButtonStyle</item>
        <item name="buttonBarNegativeButtonStyle">@style/NeonNegativeButtonStyle</item>
    </style>
    <style name="DropdownDialog.Ocean">
        <item name="android:windowBackground">@drawable/dropdown_background_ocean</item>
        <item name="android:textColor">@color/black</item>
        <item name="colorAccent">@color/ocean_medium</item>
        <item name="buttonBarPositiveButtonStyle">@style/OceanPositiveButtonStyle</item>
        <item name="buttonBarNegativeButtonStyle">@style/OceanNegativeButtonStyle</item>
    </style>
    <style name="DropdownDialog.Snooker">
        <item name="android:windowBackground">@drawable/dropdown_background_snooker</item>
        <item name="android:textColor">@color/black</item>
        <item name="colorAccent">@color/snooker_red_500</item>
        <item name="buttonBarPositiveButtonStyle">@style/SnookerPositiveButtonStyle</item>
        <item name="buttonBarNegativeButtonStyle">@style/SnookerNegativeButtonStyle</item>
    </style>
    <style name="NeonCardStyle" parent="Widget.MaterialComponents.CardView">
        <item name="cardBackgroundColor">@color/neon_card_background_light</item>
        <item name="cardElevation">4dp</item>
        <item name="cardCornerRadius">8dp</item>
    </style>
    <style name="NeonNegativeButtonStyle" parent="Widget.MaterialComponents.Button.TextButton.Dialog">
        <item name="android:textColor">@color/neon_pink_2</item>
    </style>
    <style name="NeonPositiveButtonStyle" parent="Widget.MaterialComponents.Button.TextButton.Dialog">
        <item name="android:textColor">@color/neon_purple_3</item>
    </style>
    <style name="OceanCardStyle" parent="Widget.MaterialComponents.CardView">
        <item name="cardBackgroundColor">@color/ocean_card_background_light</item>
        <item name="cardElevation">4dp</item>
        <item name="cardCornerRadius">8dp</item>
    </style>
    <style name="OceanNegativeButtonStyle" parent="Widget.MaterialComponents.Button.TextButton.Dialog">
        <item name="android:textColor">@color/ocean_lightest</item>
    </style>
    <style name="OceanPositiveButtonStyle" parent="Widget.MaterialComponents.Button.TextButton.Dialog">
        <item name="android:textColor">@color/ocean_medium</item>
    </style>
    <style name="PopupMenuItemStyle" parent="TextAppearance.AppCompat.Widget.PopupMenu.Small">
        <item name="android:textColor">@color/black</item>
    </style>
    <style name="PopupMenuItemStyle_Blue" parent="PopupMenuItemStyle">
        <item name="android:textColor">@color/black</item>
        <item name="android:background">@drawable/dropdown_background_blue</item>
    </style>
    <style name="PopupMenuItemStyle_Dark" parent="PopupMenuItemStyle">
        <item name="android:textColor">@color/white</item>
        <item name="android:background">@drawable/dropdown_background_dark</item>
    </style>
    <style name="PopupMenuItemStyle_DarkBlue" parent="PopupMenuItemStyle">
        <item name="android:textColor">@color/black</item>
        <item name="android:background">@drawable/dropdown_background_dark_blue</item>
    </style>
    <style name="PopupMenuItemStyle_Snooker" parent="PopupMenuItemStyle">
        <item name="android:textColor">@color/black</item>
        <item name="android:background">@drawable/dropdown_background_snooker</item>
    </style>
    <style name="PopupMenuListViewStyle" parent="Widget.AppCompat.ListView.DropDown">
        <item name="android:divider">@android:color/transparent</item>
        <item name="android:dividerHeight">0dp</item>
        <item name="android:background">@drawable/dropdown_background</item>
    </style>
    <style name="PopupMenuListViewStyle_Blue" parent="PopupMenuListViewStyle">
        <item name="android:background">@drawable/dropdown_background_blue</item>
    </style>
    <style name="PopupMenuListViewStyle_Crimson" parent="PopupMenuListViewStyle">
        <item name="android:background">@drawable/dropdown_background_crimson</item>
    </style>
    <style name="PopupMenuListViewStyle_Dark" parent="PopupMenuListViewStyle">
        <item name="android:background">@drawable/dropdown_background_dark</item>
    </style>
    <style name="PopupMenuListViewStyle_DarkBlue" parent="PopupMenuListViewStyle">
        <item name="android:background">@drawable/dropdown_background_dark_blue</item>
    </style>
    <style name="PopupMenuListViewStyle_Neon" parent="PopupMenuListViewStyle">
        <item name="android:background">@drawable/dropdown_background_neon</item>
    </style>
    <style name="PopupMenuListViewStyle_Ocean" parent="PopupMenuListViewStyle">
        <item name="android:background">@drawable/dropdown_background_ocean</item>
    </style>
    <style name="PopupMenuListViewStyle_Snooker" parent="PopupMenuListViewStyle">
        <item name="android:background">@drawable/dropdown_background_snooker</item>
    </style>
    <style name="PopupMenuStyle" parent="Widget.AppCompat.PopupMenu">
        <item name="android:popupBackground">@drawable/dropdown_background</item>
        <item name="android:textColor">@color/black</item>
        <item name="android:itemBackground">@drawable/dropdown_background</item>
        <item name="android:dropDownListViewStyle">@style/PopupMenuListViewStyle</item>
    </style>
    <style name="PopupMenuStyle_Blue" parent="PopupMenuStyle">
        <item name="android:popupBackground">@drawable/dropdown_background_blue</item>
        <item name="android:textColor">@color/black</item>
        <item name="android:itemBackground">@drawable/dropdown_background_blue</item>
        <item name="android:dropDownListViewStyle">@style/PopupMenuListViewStyle_Blue</item>
    </style>
    <style name="PopupMenuStyle_Crimson" parent="PopupMenuStyle">
        <item name="android:popupBackground">@drawable/dropdown_background_crimson</item>
        <item name="android:textColor">@color/black</item>
        <item name="android:itemBackground">@drawable/dropdown_background_crimson</item>
        <item name="android:dropDownListViewStyle">@style/PopupMenuListViewStyle_Crimson</item>
    </style>
    <style name="PopupMenuStyle_Dark" parent="PopupMenuStyle">
        <item name="android:popupBackground">@drawable/dropdown_background_dark</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:itemBackground">@drawable/dropdown_background_dark</item>
        <item name="android:dropDownListViewStyle">@style/PopupMenuListViewStyle_Dark</item>
    </style>
    <style name="PopupMenuStyle_DarkBlue" parent="PopupMenuStyle">
        <item name="android:popupBackground">@drawable/dropdown_background_dark_blue</item>
        <item name="android:textColor">@color/black</item>
        <item name="android:itemBackground">@drawable/dropdown_background_dark_blue</item>
        <item name="android:dropDownListViewStyle">@style/PopupMenuListViewStyle_DarkBlue</item>
    </style>
    <style name="PopupMenuStyle_Neon" parent="PopupMenuStyle">
        <item name="android:popupBackground">@drawable/dropdown_background_neon</item>
        <item name="android:textColor">@color/black</item>
        <item name="android:itemBackground">@drawable/dropdown_background_neon</item>
        <item name="android:dropDownListViewStyle">@style/PopupMenuListViewStyle_Neon</item>
    </style>
    <style name="PopupMenuStyle_Ocean" parent="PopupMenuStyle">
        <item name="android:popupBackground">@drawable/dropdown_background_ocean</item>
        <item name="android:textColor">@color/black</item>
        <item name="android:itemBackground">@drawable/dropdown_background_ocean</item>
        <item name="android:dropDownListViewStyle">@style/PopupMenuListViewStyle_Ocean</item>
    </style>
    <style name="PopupMenuStyle_Snooker" parent="PopupMenuStyle">
        <item name="android:popupBackground">@drawable/dropdown_background_snooker</item>
        <item name="android:textColor">@color/black</item>
        <item name="android:itemBackground">@drawable/dropdown_background_snooker</item>
        <item name="android:dropDownListViewStyle">@style/PopupMenuListViewStyle_Snooker</item>
    </style>
    <style name="SnookerCardStyle" parent="Widget.MaterialComponents.CardView">
        <item name="cardBackgroundColor">@color/snooker_card_background_light</item>
        <item name="cardElevation">4dp</item>
        <item name="cardCornerRadius">8dp</item>
    </style>
    <style name="SnookerNegativeButtonStyle" parent="Widget.MaterialComponents.Button.TextButton.Dialog">
        <item name="android:textColor">@color/snooker_green_500</item>
    </style>
    <style name="SnookerPositiveButtonStyle" parent="Widget.MaterialComponents.Button.TextButton.Dialog">
        <item name="android:textColor">@color/snooker_red_500</item>
    </style>
    <style name="Theme.DieSnookerApp" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        <item name="colorPrimary">@color/purple_500</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/white</item>
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>

        
        <item name="android:popupMenuStyle">@style/PopupMenuStyle</item>
        <item name="popupMenuStyle">@style/PopupMenuStyle</item>
        <item name="android:itemTextAppearance">@style/PopupMenuItemStyle</item>
    </style>
    <style name="Theme.DieSnookerApp.Blue" parent="Theme.MaterialComponents.Light.NoActionBar">
        <item name="colorPrimary">@color/blue_500</item>
        <item name="colorPrimaryVariant">@color/blue_700</item>
        <item name="colorOnPrimary">@color/white</item>
        <item name="colorSecondary">@color/orange_500</item>
        <item name="colorSecondaryVariant">@color/orange_700</item>
        <item name="colorOnSecondary">@color/black</item>
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        <item name="android:windowBackground">@color/white</item>
        <item name="android:textColorPrimary">@color/black</item>
        <item name="colorSurface">@color/white</item>
        <item name="colorOnSurface">@color/black</item>

        
        <item name="cardBackgroundColor">@color/blue_card_background_light</item>
        <item name="materialCardViewStyle">@style/BlueCardStyle</item>

        
        <item name="android:popupMenuStyle">@style/PopupMenuStyle_Blue</item>
        <item name="popupMenuStyle">@style/PopupMenuStyle_Blue</item>
        <item name="android:itemTextAppearance">@style/PopupMenuItemStyle_Blue</item>
    </style>
    <style name="Theme.DieSnookerApp.Crimson" parent="Theme.MaterialComponents.Light.NoActionBar">
        <item name="colorPrimary">@color/crimson_medium</item>
        <item name="colorPrimaryVariant">@color/crimson_dark</item>
        <item name="colorOnPrimary">@color/white</item>
        <item name="colorSecondary">@color/crimson_light</item>
        <item name="colorSecondaryVariant">@color/crimson_lightest</item>
        <item name="colorOnSecondary">@color/white</item>
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        <item name="android:windowBackground">@color/crimson_card_background_light</item>
        <item name="android:textColorPrimary">@color/black</item>
        <item name="colorSurface">@color/crimson_card_background_light</item>
        <item name="colorOnSurface">@color/black</item>

        
        <item name="cardBackgroundColor">@color/crimson_card_background_light</item>
        <item name="materialCardViewStyle">@style/CrimsonCardStyle</item>

        
        <item name="android:popupMenuStyle">@style/PopupMenuStyle_Crimson</item>
        <item name="popupMenuStyle">@style/PopupMenuStyle_Crimson</item>
        <item name="android:itemTextAppearance">@style/PopupMenuItemStyle</item>
    </style>
    <style name="Theme.DieSnookerApp.DarkBlue" parent="Theme.MaterialComponents.Light.NoActionBar">
        <item name="colorPrimary">@color/dark_blue_500</item>
        <item name="colorPrimaryVariant">@color/dark_blue_700</item>
        <item name="colorOnPrimary">@color/white</item>
        <item name="colorSecondary">@color/cyan_500</item>
        <item name="colorSecondaryVariant">@color/cyan_700</item>
        <item name="colorOnSecondary">@color/black</item>
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        <item name="android:windowBackground">@color/white</item>
        <item name="android:textColorPrimary">@color/black</item>
        <item name="colorSurface">@color/white</item>
        <item name="colorOnSurface">@color/black</item>

        
        <item name="cardBackgroundColor">@color/dark_blue_card_background_light</item>
        <item name="materialCardViewStyle">@style/DarkBlueCardStyle</item>

        
        <item name="android:popupMenuStyle">@style/PopupMenuStyle_DarkBlue</item>
        <item name="popupMenuStyle">@style/PopupMenuStyle_DarkBlue</item>
        <item name="android:itemTextAppearance">@style/PopupMenuItemStyle_DarkBlue</item>
    </style>
    <style name="Theme.DieSnookerApp.Neon" parent="Theme.MaterialComponents.Light.NoActionBar">
        <item name="colorPrimary">@color/neon_purple_3</item>
        <item name="colorPrimaryVariant">@color/neon_purple_1</item>
        <item name="colorOnPrimary">@color/white</item>
        <item name="colorSecondary">@color/neon_pink_2</item>
        <item name="colorSecondaryVariant">@color/neon_pink_3</item>
        <item name="colorOnSecondary">@color/white</item>
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        <item name="android:windowBackground">@color/neon_card_background_light</item>
        <item name="android:textColorPrimary">@color/black</item>
        <item name="colorSurface">@color/neon_card_background_light</item>
        <item name="colorOnSurface">@color/black</item>

        
        <item name="cardBackgroundColor">@color/neon_card_background_light</item>
        <item name="materialCardViewStyle">@style/NeonCardStyle</item>

        
        <item name="android:popupMenuStyle">@style/PopupMenuStyle_Neon</item>
        <item name="popupMenuStyle">@style/PopupMenuStyle_Neon</item>
        <item name="android:itemTextAppearance">@style/PopupMenuItemStyle</item>
    </style>
    <style name="Theme.DieSnookerApp.Ocean" parent="Theme.MaterialComponents.Light.NoActionBar">
        <item name="colorPrimary">@color/ocean_medium</item>
        <item name="colorPrimaryVariant">@color/ocean_dark</item>
        <item name="colorOnPrimary">@color/white</item>
        <item name="colorSecondary">@color/ocean_light</item>
        <item name="colorSecondaryVariant">@color/ocean_lightest</item>
        <item name="colorOnSecondary">@color/black</item>
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        <item name="android:windowBackground">@color/ocean_lightest</item>
        <item name="android:textColorPrimary">@color/black</item>
        <item name="colorSurface">@color/ocean_lightest</item>
        <item name="colorOnSurface">@color/black</item>

        
        <item name="cardBackgroundColor">@color/ocean_card_background_light</item>
        <item name="materialCardViewStyle">@style/OceanCardStyle</item>

        
        <item name="android:popupMenuStyle">@style/PopupMenuStyle_Ocean</item>
        <item name="popupMenuStyle">@style/PopupMenuStyle_Ocean</item>
        <item name="android:itemTextAppearance">@style/PopupMenuItemStyle</item>
    </style>
    <style name="Theme.DieSnookerApp.Snooker" parent="Theme.MaterialComponents.Light.NoActionBar">
        <item name="colorPrimary">@color/snooker_red_500</item>
        <item name="colorPrimaryVariant">@color/snooker_red_700</item>
        <item name="colorOnPrimary">@color/white</item>
        <item name="colorSecondary">@color/snooker_green_500</item>
        <item name="colorSecondaryVariant">@color/snooker_green_700</item>
        <item name="colorOnSecondary">@color/white</item>
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        <item name="android:windowBackground">@color/snooker_green_200</item>
        <item name="android:textColorPrimary">@color/black</item>
        <item name="colorSurface">@color/snooker_green_200</item>
        <item name="colorOnSurface">@color/black</item>

        
        <item name="cardBackgroundColor">@color/snooker_card_background_light</item>
        <item name="materialCardViewStyle">@style/SnookerCardStyle</item>

        
        <item name="android:popupMenuStyle">@style/PopupMenuStyle_Snooker</item>
        <item name="popupMenuStyle">@style/PopupMenuStyle_Snooker</item>
        <item name="android:itemTextAppearance">@style/PopupMenuItemStyle_Snooker</item>
    </style>
</resources>