/ Header Record For PersistentHashMapValueStorage" !androidx.navigation.NavDirections" !androidx.navigation.NavDirections androidx.navigation.NavArgs" !androidx.navigation.NavDirections androidx.navigation.NavArgs androidx.navigation.NavArgs" !androidx.navigation.NavDirections" !androidx.navigation.NavDirections androidx.navigation.NavArgs" !androidx.navigation.NavDirections" !androidx.navigation.NavDirections" !androidx.navigation.NavDirections" !androidx.navigation.NavDirections" !androidx.navigation.NavDirections" !androidx.navigation.NavDirections androidx.navigation.NavArgs" !androidx.navigation.NavDirections androidx.navigation.NavArgs" !androidx.navigation.NavDirections) (androidx.appcompat.app.AppCompatActivity android.app.Application8 7com.atom.diesnookerapp.data.firebase.FirebaseRepository3 2com.atom.diesnookerapp.data.firebase.FirebaseModel3 2com.atom.diesnookerapp.data.firebase.FirebaseModel3 2com.atom.diesnookerapp.data.firebase.FirebaseModel3 2com.atom.diesnookerapp.data.firebase.FirebaseModel3 2com.atom.diesnookerapp.data.firebase.FirebaseModel3 2com.atom.diesnookerapp.data.firebase.FirebaseModel kotlin.Enum3 2com.atom.diesnookerapp.data.firebase.FirebaseModel kotlin.Enum8 7com.atom.diesnookerapp.data.firebase.FirebaseRepository androidx.work.CoroutineWorker8 7com.atom.diesnookerapp.data.firebase.FirebaseRepository8 7com.atom.diesnookerapp.data.firebase.FirebaseRepository8 7com.atom.diesnookerapp.data.firebase.FirebaseRepository8 7com.atom.diesnookerapp.data.firebase.FirebaseRepository androidx.fragment.app.Fragment androidx.fragment.app.Fragment androidx.fragment.app.Fragment androidx.fragment.app.Fragment androidx.fragment.app.Fragment androidx.fragment.app.Fragment androidx.fragment.app.Fragment androidx.fragment.app.Fragment kotlin.Enum2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder6 5com.atom.diesnookerapp.ui.aufgaben.TaskCompletionItem6 5com.atom.diesnookerapp.ui.aufgaben.TaskCompletionItem2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder5 4androidx.recyclerview.widget.RecyclerView.ViewHolder2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder androidx.fragment.app.Fragment2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder com.google.gson.JsonSerializer!  com.google.gson.JsonDeserializer androidx.fragment.app.Fragment androidx.fragment.app.Fragment androidx.fragment.app.Fragment androidx.fragment.app.Fragment androidx.fragment.app.Fragment) (androidx.recyclerview.widget.ListAdapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder3 2androidx.recyclerview.widget.DiffUtil.ItemCallback androidx.fragment.app.Fragment2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder androidx.fragment.app.Fragment2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder androidx.fragment.app.Fragment androidx.fragment.app.Fragment androidx.fragment.app.Fragment kotlin.Enum6 5com.github.mikephil.charting.formatter.ValueFormatter2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder androidx.fragment.app.Fragment androidx.fragment.app.Fragment androidx.fragment.app.Fragment2 1androidx.recyclerview.widget.RecyclerView.AdapterF Ecom.atom.diesnookerapp.ui.ergebniserfassung.MonthDateAdapter.DateItemF Ecom.atom.diesnookerapp.ui.ergebniserfassung.MonthDateAdapter.DateItem5 4androidx.recyclerview.widget.RecyclerView.ViewHolder5 4androidx.recyclerview.widget.RecyclerView.ViewHolder androidx.fragment.app.Fragment androidx.fragment.app.Fragment androidx.fragment.app.Fragment androidx.fragment.app.Fragment androidx.fragment.app.Fragment) (androidx.recyclerview.widget.ListAdapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder androidx.fragment.app.Fragment androidx.fragment.app.Fragment androidx.fragment.app.Fragment2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder androidx.fragment.app.Fragment androidx.fragment.app.Fragment kotlin.Enum kotlin.Enum androidx.fragment.app.Fragment kotlin.Enum6 5com.github.mikephil.charting.formatter.ValueFormatter3 2com.github.mikephil.charting.components.MarkerView androidx.fragment.app.Fragment2 1androidx.recyclerview.widget.RecyclerView.AdapterI Hcom.atom.diesnookerapp.ui.selbsteinschaetzung.HistoryAdapter.HistoryItemI Hcom.atom.diesnookerapp.ui.selbsteinschaetzung.HistoryAdapter.HistoryItem5 4androidx.recyclerview.widget.RecyclerView.ViewHolder5 4androidx.recyclerview.widget.RecyclerView.ViewHolder2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder androidx.fragment.app.Fragment androidx.fragment.app.Fragment2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder androidx.fragment.app.Fragment androidx.fragment.app.Fragment androidx.fragment.app.Fragment androidx.fragment.app.Fragment androidx.fragment.app.Fragment androidx.fragment.app.Fragment2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder androidx.fragment.app.Fragment2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder androidx.fragment.app.Fragment androidx.work.Worker2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder androidx.fragment.app.Fragment2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder androidx.fragment.app.Fragment androidx.work.CoroutineWorker+ *com.google.android.material.tabs.TabLayout android.widget.ArrayAdapter androidx.fragment.app.Fragment android.widget.ArrayAdapter) (androidx.recyclerview.widget.ListAdapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder androidx.fragment.app.Fragment androidx.fragment.app.Fragment androidx.fragment.app.Fragment androidx.fragment.app.Fragment androidx.fragment.app.Fragment androidx.fragment.app.Fragment androidx.fragment.app.Fragment androidx.fragment.app.Fragment androidx.fragment.app.Fragment androidx.fragment.app.Fragment2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder androidx.fragment.app.Fragment androidx.fragment.app.Fragment