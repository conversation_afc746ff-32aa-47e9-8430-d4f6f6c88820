package com.atom.diesnookerapp.ui.aufgaben

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.atom.diesnookerapp.R
import org.threeten.bp.LocalDate

class MentalFragment : Fragment() {
    private lateinit var recyclerView: RecyclerView
    private lateinit var pointsTextView: TextView
    private lateinit var titleTextView: TextView
    private lateinit var taskAdapter: TaskAdapter
    private lateinit var taskManager: TaskManager
    
    private val mentalTasks = listOf(
        Task(
            id = "mit_druck",
            title = "Mit Druck umgehen",
            description = "20min Video aufnehmen",
            frequency = TaskFrequency.WEEKLY,
            points = 1,
            category = "safeties"
        ),
        Task(
            id = "unterbewusstsein",
            title = "Unterbewusstsein stärken",
            description = "Jeden Tag Flowscript, Katastrophe und Vorbild (auf Weg zum Training) hören",
            frequency = TaskFrequency.DAILY,
            points = 3,
            category = "mental"
        ),
        Task(
            id = "flow",
            title = "Flow",
            description = "Zu Flowscript 10min genau Vorstellen wie sich alles anfühlt",
            frequency = TaskFrequency.DAILY,
            points = 1,
            category = "mental"
        ),
        Task(
            id = "positives_denken",
            title = "Positives Denken",
            description = "Immer dran erinnern",
            frequency = TaskFrequency.DAILY,
            points = 1,
            category = "mental"
        ),
        Task(
            id = "voll_da_sein",
            title = "Von Anfang an voll da sein",
            description = "Warm Up plan pro korrekter Durchführung",
            frequency = TaskFrequency.DAILY,
            points = 1,
            category = "mental"
        ),
        Task(
            id = "selbstbewusstsein",
            title = "Selbstbewusstsein",
            description = "Text eingeben",
            frequency = TaskFrequency.DAILY,
            points = 1,
            category = "mental"
        )
    )
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_task_list, container, false)
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        // Initialize TaskManager
        taskManager = TaskManager(requireContext())
        
        // Initialize views
        recyclerView = view.findViewById(R.id.tasksRecyclerView)
        pointsTextView = view.findViewById(R.id.pointsTextView)
        titleTextView = view.findViewById(R.id.categoryTitleTextView)
        
        // Set up RecyclerView
        recyclerView.layoutManager = LinearLayoutManager(requireContext())
        
        // Initialize tasks if needed
        taskManager.initializeTasksIfNeeded(mentalTasks)
        
        // Get tasks from storage
        val tasks = taskManager.getTasksByCategory("mental")
        
        // Set up adapter
        taskAdapter = TaskAdapter(
            tasks,
            onTaskCompleted = { task ->
                taskManager.markTaskCompleted(task.id, LocalDate.now())
                updatePointsDisplay()
            },
            onTaskUncompleted = { task ->
                taskManager.markTaskUncompleted(task.id, LocalDate.now())
                updatePointsDisplay()
            },
            getFrequencyText = { task ->
                when (task.frequency) {
                    TaskFrequency.DAILY -> "1x täglich"
                    TaskFrequency.WEEKLY -> when (task.id) {
                        "mit_druck" -> "4x pro Woche"
                        else -> "wöchentlich"
                    }
                    TaskFrequency.MONTHLY -> "monatlich"
                }
            },
            taskManager = taskManager  // Add this parameter
        )
        
        recyclerView.adapter = taskAdapter
        
        // Set title
        titleTextView.text = "Mental"
        
        // Update points display
        updatePointsDisplay()
    }
    
    private fun updatePointsDisplay() {
        val totalPoints = taskManager.getTotalPoints()
        pointsTextView.text = "Gesammelte Punkte: $totalPoints"
    }
}