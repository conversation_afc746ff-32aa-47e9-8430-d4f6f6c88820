<resources>
    <string name="app_name">Die Snooker App</string>

    <string name="save_button">Save</string>
    <string name="cancel_button">Cancel</string>
    <string name="edit_button">Edit</string>
    <string name="delete_button">Delete</string>
    <string name="manage_my_exercises_button">Manage My Exercises</string>
    <string name="add_exercise_button_label">Add Exercise</string>
    <string name="dialog_title_add_exercise">Add Exercise</string>
    <string name="dialog_title_edit_exercise">Edit Exercise</string>
    <string name="fragment_label_manage_exercises">Manage Exercises</string>
    <string name="dialog_title_confirmation">Confirmation</string>
    <string name="label_name">Name</string>
    <string name="label_category">Category</string>
    <string name="label_exercise_type">Exercise Type</string>
    <string name="exercise_type_normal">Normal</string>
    <string name="exercise_type_timeonly">Time Only</string>
    <string name="exercise_type_splits">Splits</string>
    <string name="exercise_type_stellungsspiel">Positional Play</string>
    <string name="label_description">Description</string>
    <string name="toast_no_specific_view_for_category">No specific view for category: %s</string>
    <string name="default_exercises_added_toast">Default exercises added to your library.</string>
    <string name="exercise_saved_toast">Exercise saved.</string>
    <string name="exercise_deleted_toast">Exercise deleted.</string>
    <string name="error_saving_exercise_toast">Error saving exercise.</string>
    <string name="error_deleting_exercise_toast">Error deleting exercise.</string>
    <string name="error_loading_exercises_toast">Error loading exercises.</string>
    <string name="no_exercises_found_toast">No exercises found.</string>
    <string name="please_log_in_to_manage_exercises_toast">Please log in to manage exercises.</string>
    <string name="please_log_in_to_load_categories_toast">Please log in to load categories.</string>
    <string name="no_categories_found_toast">No categories found. Add exercises to create categories.</string>
    <string name="please_log_in_to_load_exercises_toast">Please log in to load exercises.</string>
    <string name="no_exercises_in_category_toast">No exercises found for %s.</string>
    <string name="confirm_delete_exercise_message">Delete exercise \'%s\'?</string>
    <string name="input_error_name_required">Name is required.</string>
    <string name="input_error_category_required">Category is required.</string>
    <string name="label_new_category">New Category Name</string>
    <string name="yes">Yes</string>
    <string name="no">No</string>

    <string name="action_create_new_category">+ Create New Category</string>
    <string name="error_user_id_not_found">Error: User ID not found</string>
    <string name="error_user_id_not_found_info">Could not retrieve user information.</string>
    <string name="default_exercises_partial_success_toast">Some default exercises added, some failed.</string>
    <string name="default_exercises_failed_toast">Failed to add default exercises.</string>
    <string name="error_loading_categories_toast">Failed to load categories.</string>
    <string name="exercise_not_deletable_toast">is not deletable.</string>
    <string name="dialog_title_delete_exercise">Delete Exercise</string>
    <string name="no_exercises_in_category_generic">No exercises found in this category.</string>
    <string name="unknown_category">Unknown Category</string>
    <string name="category_not_specified_toast">Category not specified.</string>

    <string name="label_empty_exercise_list">No custom exercises yet. Tap \'+\' to add one.</string>
    <string name="toast_please_select_exercise">Please select at least one exercise.</string>
    <string name="confirm_remove_exercise_from_plan_message">Remove exercise \'%s\' from the training plan?</string>
    <string name="debug_menu_title">Debug Menu</string>
    <string-array name="debug_menu_options">
        <item>Test Notification (Normal)</item>
        <item>Force Notification (Add Test Exercise)</item>
        <item>Reschedule Weekly Reminder</item>
    </string-array>
    <string name="toast_plan_reset_archived">Training plan was reset and archived.</string>
    <string name="toast_notification_sent_success">Notification sent successfully!</string>
    <string name="toast_notification_sent_none">No notification sent - no incomplete exercises.</string>
    <string name="toast_notification_forced">Forced notification with test exercise.</string>
    <string name="toast_reminder_rescheduled">Weekly reminder rescheduled.</string>
    <string name="title_category_exercises_generic">Category Exercises</string>
</resources>