package com.atom.diesnookerapp.ui.ergebniserfassung

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.core.os.bundleOf
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.atom.diesnookerapp.R
import com.atom.diesnookerapp.data.firebase.FirebaseAuthManager
import com.atom.diesnookerapp.ui.trainingsplan.ExerciseItem
import com.atom.diesnookerapp.ui.trainingsplan.TrainingsplanManager
import com.google.android.material.button.MaterialButton
import com.google.android.material.textview.MaterialTextView
import kotlinx.coroutines.launch

class SafetiesFragment : Fragment() {

    private lateinit var trainingsplanManager: TrainingsplanManager
    private lateinit var firebaseAuthManager: FirebaseAuthManager
    private lateinit var safetiesAdapter: BreakbuildingAdapter // Using the refactored BreakbuildingAdapter
    private var exerciseList = mutableListOf<ExerciseItem>()
    private var categoryName: String? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            categoryName = it.getString("categoryName")
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_safeties, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        trainingsplanManager = TrainingsplanManager(requireContext())
        firebaseAuthManager = FirebaseAuthManager()

        val recyclerView = view.findViewById<RecyclerView>(R.id.recyclerView)
        recyclerView.layoutManager = LinearLayoutManager(requireContext())
        val exercisePreferences = ExercisePreferences(requireContext()) // Still needed by BreakbuildingAdapter

        safetiesAdapter = BreakbuildingAdapter(exerciseList, exercisePreferences) { exerciseItem ->
            findNavController().navigate(
                R.id.action_safetiesFragment_to_exerciseDetailFragment, // Ensure this action ID is correct
                bundleOf(
                    "exerciseId" to exerciseItem.id,
                    "exerciseTitle" to exerciseItem.name // Name from ExerciseItem
                )
            )
        }
        recyclerView.adapter = safetiesAdapter

        // Attempt to set fragment title - comment out if fragmentTitleTextView doesn't exist
        // view.findViewById<MaterialTextView>(R.id.fragmentTitleTextView)?.text = categoryName ?: "Safeties Exercises"
        // Assuming R.id.fragmentTitleTextView might not exist in this layout, so commenting out:
        // (activity as? AppCompatActivity)?.supportActionBar?.title = categoryName ?: "Safeties Exercises"


        loadExercises()
    }

    private fun loadExercises() {
        if (categoryName == null) {
            Toast.makeText(context, "Category not specified for Safeties.", Toast.LENGTH_LONG).show()
            return
        }

        if (!firebaseAuthManager.isLoggedIn()) {
            Toast.makeText(context, "Please log in to view exercises.", Toast.LENGTH_SHORT).show()
            exerciseList.clear()
            safetiesAdapter.notifyDataSetChanged()
            return
        }

        viewLifecycleOwner.lifecycleScope.launch {
            try {
                val fetchedExercises = trainingsplanManager.getExercisesForCategory(categoryName!!)
                exerciseList.clear()
                if (fetchedExercises.isNotEmpty()) {
                    exerciseList.addAll(fetchedExercises)
                } else {
                    Toast.makeText(context, "No exercises found for $categoryName.", Toast.LENGTH_SHORT).show()
                }
                safetiesAdapter.notifyDataSetChanged()
            } catch (e: Exception) {
                Toast.makeText(context, "Failed to load safeties exercises: ${e.message}", Toast.LENGTH_LONG).show()
                exerciseList.clear()
                safetiesAdapter.notifyDataSetChanged()
            }
        }
    }
}