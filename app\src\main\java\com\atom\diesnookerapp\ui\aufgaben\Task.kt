package com.atom.diesnookerapp.ui.aufgaben

import org.threeten.bp.LocalDate
import org.threeten.bp.DayOfWeek
import org.threeten.bp.temporal.WeekFields
import java.util.Locale

data class Task(
    val id: String,
    val title: String,
    val description: String,
    val frequency: TaskFrequency,
    val points: Int,
    val category: String,
    val completions: MutableList<LocalDate>? = mutableListOf(),
    // Add a field to store custom frequency times (e.g., 2x per week)
    val frequencyTimes: Int = 1,
    // Map to store custom points for specific dates
    val customPoints: MutableMap<String, Int> = mutableMapOf()
)

enum class TaskFrequency {
    DAILY,
    WEEKLY,
    MONTHLY
}

data class TaskFrequencyDetails(
    val frequency: TaskFrequency,
    val times: Int,
    val displayText: String
)

// Extension function to get the week of year for a LocalDate
// Update the weekOfYear function to use Monday as the first day of the week
fun LocalDate.weekOfYear(): Int {
    return this.get(WeekFields.of(Locale.getDefault()).weekOfWeekBasedYear())
}

// Add a new function to get the week key with Monday as the first day
fun LocalDate.yearWeekKeyMonday(): String {
    val weekFields = WeekFields.of(DayOfWeek.MONDAY, 1)
    val weekOfYear = this.get(weekFields.weekOfWeekBasedYear())
    return "${this.year}-W$weekOfYear"
}

// Extension function to get the year and week as a string
fun LocalDate?.yearWeekKey(): String {
    return if (this == null) {
        "unknown-week"
    } else {
        "${this.year}-W${this.weekOfYear()}"
    }
}

fun LocalDate?.yearMonthKey(): String {
    return if (this == null) {
        "unknown-month"
    } else {
        "${this.year}-${this.monthValue}"
    }
}